module.exports = {

"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/tty [external] (tty, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tty", () => require("tty"));

module.exports = mod;
}}),
"[externals]/os [external] (os, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[project]/src/lib/api.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "adminAPI": ()=>adminAPI,
    "authAPI": ()=>authAPI,
    "default": ()=>__TURBOPACK__default__export__,
    "deliveryAPI": ()=>deliveryAPI,
    "foodAPI": ()=>foodAPI,
    "notificationsAPI": ()=>notificationsAPI,
    "offersAPI": ()=>offersAPI,
    "orderAPI": ()=>orderAPI,
    "paymentAPI": ()=>paymentAPI,
    "restaurantAPI": ()=>restaurantAPI,
    "userAPI": ()=>userAPI,
    "vendorAPI": ()=>vendorAPI
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hot-toast/dist/index.mjs [app-ssr] (ecmascript)");
;
;
// Create axios instance
const api = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].create({
    baseURL: ("TURBOPACK compile-time value", "http://localhost:5000/api") || 'http://localhost:5000/api',
    timeout: 10000,
    withCredentials: true,
    headers: {
        'Content-Type': 'application/json'
    }
});
// Request interceptor to add auth token
api.interceptors.request.use((config)=>{
    const token = localStorage.getItem('token');
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
}, (error)=>{
    return Promise.reject(error);
});
// Response interceptor for error handling
api.interceptors.response.use((response)=>{
    return response;
}, async (error)=>{
    const originalRequest = error.config;
    // Only try to refresh if we have a token and this is an auth-related 401
    if (error.response?.status === 401 && !originalRequest._retry && localStorage.getItem('token')) {
        originalRequest._retry = true;
        try {
            // Try to refresh token
            const response = await api.post('/auth/refresh');
            const { token } = response.data.data;
            localStorage.setItem('token', token);
            originalRequest.headers.Authorization = `Bearer ${token}`;
            // Update auth store with new token
            if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
            ;
            return api(originalRequest);
        } catch (refreshError) {
            // Refresh failed, clear auth data
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            // Only redirect to login if not already on auth pages and this was a protected route
            if (!window.location.pathname.startsWith('/auth') && originalRequest.headers.Authorization) {
                window.location.href = '/auth/login';
            }
            return Promise.reject(refreshError);
        }
    }
    // Show error toast for non-401 errors
    if (error.response?.data?.message) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error(error.response.data.message);
    } else {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error('Something went wrong. Please try again.');
    }
    return Promise.reject(error);
});
const authAPI = {
    register: (data)=>api.post('/auth/register', data),
    login: (data)=>api.post('/auth/login', data),
    logout: ()=>api.post('/auth/logout'),
    getProfile: ()=>api.get('/auth/me'),
    forgotPassword: (email)=>api.post('/auth/forgot-password', {
            email
        }),
    resetPassword: (data)=>api.put('/auth/reset-password', data),
    refreshToken: ()=>api.post('/auth/refresh')
};
const userAPI = {
    getProfile: ()=>api.get('/users/profile'),
    updateProfile: (data)=>api.put('/users/profile', data),
    addAddress: (data)=>api.post('/users/addresses', data),
    updateAddress: (id, data)=>api.put(`/users/addresses/${id}`, data),
    deleteAddress: (id)=>api.delete(`/users/addresses/${id}`),
    getFavorites: ()=>api.get('/users/favorites'),
    addToFavorites: (restaurantId)=>api.post(`/users/favorites/${restaurantId}`),
    removeFromFavorites: (restaurantId)=>api.delete(`/users/favorites/${restaurantId}`),
    getOrders: (params)=>api.get('/users/orders', {
            params
        }),
    getStats: ()=>api.get('/users/stats')
};
const restaurantAPI = {
    getAll: (params)=>api.get('/restaurants', {
            params
        }),
    getById: (id)=>api.get(`/restaurants/${id}`),
    getNearby: (lat, lng, params)=>api.get(`/restaurants/nearby/${lat}/${lng}`, {
            params
        }),
    create: (data)=>api.post('/restaurants', data),
    update: (id, data)=>api.put(`/restaurants/${id}`, data),
    delete: (id)=>api.delete(`/restaurants/${id}`),
    toggleStatus: (id)=>api.patch(`/restaurants/${id}/toggle-status`)
};
const foodAPI = {
    getAll: (params)=>api.get('/foods', {
            params
        }),
    getById: (id)=>api.get(`/foods/${id}`),
    getByRestaurant: (restaurantId, params)=>api.get(`/foods/restaurant/${restaurantId}`, {
            params
        }),
    create: (data)=>api.post('/foods', data),
    update: (id, data)=>api.put(`/foods/${id}`, data),
    delete: (id)=>api.delete(`/foods/${id}`),
    toggleAvailability: (id)=>api.patch(`/foods/${id}/toggle-availability`)
};
const orderAPI = {
    create: (data)=>api.post('/orders', data),
    getAll: (params)=>api.get('/orders', {
            params
        }),
    getById: (id)=>api.get(`/orders/${id}`),
    getUserOrders: (params)=>api.get('/users/orders', {
            params
        }),
    updateStatus: (id, data)=>api.patch(`/orders/${id}/status`, data),
    cancel: (id, reason)=>api.patch(`/orders/${id}/cancel`, {
            reason
        })
};
const paymentAPI = {
    process: (data)=>api.post('/payments/process', data),
    getMethods: ()=>api.get('/payments/methods'),
    getHistory: (params)=>api.get('/payments/history', {
            params
        }),
    refund: (data)=>api.post('/payments/refund', data)
};
const adminAPI = {
    getStats: (params)=>api.get('/admin/stats', {
            params
        }),
    getUsers: (params)=>api.get('/admin/users', {
            params
        }),
    getRestaurants: (params)=>api.get('/admin/restaurants', {
            params
        }),
    getOrders: (params)=>api.get('/admin/orders', {
            params
        }),
    updateRestaurantStatus: (id, status)=>api.patch(`/admin/restaurants/${id}/status`, {
            status
        }),
    toggleUserStatus: (id)=>api.patch(`/admin/users/${id}/toggle-status`),
    getAnalytics: (period)=>api.get('/admin/analytics', {
            params: {
                period
            }
        }),
    getPayments: (params)=>api.get('/admin/payments', {
            params
        }),
    getReviews: (params)=>api.get('/admin/reviews', {
            params
        }),
    getReports: (type, params)=>api.get(`/admin/reports/${type}`, {
            params
        }),
    getSettings: ()=>api.get('/admin/settings'),
    updateSettings: (data)=>api.put('/admin/settings', data)
};
const vendorAPI = {
    getDashboard: (params)=>api.get('/vendors/dashboard', {
            params
        }),
    getAnalytics: (period)=>api.get('/vendors/analytics', {
            params: {
                period
            }
        }),
    getOrders: (params)=>api.get('/vendors/orders', {
            params
        }),
    getRecentOrders: (params)=>api.get('/vendors/orders/recent', {
            params
        }),
    getFoods: (params)=>api.get('/vendors/foods', {
            params
        }),
    getRestaurant: ()=>api.get('/vendors/restaurant'),
    createRestaurant: (data)=>api.post('/vendors/restaurant', data),
    updateRestaurant: (data)=>api.put('/vendors/restaurant', data),
    createFood: (data)=>api.post('/vendors/foods', data),
    updateFood: (id, data)=>api.put(`/vendors/foods/${id}`, data),
    deleteFood: (id)=>api.delete(`/vendors/foods/${id}`),
    updateOrderStatus: (id, status)=>api.patch(`/vendors/orders/${id}/status`, {
            status
        }),
    getCustomers: (params)=>api.get('/vendors/customers', {
            params
        }),
    getReviews: (params)=>api.get('/vendors/reviews', {
            params
        }),
    getNotifications: (params)=>api.get('/vendors/notifications', {
            params
        })
};
const deliveryAPI = {
    getDashboard: ()=>api.get('/delivery/dashboard'),
    getOrders: (params)=>api.get('/delivery/orders', {
            params
        }),
    acceptOrder: (id)=>api.patch(`/delivery/orders/${id}/accept`),
    pickupOrder: (id)=>api.patch(`/delivery/orders/${id}/pickup`),
    deliverOrder: (id)=>api.patch(`/delivery/orders/${id}/deliver`),
    getEarnings: (params)=>api.get('/delivery/earnings', {
            params
        }),
    getHistory: (params)=>api.get('/delivery/history', {
            params
        }),
    updateLocation: (data)=>api.patch('/delivery/location', data),
    updateStatus: (status)=>api.patch('/delivery/status', {
            status
        })
};
const offersAPI = {
    getOffers: (params)=>api.get('/offers', {
            params
        }),
    getOfferById: (id)=>api.get(`/offers/${id}`),
    applyOffer: (code, orderData)=>api.post('/offers/apply', {
            code,
            ...orderData
        }),
    validateOffer: (code)=>api.post('/offers/validate', {
            code
        })
};
const notificationsAPI = {
    getNotifications: (params)=>api.get('/notifications', {
            params
        }),
    getUnreadCount: ()=>api.get('/notifications/unread-count'),
    markAsRead: (id)=>api.patch(`/notifications/${id}/read`),
    markAllAsRead: ()=>api.patch('/notifications/mark-all-read'),
    deleteNotification: (id)=>api.delete(`/notifications/${id}`)
};
const __TURBOPACK__default__export__ = api;
}),
"[project]/src/store/useAuthStore.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/react.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/middleware.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hot-toast/dist/index.mjs [app-ssr] (ecmascript)");
;
;
;
;
const useAuthStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["create"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["persist"])((set, get)=>({
        user: null,
        token: null,
        isLoading: false,
        isAuthenticated: false,
        // Login action
        login: async (credentials)=>{
            set({
                isLoading: true
            });
            try {
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["authAPI"].login(credentials);
                const { user, token } = response.data.data;
                localStorage.setItem('token', token);
                localStorage.setItem('user', JSON.stringify(user));
                set({
                    user,
                    token,
                    isAuthenticated: true,
                    isLoading: false
                });
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].success('Login successful!');
                return {
                    success: true,
                    user
                };
            } catch (error) {
                set({
                    isLoading: false
                });
                return {
                    success: false,
                    error: error.response?.data?.message || 'Login failed'
                };
            }
        },
        // Register action
        register: async (userData)=>{
            set({
                isLoading: true
            });
            try {
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["authAPI"].register(userData);
                const { user, token } = response.data.data;
                localStorage.setItem('token', token);
                localStorage.setItem('user', JSON.stringify(user));
                set({
                    user,
                    token,
                    isAuthenticated: true,
                    isLoading: false
                });
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].success('Registration successful!');
                return {
                    success: true
                };
            } catch (error) {
                set({
                    isLoading: false
                });
                return {
                    success: false,
                    error: error.response?.data?.message || 'Registration failed'
                };
            }
        },
        // Logout action
        logout: async ()=>{
            try {
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["authAPI"].logout();
            } catch (error) {
                console.error('Logout error:', error);
            } finally{
                localStorage.removeItem('token');
                localStorage.removeItem('user');
                set({
                    user: null,
                    token: null,
                    isAuthenticated: false
                });
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].success('Logged out successfully');
            }
        },
        // Get current user profile
        getProfile: async ()=>{
            try {
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["authAPI"].getProfile();
                const user = response.data.data;
                set({
                    user
                });
                return user;
            } catch (error) {
                console.error('Get profile error:', error);
                return null;
            }
        },
        // Update user profile
        updateProfile: async (userData)=>{
            try {
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["authAPI"].updateProfile(userData);
                const user = response.data.data;
                set({
                    user
                });
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].success('Profile updated successfully');
                return {
                    success: true,
                    user
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.response?.data?.message || 'Update failed'
                };
            }
        },
        // Initialize auth state
        initialize: async ()=>{
            set({
                isLoading: true
            });
            try {
                const token = localStorage.getItem('token');
                if (token) {
                    set({
                        token,
                        isAuthenticated: true
                    });
                    const user = await get().getProfile();
                    if (user) {
                        set({
                            isLoading: false
                        });
                    } else {
                        // Profile fetch failed, clear auth
                        get().clearAuth();
                        set({
                            isLoading: false
                        });
                    }
                } else {
                    set({
                        isLoading: false
                    });
                }
            } catch (error) {
                console.error('Auth initialization error:', error);
                get().clearAuth();
                set({
                    isLoading: false
                });
            }
        },
        // Set token (for token refresh)
        setToken: (token)=>{
            set({
                token
            });
            localStorage.setItem('token', token);
        },
        // Initialize auth state from localStorage
        initializeAuth: ()=>{
            const token = localStorage.getItem('token');
            const storedUser = localStorage.getItem('user');
            if (token && storedUser) {
                try {
                    const user = JSON.parse(storedUser);
                    set({
                        user,
                        token,
                        isAuthenticated: true,
                        isLoading: false
                    });
                } catch (error) {
                    console.error('Error parsing stored user:', error);
                    get().clearAuth();
                }
            } else {
                set({
                    isLoading: false
                });
            }
        },
        // Clear auth state
        clearAuth: ()=>{
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            set({
                user: null,
                token: null,
                isAuthenticated: false
            });
        }
    }), {
    name: 'auth-storage',
    partialize: (state)=>({
            user: state.user,
            token: state.token,
            isAuthenticated: state.isAuthenticated
        })
}));
const __TURBOPACK__default__export__ = useAuthStore;
}),
"[externals]/child_process [external] (child_process, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("child_process", () => require("child_process"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/net [external] (net, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}}),
"[externals]/tls [external] (tls, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tls", () => require("tls"));

module.exports = mod;
}}),
"[project]/src/hooks/useSocket.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$socket$2e$io$2d$client$2f$build$2f$esm$2d$debug$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/socket.io-client/build/esm-debug/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$socket$2e$io$2d$client$2f$build$2f$esm$2d$debug$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/socket.io-client/build/esm-debug/index.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hot-toast/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$useAuthStore$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/useAuthStore.js [app-ssr] (ecmascript)");
;
;
;
;
const useSocket = ()=>{
    const socketRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const { user, isAuthenticated } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$useAuthStore$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!isAuthenticated || !user) return;
        // Initialize socket connection
        socketRef.current = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$socket$2e$io$2d$client$2f$build$2f$esm$2d$debug$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["io"])(("TURBOPACK compile-time value", "http://localhost:5000/api") || 'http://localhost:5000', {
            withCredentials: true,
            transports: [
                'websocket',
                'polling'
            ]
        });
        const socket = socketRef.current;
        // Connection events
        socket.on('connect', ()=>{
            console.log('Connected to server');
            // Join user-specific room
            socket.emit('join-user-room', user._id);
            // Join restaurant room if user is a vendor
            if (user.role === 'vendor' && user.restaurant) {
                socket.emit('join-restaurant-room', user.restaurant._id);
            }
        });
        socket.on('disconnect', ()=>{
            console.log('Disconnected from server');
        });
        // Order-related events for customers
        if (user.role === 'customer') {
            socket.on('order-status-changed', (data)=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].success(`Order ${data.status}: ${data.message}`);
            // You can dispatch to a global state or trigger a refetch here
            });
            socket.on('order-placed', (data)=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].success('Order placed successfully!');
            });
        }
        // Restaurant-related events for vendors
        if (user.role === 'vendor') {
            socket.on('new-order-received', (orderData)=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].success('New order received!');
                // Play notification sound
                playNotificationSound();
            });
            socket.on('payment-received', (data)=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].success(`Payment received: ₹${data.amount}`);
            });
            socket.on('order-cancelled', (data)=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error(`Order cancelled: ${data.reason}`);
            });
        }
        // Admin events
        if (user.role === 'admin') {
            socket.on('new-restaurant-registration', (data)=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].info('New restaurant registration pending approval');
            });
            socket.on('new-user-registration', (data)=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].info('New user registered');
            });
        }
        // Cleanup on unmount
        return ()=>{
            if (socket) {
                socket.disconnect();
            }
        };
    }, [
        isAuthenticated,
        user
    ]);
    // Helper function to play notification sound
    const playNotificationSound = ()=>{
        try {
            const audio = new Audio('/sounds/notification.mp3');
            audio.play().catch(console.error);
        } catch (error) {
            console.error('Error playing notification sound:', error);
        }
    };
    // Emit order status update (for vendors)
    const updateOrderStatus = (orderData)=>{
        if (socketRef.current) {
            socketRef.current.emit('order-status-update', orderData);
        }
    };
    // Emit new order (for customers)
    const emitNewOrder = (orderData)=>{
        if (socketRef.current) {
            socketRef.current.emit('new-order', orderData);
        }
    };
    return {
        socket: socketRef.current,
        updateOrderStatus,
        emitNewOrder,
        isConnected: socketRef.current?.connected || false
    };
};
const __TURBOPACK__default__export__ = useSocket;
}),
"[project]/src/app/providers.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "Providers": ()=>Providers
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$core$2f$build$2f$modern$2f$queryClient$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/query-core/build/modern/queryClient.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$useAuthStore$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/useAuthStore.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useSocket$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/useSocket.js [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
function Providers({ children }) {
    const [queryClient] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(()=>new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$core$2f$build$2f$modern$2f$queryClient$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QueryClient"]({
            defaultOptions: {
                queries: {
                    staleTime: 60 * 1000,
                    retry: 1,
                    refetchOnWindowFocus: false
                }
            }
        }));
    const initializeAuth = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$useAuthStore$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])((state)=>state.initializeAuth);
    // Initialize auth state on app load
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        // Make auth store globally accessible for token refresh
        if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
        ;
        initializeAuth();
    }, [
        initializeAuth
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QueryClientProvider"], {
        client: queryClient,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(SocketProvider, {
            children: children
        }, void 0, false, {
            fileName: "[project]/src/app/providers.tsx",
            lineNumber: 32,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/providers.tsx",
        lineNumber: 31,
        columnNumber: 5
    }, this);
}
// Socket provider component
function SocketProvider({ children }) {
    // Initialize socket connection
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useSocket$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
        children: children
    }, void 0, false);
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__1c6287a8._.js.map