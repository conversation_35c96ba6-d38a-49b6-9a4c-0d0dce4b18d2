{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/project%20food-order%206k/food-ordering-system-modern/frontend/src/app/vendor/reviews/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { useQuery } from '@tanstack/react-query';\nimport { \n  MessageCircle, \n  Star, \n  Filter,\n  Search,\n  ThumbsUp,\n  ThumbsDown,\n  Calendar,\n  User\n} from 'lucide-react';\nimport VendorLayout from '@/components/VendorLayout';\nimport { vendorAPI } from '@/lib/api';\n\nconst VendorReviews = () => {\n  const [filterRating, setFilterRating] = useState('all');\n  const [searchTerm, setSearchTerm] = useState('');\n\n  // Fetch reviews data\n  const { data: reviewsData, isLoading } = useQuery({\n    queryKey: ['vendor-reviews', filterRating],\n    queryFn: () => vendorAPI.getReviews({ rating: filterRating }),\n  });\n\n  // Ensure reviews is always an array\n  const reviews = Array.isArray(reviewsData?.data) ? reviewsData.data :\n                  Array.isArray(reviewsData) ? reviewsData : [];\n\n\n  const ratingFilters = [\n    { value: 'all', label: 'All Reviews' },\n    { value: '5', label: '5 Stars' },\n    { value: '4', label: '4 Stars' },\n    { value: '3', label: '3 Stars' },\n    { value: '2', label: '2 Stars' },\n    { value: '1', label: '1 Star' },\n  ];\n\n  const filteredReviews = Array.isArray(reviews) ? reviews.filter(review => {\n    const matchesRating = filterRating === 'all' || review.rating.toString() === filterRating;\n    const matchesSearch = review.comment?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         review.customer?.name?.toLowerCase().includes(searchTerm.toLowerCase());\n    return matchesRating && matchesSearch;\n  }) : [];\n\n  const totalReviews = Array.isArray(reviews) ? reviews.length : 0;\n  const averageRating = totalReviews > 0\n    ? (reviews.reduce((sum, review) => sum + (review.rating || 0), 0) / totalReviews).toFixed(1)\n    : '0.0';\n\n  const renderStars = (rating) => {\n    return [...Array(5)].map((_, index) => (\n      <Star\n        key={index}\n        className={`w-4 h-4 ${\n          index < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'\n        }`}\n      />\n    ));\n  };\n\n  const handleReplyToReview = (reviewId) => {\n    // Handle reply functionality\n    console.log('Reply to review:', reviewId);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 flex\">\n      <VendorSidebar />\n      \n      <div className=\"flex-1 p-8\">\n        <div className=\"max-w-6xl mx-auto\">\n          {/* Header */}\n          <div className=\"flex items-center justify-between mb-8\">\n            <div className=\"flex items-center gap-3\">\n              <div className=\"w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center\">\n                <MessageCircle className=\"w-5 h-5 text-orange-600\" />\n              </div>\n              <div>\n                <h1 className=\"text-2xl font-bold text-gray-900\">Reviews & Ratings</h1>\n                <p className=\"text-gray-600\">Manage customer feedback and responses</p>\n              </div>\n            </div>\n          </div>\n\n          {/* Stats Overview */}\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\">\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6 }}\n              className=\"bg-white rounded-2xl p-6 shadow-md\"\n            >\n              <div className=\"flex items-center gap-3 mb-2\">\n                <Star className=\"w-6 h-6 text-yellow-400 fill-current\" />\n                <h3 className=\"text-lg font-semibold text-gray-900\">Average Rating</h3>\n              </div>\n              <p className=\"text-3xl font-bold text-gray-900\">{averageRating.toFixed(1)}</p>\n              <p className=\"text-gray-600\">Based on {totalReviews} reviews</p>\n            </motion.div>\n\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.1 }}\n              className=\"bg-white rounded-2xl p-6 shadow-md\"\n            >\n              <div className=\"flex items-center gap-3 mb-2\">\n                <ThumbsUp className=\"w-6 h-6 text-green-500\" />\n                <h3 className=\"text-lg font-semibold text-gray-900\">Positive Reviews</h3>\n              </div>\n              <p className=\"text-3xl font-bold text-gray-900\">\n                {Array.isArray(reviews) ? reviews.filter(r => r.rating >= 4).length : 0}\n              </p>\n              <p className=\"text-gray-600\">4+ star ratings</p>\n            </motion.div>\n\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.2 }}\n              className=\"bg-white rounded-2xl p-6 shadow-md\"\n            >\n              <div className=\"flex items-center gap-3 mb-2\">\n                <MessageCircle className=\"w-6 h-6 text-blue-500\" />\n                <h3 className=\"text-lg font-semibold text-gray-900\">Response Rate</h3>\n              </div>\n              <p className=\"text-3xl font-bold text-gray-900\">\n                {totalReviews > 0 && Array.isArray(reviews) ? Math.round((reviews.filter(r => r.response).length / totalReviews) * 100) : 0}%\n              </p>\n              <p className=\"text-gray-600\">Reviews responded to</p>\n            </motion.div>\n          </div>\n\n          {/* Filters and Search */}\n          <div className=\"bg-white rounded-2xl p-6 shadow-md mb-6\">\n            <div className=\"flex flex-col md:flex-row gap-4\">\n              {/* Search */}\n              <div className=\"flex-1\">\n                <div className=\"relative\">\n                  <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\" />\n                  <input\n                    type=\"text\"\n                    placeholder=\"Search reviews...\"\n                    value={searchTerm}\n                    onChange={(e) => setSearchTerm(e.target.value)}\n                    className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent\"\n                  />\n                </div>\n              </div>\n\n              {/* Rating Filter */}\n              <div className=\"flex gap-2\">\n                {ratingFilters.map((filter) => (\n                  <button\n                    key={filter.value}\n                    onClick={() => setFilterRating(filter.value)}\n                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${\n                      filterRating === filter.value\n                        ? 'bg-orange-500 text-white'\n                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'\n                    }`}\n                  >\n                    {filter.label}\n                  </button>\n                ))}\n              </div>\n            </div>\n          </div>\n\n          {/* Reviews List */}\n          <div className=\"space-y-6\">\n            {filteredReviews.map((review, index) => (\n              <motion.div\n                key={review.id}\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                className=\"bg-white rounded-2xl p-6 shadow-md\"\n              >\n                {/* Review Header */}\n                <div className=\"flex items-start justify-between mb-4\">\n                  <div className=\"flex items-center gap-3\">\n                    <div className=\"w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center\">\n                      <User className=\"w-5 h-5 text-gray-500\" />\n                    </div>\n                    <div>\n                      <h4 className=\"font-semibold text-gray-900\">{review.customer.name}</h4>\n                      <div className=\"flex items-center gap-2\">\n                        <div className=\"flex\">{renderStars(review.rating)}</div>\n                        <span className=\"text-sm text-gray-500\">•</span>\n                        <span className=\"text-sm text-gray-500\">{review.date}</span>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"flex items-center gap-2 text-sm text-gray-500\">\n                    <ThumbsUp className=\"w-4 h-4\" />\n                    <span>{review.helpful}</span>\n                  </div>\n                </div>\n\n                {/* Review Content */}\n                <p className=\"text-gray-700 mb-4\">{review.comment}</p>\n\n                {/* Order Items */}\n                <div className=\"mb-4\">\n                  <p className=\"text-sm text-gray-500 mb-2\">Items ordered:</p>\n                  <div className=\"flex flex-wrap gap-2\">\n                    {review.orderItems.map((item, idx) => (\n                      <span\n                        key={idx}\n                        className=\"px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm\"\n                      >\n                        {item}\n                      </span>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Response */}\n                {review.response ? (\n                  <div className=\"bg-orange-50 border-l-4 border-orange-500 p-4 mb-4\">\n                    <p className=\"text-sm font-medium text-orange-800 mb-1\">Your Response:</p>\n                    <p className=\"text-orange-700\">{review.response}</p>\n                  </div>\n                ) : (\n                  <button\n                    onClick={() => handleReplyToReview(review.id)}\n                    className=\"text-orange-600 hover:text-orange-700 text-sm font-medium\"\n                  >\n                    Reply to this review\n                  </button>\n                )}\n              </motion.div>\n            ))}\n          </div>\n\n          {filteredReviews.length === 0 && (\n            <div className=\"text-center py-12\">\n              <MessageCircle className=\"w-16 h-16 text-gray-300 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">No reviews found</h3>\n              <p className=\"text-gray-600\">Try adjusting your filters or search terms.</p>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default VendorReviews;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAWA;;;AAhBA;;;;;;AAkBA,MAAM,gBAAgB;;IACpB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,qBAAqB;IACrB,MAAM,EAAE,MAAM,WAAW,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QAChD,UAAU;YAAC;YAAkB;SAAa;QAC1C,OAAO;sCAAE,IAAM,oHAAA,CAAA,YAAS,CAAC,UAAU,CAAC;oBAAE,QAAQ;gBAAa;;IAC7D;IAEA,oCAAoC;IACpC,MAAM,UAAU,MAAM,OAAO,CAAC,wBAAA,kCAAA,YAAa,IAAI,IAAI,YAAY,IAAI,GACnD,MAAM,OAAO,CAAC,eAAe,cAAc,EAAE;IAG7D,MAAM,gBAAgB;QACpB;YAAE,OAAO;YAAO,OAAO;QAAc;QACrC;YAAE,OAAO;YAAK,OAAO;QAAU;QAC/B;YAAE,OAAO;YAAK,OAAO;QAAU;QAC/B;YAAE,OAAO;YAAK,OAAO;QAAU;QAC/B;YAAE,OAAO;YAAK,OAAO;QAAU;QAC/B;YAAE,OAAO;YAAK,OAAO;QAAS;KAC/B;IAED,MAAM,kBAAkB,MAAM,OAAO,CAAC,WAAW,QAAQ,MAAM,CAAC,CAAA;YAExC,iBACD,uBAAA;QAFrB,MAAM,gBAAgB,iBAAiB,SAAS,OAAO,MAAM,CAAC,QAAQ,OAAO;QAC7E,MAAM,gBAAgB,EAAA,kBAAA,OAAO,OAAO,cAAd,sCAAA,gBAAgB,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,UAC9D,mBAAA,OAAO,QAAQ,cAAf,wCAAA,wBAAA,iBAAiB,IAAI,cAArB,4CAAA,sBAAuB,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QACzF,OAAO,iBAAiB;IAC1B,KAAK,EAAE;IAEP,MAAM,eAAe,MAAM,OAAO,CAAC,WAAW,QAAQ,MAAM,GAAG;IAC/D,MAAM,gBAAgB,eAAe,IACjC,CAAC,QAAQ,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,CAAC,OAAO,MAAM,IAAI,CAAC,GAAG,KAAK,YAAY,EAAE,OAAO,CAAC,KACxF;IAEJ,MAAM,cAAc,CAAC;QACnB,OAAO;eAAI,MAAM;SAAG,CAAC,GAAG,CAAC,CAAC,GAAG,sBAC3B,6LAAC,qMAAA,CAAA,OAAI;gBAEH,WAAW,AAAC,WAEX,OADC,QAAQ,SAAS,iCAAiC;eAF/C;;;;;IAMX;IAEA,MAAM,sBAAsB,CAAC;QAC3B,6BAA6B;QAC7B,QAAQ,GAAG,CAAC,oBAAoB;IAClC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;;;;;0BAED,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;;;;;;kDAE3B,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;sCAMnC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;oDAAG,WAAU;8DAAsC;;;;;;;;;;;;sDAEtD,6LAAC;4CAAE,WAAU;sDAAoC,cAAc,OAAO,CAAC;;;;;;sDACvE,6LAAC;4CAAE,WAAU;;gDAAgB;gDAAU;gDAAa;;;;;;;;;;;;;8CAGtD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,iNAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC;oDAAG,WAAU;8DAAsC;;;;;;;;;;;;sDAEtD,6LAAC;4CAAE,WAAU;sDACV,MAAM,OAAO,CAAC,WAAW,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,IAAI,GAAG,MAAM,GAAG;;;;;;sDAExE,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAG/B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,2NAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;8DACzB,6LAAC;oDAAG,WAAU;8DAAsC;;;;;;;;;;;;sDAEtD,6LAAC;4CAAE,WAAU;;gDACV,eAAe,KAAK,MAAM,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,AAAC,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,EAAE,MAAM,GAAG,eAAgB,OAAO;gDAAE;;;;;;;sDAE9H,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;sCAKjC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;oDACC,MAAK;oDACL,aAAY;oDACZ,OAAO;oDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oDAC7C,WAAU;;;;;;;;;;;;;;;;;kDAMhB,6LAAC;wCAAI,WAAU;kDACZ,cAAc,GAAG,CAAC,CAAC,uBAClB,6LAAC;gDAEC,SAAS,IAAM,gBAAgB,OAAO,KAAK;gDAC3C,WAAW,AAAC,wEAIX,OAHC,iBAAiB,OAAO,KAAK,GACzB,6BACA;0DAGL,OAAO,KAAK;+CARR,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;sCAgB3B,6LAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC,QAAQ,sBAC5B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,WAAU;;sDAGV,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;sEAElB,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAA+B,OAAO,QAAQ,CAAC,IAAI;;;;;;8EACjE,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;sFAAQ,YAAY,OAAO,MAAM;;;;;;sFAChD,6LAAC;4EAAK,WAAU;sFAAwB;;;;;;sFACxC,6LAAC;4EAAK,WAAU;sFAAyB,OAAO,IAAI;;;;;;;;;;;;;;;;;;;;;;;;8DAI1D,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,iNAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,6LAAC;sEAAM,OAAO,OAAO;;;;;;;;;;;;;;;;;;sDAKzB,6LAAC;4CAAE,WAAU;sDAAsB,OAAO,OAAO;;;;;;sDAGjD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAA6B;;;;;;8DAC1C,6LAAC;oDAAI,WAAU;8DACZ,OAAO,UAAU,CAAC,GAAG,CAAC,CAAC,MAAM,oBAC5B,6LAAC;4DAEC,WAAU;sEAET;2DAHI;;;;;;;;;;;;;;;;wCAUZ,OAAO,QAAQ,iBACd,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAA2C;;;;;;8DACxD,6LAAC;oDAAE,WAAU;8DAAmB,OAAO,QAAQ;;;;;;;;;;;qGAGjD,6LAAC;4CACC,SAAS,IAAM,oBAAoB,OAAO,EAAE;4CAC5C,WAAU;sDACX;;;;;;;mCAvDE,OAAO,EAAE;;;;;;;;;;wBA+DnB,gBAAgB,MAAM,KAAK,mBAC1B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,2NAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACzB,6LAAC;oCAAG,WAAU;8CAA2C;;;;;;8CACzD,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3C;GA1OM;;QAKqC,8KAAA,CAAA,WAAQ;;;KAL7C;uCA4OS", "debugId": null}}]}