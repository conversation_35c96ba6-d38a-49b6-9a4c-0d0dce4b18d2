{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/project%20food-order%206k/food-ordering-system-modern/frontend/src/lib/api.js"], "sourcesContent": ["import axios from 'axios';\nimport { toast } from 'react-hot-toast';\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api',\n  timeout: 10000,\n  withCredentials: true,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Request interceptor to add auth token\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor for error handling\napi.interceptors.response.use(\n  (response) => {\n    return response;\n  },\n  async (error) => {\n    const originalRequest = error.config;\n\n    // Only try to refresh if we have a token and this is an auth-related 401\n    if (error.response?.status === 401 && !originalRequest._retry && localStorage.getItem('token')) {\n      originalRequest._retry = true;\n\n      try {\n        // Try to refresh token\n        const response = await api.post('/auth/refresh');\n        const { token } = response.data.data;\n\n        localStorage.setItem('token', token);\n        originalRequest.headers.Authorization = `Bearer ${token}`;\n\n        // Update auth store with new token\n        if (typeof window !== 'undefined' && window.useAuthStore) {\n          window.useAuthStore.getState().setToken(token);\n        }\n\n        return api(originalRequest);\n      } catch (refreshError) {\n        // Refresh failed, clear auth data\n        localStorage.removeItem('token');\n        localStorage.removeItem('user');\n\n        // Only redirect to login if not already on auth pages and this was a protected route\n        if (!window.location.pathname.startsWith('/auth') && originalRequest.headers.Authorization) {\n          window.location.href = '/auth/login';\n        }\n\n        return Promise.reject(refreshError);\n      }\n    }\n\n    // Show error toast for non-401 errors\n    if (error.response?.data?.message) {\n      toast.error(error.response.data.message);\n    } else {\n      toast.error('Something went wrong. Please try again.');\n    }\n\n    return Promise.reject(error);\n  }\n);\n\n// Auth API\nexport const authAPI = {\n  register: (data) => api.post('/auth/register', data),\n  login: (data) => api.post('/auth/login', data),\n  logout: () => api.post('/auth/logout'),\n  getProfile: () => api.get('/auth/me'),\n  forgotPassword: (email) => api.post('/auth/forgot-password', { email }),\n  resetPassword: (data) => api.put('/auth/reset-password', data),\n  refreshToken: () => api.post('/auth/refresh'),\n};\n\n// User API\nexport const userAPI = {\n  getProfile: () => api.get('/users/profile'),\n  updateProfile: (data) => api.put('/users/profile', data),\n  addAddress: (data) => api.post('/users/addresses', data),\n  updateAddress: (id, data) => api.put(`/users/addresses/${id}`, data),\n  deleteAddress: (id) => api.delete(`/users/addresses/${id}`),\n  getFavorites: () => api.get('/users/favorites'),\n  addToFavorites: (restaurantId) => api.post(`/users/favorites/${restaurantId}`),\n  removeFromFavorites: (restaurantId) => api.delete(`/users/favorites/${restaurantId}`),\n  getOrders: (params) => api.get('/users/orders', { params }),\n  getStats: () => api.get('/users/stats'),\n};\n\n// Restaurant API\nexport const restaurantAPI = {\n  getAll: (params) => api.get('/restaurants', { params }),\n  getById: (id) => api.get(`/restaurants/${id}`),\n  getNearby: (lat, lng, params) => api.get(`/restaurants/nearby/${lat}/${lng}`, { params }),\n  create: (data) => api.post('/restaurants', data),\n  update: (id, data) => api.put(`/restaurants/${id}`, data),\n  delete: (id) => api.delete(`/restaurants/${id}`),\n  toggleStatus: (id) => api.patch(`/restaurants/${id}/toggle-status`),\n};\n\n// Food API\nexport const foodAPI = {\n  getAll: (params) => api.get('/foods', { params }),\n  getById: (id) => api.get(`/foods/${id}`),\n  getByRestaurant: (restaurantId, params) => api.get(`/foods/restaurant/${restaurantId}`, { params }),\n  create: (data) => api.post('/foods', data),\n  update: (id, data) => api.put(`/foods/${id}`, data),\n  delete: (id) => api.delete(`/foods/${id}`),\n  toggleAvailability: (id) => api.patch(`/foods/${id}/toggle-availability`),\n};\n\n// Order API\nexport const orderAPI = {\n  create: (data) => api.post('/orders', data),\n  getAll: (params) => api.get('/orders', { params }),\n  getById: (id) => api.get(`/orders/${id}`),\n  getUserOrders: (params) => api.get('/users/orders', { params }),\n  updateStatus: (id, data) => api.patch(`/orders/${id}/status`, data),\n  cancel: (id, reason) => api.patch(`/orders/${id}/cancel`, { reason }),\n};\n\n// Payment API\nexport const paymentAPI = {\n  process: (data) => api.post('/payments/process', data),\n  getMethods: () => api.get('/payments/methods'),\n  getHistory: (params) => api.get('/payments/history', { params }),\n  refund: (data) => api.post('/payments/refund', data),\n};\n\n// Admin API\nexport const adminAPI = {\n  getStats: (params) => api.get('/admin/stats', { params }),\n  getUsers: (params) => api.get('/admin/users', { params }),\n  getRestaurants: (params) => api.get('/admin/restaurants', { params }),\n  getOrders: (params) => api.get('/admin/orders', { params }),\n  updateRestaurantStatus: (id, status) => api.patch(`/admin/restaurants/${id}/status`, { status }),\n  toggleUserStatus: (id) => api.patch(`/admin/users/${id}/toggle-status`),\n  getAnalytics: (period) => api.get('/admin/analytics', { params: { period } }),\n  getPayments: (params) => api.get('/admin/payments', { params }),\n  getReviews: (params) => api.get('/admin/reviews', { params }),\n  getReports: (type, params) => api.get(`/admin/reports/${type}`, { params }),\n  getSettings: () => api.get('/admin/settings'),\n  updateSettings: (data) => api.put('/admin/settings', data),\n};\n\n// Vendor API\nexport const vendorAPI = {\n  getDashboard: (params) => api.get('/vendors/dashboard', { params }),\n  getAnalytics: (period) => api.get('/vendors/analytics', { params: { period } }),\n  getOrders: (params) => api.get('/vendors/orders', { params }),\n  getRecentOrders: (params) => api.get('/vendors/orders/recent', { params }),\n  getFoods: (params) => api.get('/vendors/foods', { params }),\n  getRestaurant: () => api.get('/vendors/restaurant'),\n  createRestaurant: (data) => api.post('/vendors/restaurant', data),\n  updateRestaurant: (data) => api.put('/vendors/restaurant', data),\n  createFood: (data) => api.post('/vendors/foods', data),\n  updateFood: (id, data) => api.put(`/vendors/foods/${id}`, data),\n  deleteFood: (id) => api.delete(`/vendors/foods/${id}`),\n  updateOrderStatus: (id, status) => api.patch(`/vendors/orders/${id}/status`, { status }),\n  getCustomers: (params) => api.get('/vendors/customers', { params }),\n  getReviews: (params) => api.get('/vendors/reviews', { params }),\n  getNotifications: (params) => api.get('/vendors/notifications', { params }),\n};\n\n// Delivery API\nexport const deliveryAPI = {\n  getDashboard: () => api.get('/delivery/dashboard'),\n  getOrders: (params) => api.get('/delivery/orders', { params }),\n  acceptOrder: (id) => api.patch(`/delivery/orders/${id}/accept`),\n  pickupOrder: (id) => api.patch(`/delivery/orders/${id}/pickup`),\n  deliverOrder: (id) => api.patch(`/delivery/orders/${id}/deliver`),\n  getEarnings: (params) => api.get('/delivery/earnings', { params }),\n  getHistory: (params) => api.get('/delivery/history', { params }),\n  updateLocation: (data) => api.patch('/delivery/location', data),\n  updateStatus: (status) => api.patch('/delivery/status', { status }),\n};\n\n// Offers API\nexport const offersAPI = {\n  getOffers: (params) => api.get('/offers', { params }),\n  getOfferById: (id) => api.get(`/offers/${id}`),\n  applyOffer: (code, orderData) => api.post('/offers/apply', { code, ...orderData }),\n  validateOffer: (code) => api.post('/offers/validate', { code }),\n};\n\n// Notifications API\nexport const notificationsAPI = {\n  getNotifications: (params) => api.get('/notifications', { params }),\n  getUnreadCount: () => api.get('/notifications/unread-count'),\n  markAsRead: (id) => api.patch(`/notifications/${id}/read`),\n  markAllAsRead: () => api.patch('/notifications/mark-all-read'),\n  deleteNotification: (id) => api.delete(`/notifications/${id}`),\n};\n\nexport default api;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAKW;AALX;AACA;;;AAEA,wBAAwB;AACxB,MAAM,MAAM,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACvB,SAAS,iEAAmC;IAC5C,SAAS;IACT,iBAAiB;IACjB,SAAS;QACP,gBAAgB;IAClB;AACF;AAEA,wCAAwC;AACxC,IAAI,YAAY,CAAC,OAAO,CAAC,GAAG,CAC1B,CAAC;IACC,MAAM,QAAQ,aAAa,OAAO,CAAC;IACnC,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,aAAa,GAAG,AAAC,UAAe,OAAN;IAC3C;IACA,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,0CAA0C;AAC1C,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC3B,CAAC;IACC,OAAO;AACT,GACA,OAAO;QAID,iBAgCA,sBAAA;IAnCJ,MAAM,kBAAkB,MAAM,MAAM;IAEpC,yEAAyE;IACzE,IAAI,EAAA,kBAAA,MAAM,QAAQ,cAAd,sCAAA,gBAAgB,MAAM,MAAK,OAAO,CAAC,gBAAgB,MAAM,IAAI,aAAa,OAAO,CAAC,UAAU;QAC9F,gBAAgB,MAAM,GAAG;QAEzB,IAAI;YACF,uBAAuB;YACvB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC;YAChC,MAAM,EAAE,KAAK,EAAE,GAAG,SAAS,IAAI,CAAC,IAAI;YAEpC,aAAa,OAAO,CAAC,SAAS;YAC9B,gBAAgB,OAAO,CAAC,aAAa,GAAG,AAAC,UAAe,OAAN;YAElD,mCAAmC;YACnC,IAAI,aAAkB,eAAe,OAAO,YAAY,EAAE;gBACxD,OAAO,YAAY,CAAC,QAAQ,GAAG,QAAQ,CAAC;YAC1C;YAEA,OAAO,IAAI;QACb,EAAE,OAAO,cAAc;YACrB,kCAAkC;YAClC,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;YAExB,qFAAqF;YACrF,IAAI,CAAC,OAAO,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,YAAY,gBAAgB,OAAO,CAAC,aAAa,EAAE;gBAC1F,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB;YAEA,OAAO,QAAQ,MAAM,CAAC;QACxB;IACF;IAEA,sCAAsC;IACtC,KAAI,mBAAA,MAAM,QAAQ,cAAd,wCAAA,uBAAA,iBAAgB,IAAI,cAApB,2CAAA,qBAAsB,OAAO,EAAE;QACjC,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO;IACzC,OAAO;QACL,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;IACd;IAEA,OAAO,QAAQ,MAAM,CAAC;AACxB;AAIK,MAAM,UAAU;IACrB,UAAU,CAAC,OAAS,IAAI,IAAI,CAAC,kBAAkB;IAC/C,OAAO,CAAC,OAAS,IAAI,IAAI,CAAC,eAAe;IACzC,QAAQ,IAAM,IAAI,IAAI,CAAC;IACvB,YAAY,IAAM,IAAI,GAAG,CAAC;IAC1B,gBAAgB,CAAC,QAAU,IAAI,IAAI,CAAC,yBAAyB;YAAE;QAAM;IACrE,eAAe,CAAC,OAAS,IAAI,GAAG,CAAC,wBAAwB;IACzD,cAAc,IAAM,IAAI,IAAI,CAAC;AAC/B;AAGO,MAAM,UAAU;IACrB,YAAY,IAAM,IAAI,GAAG,CAAC;IAC1B,eAAe,CAAC,OAAS,IAAI,GAAG,CAAC,kBAAkB;IACnD,YAAY,CAAC,OAAS,IAAI,IAAI,CAAC,oBAAoB;IACnD,eAAe,CAAC,IAAI,OAAS,IAAI,GAAG,CAAC,AAAC,oBAAsB,OAAH,KAAM;IAC/D,eAAe,CAAC,KAAO,IAAI,MAAM,CAAC,AAAC,oBAAsB,OAAH;IACtD,cAAc,IAAM,IAAI,GAAG,CAAC;IAC5B,gBAAgB,CAAC,eAAiB,IAAI,IAAI,CAAC,AAAC,oBAAgC,OAAb;IAC/D,qBAAqB,CAAC,eAAiB,IAAI,MAAM,CAAC,AAAC,oBAAgC,OAAb;IACtE,WAAW,CAAC,SAAW,IAAI,GAAG,CAAC,iBAAiB;YAAE;QAAO;IACzD,UAAU,IAAM,IAAI,GAAG,CAAC;AAC1B;AAGO,MAAM,gBAAgB;IAC3B,QAAQ,CAAC,SAAW,IAAI,GAAG,CAAC,gBAAgB;YAAE;QAAO;IACrD,SAAS,CAAC,KAAO,IAAI,GAAG,CAAC,AAAC,gBAAkB,OAAH;IACzC,WAAW,CAAC,KAAK,KAAK,SAAW,IAAI,GAAG,CAAC,AAAC,uBAA6B,OAAP,KAAI,KAAO,OAAJ,MAAO;YAAE;QAAO;IACvF,QAAQ,CAAC,OAAS,IAAI,IAAI,CAAC,gBAAgB;IAC3C,QAAQ,CAAC,IAAI,OAAS,IAAI,GAAG,CAAC,AAAC,gBAAkB,OAAH,KAAM;IACpD,QAAQ,CAAC,KAAO,IAAI,MAAM,CAAC,AAAC,gBAAkB,OAAH;IAC3C,cAAc,CAAC,KAAO,IAAI,KAAK,CAAC,AAAC,gBAAkB,OAAH,IAAG;AACrD;AAGO,MAAM,UAAU;IACrB,QAAQ,CAAC,SAAW,IAAI,GAAG,CAAC,UAAU;YAAE;QAAO;IAC/C,SAAS,CAAC,KAAO,IAAI,GAAG,CAAC,AAAC,UAAY,OAAH;IACnC,iBAAiB,CAAC,cAAc,SAAW,IAAI,GAAG,CAAC,AAAC,qBAAiC,OAAb,eAAgB;YAAE;QAAO;IACjG,QAAQ,CAAC,OAAS,IAAI,IAAI,CAAC,UAAU;IACrC,QAAQ,CAAC,IAAI,OAAS,IAAI,GAAG,CAAC,AAAC,UAAY,OAAH,KAAM;IAC9C,QAAQ,CAAC,KAAO,IAAI,MAAM,CAAC,AAAC,UAAY,OAAH;IACrC,oBAAoB,CAAC,KAAO,IAAI,KAAK,CAAC,AAAC,UAAY,OAAH,IAAG;AACrD;AAGO,MAAM,WAAW;IACtB,QAAQ,CAAC,OAAS,IAAI,IAAI,CAAC,WAAW;IACtC,QAAQ,CAAC,SAAW,IAAI,GAAG,CAAC,WAAW;YAAE;QAAO;IAChD,SAAS,CAAC,KAAO,IAAI,GAAG,CAAC,AAAC,WAAa,OAAH;IACpC,eAAe,CAAC,SAAW,IAAI,GAAG,CAAC,iBAAiB;YAAE;QAAO;IAC7D,cAAc,CAAC,IAAI,OAAS,IAAI,KAAK,CAAC,AAAC,WAAa,OAAH,IAAG,YAAU;IAC9D,QAAQ,CAAC,IAAI,SAAW,IAAI,KAAK,CAAC,AAAC,WAAa,OAAH,IAAG,YAAU;YAAE;QAAO;AACrE;AAGO,MAAM,aAAa;IACxB,SAAS,CAAC,OAAS,IAAI,IAAI,CAAC,qBAAqB;IACjD,YAAY,IAAM,IAAI,GAAG,CAAC;IAC1B,YAAY,CAAC,SAAW,IAAI,GAAG,CAAC,qBAAqB;YAAE;QAAO;IAC9D,QAAQ,CAAC,OAAS,IAAI,IAAI,CAAC,oBAAoB;AACjD;AAGO,MAAM,WAAW;IACtB,UAAU,CAAC,SAAW,IAAI,GAAG,CAAC,gBAAgB;YAAE;QAAO;IACvD,UAAU,CAAC,SAAW,IAAI,GAAG,CAAC,gBAAgB;YAAE;QAAO;IACvD,gBAAgB,CAAC,SAAW,IAAI,GAAG,CAAC,sBAAsB;YAAE;QAAO;IACnE,WAAW,CAAC,SAAW,IAAI,GAAG,CAAC,iBAAiB;YAAE;QAAO;IACzD,wBAAwB,CAAC,IAAI,SAAW,IAAI,KAAK,CAAC,AAAC,sBAAwB,OAAH,IAAG,YAAU;YAAE;QAAO;IAC9F,kBAAkB,CAAC,KAAO,IAAI,KAAK,CAAC,AAAC,gBAAkB,OAAH,IAAG;IACvD,cAAc,CAAC,SAAW,IAAI,GAAG,CAAC,oBAAoB;YAAE,QAAQ;gBAAE;YAAO;QAAE;IAC3E,aAAa,CAAC,SAAW,IAAI,GAAG,CAAC,mBAAmB;YAAE;QAAO;IAC7D,YAAY,CAAC,SAAW,IAAI,GAAG,CAAC,kBAAkB;YAAE;QAAO;IAC3D,YAAY,CAAC,MAAM,SAAW,IAAI,GAAG,CAAC,AAAC,kBAAsB,OAAL,OAAQ;YAAE;QAAO;IACzE,aAAa,IAAM,IAAI,GAAG,CAAC;IAC3B,gBAAgB,CAAC,OAAS,IAAI,GAAG,CAAC,mBAAmB;AACvD;AAGO,MAAM,YAAY;IACvB,cAAc,CAAC,SAAW,IAAI,GAAG,CAAC,sBAAsB;YAAE;QAAO;IACjE,cAAc,CAAC,SAAW,IAAI,GAAG,CAAC,sBAAsB;YAAE,QAAQ;gBAAE;YAAO;QAAE;IAC7E,WAAW,CAAC,SAAW,IAAI,GAAG,CAAC,mBAAmB;YAAE;QAAO;IAC3D,iBAAiB,CAAC,SAAW,IAAI,GAAG,CAAC,0BAA0B;YAAE;QAAO;IACxE,UAAU,CAAC,SAAW,IAAI,GAAG,CAAC,kBAAkB;YAAE;QAAO;IACzD,eAAe,IAAM,IAAI,GAAG,CAAC;IAC7B,kBAAkB,CAAC,OAAS,IAAI,IAAI,CAAC,uBAAuB;IAC5D,kBAAkB,CAAC,OAAS,IAAI,GAAG,CAAC,uBAAuB;IAC3D,YAAY,CAAC,OAAS,IAAI,IAAI,CAAC,kBAAkB;IACjD,YAAY,CAAC,IAAI,OAAS,IAAI,GAAG,CAAC,AAAC,kBAAoB,OAAH,KAAM;IAC1D,YAAY,CAAC,KAAO,IAAI,MAAM,CAAC,AAAC,kBAAoB,OAAH;IACjD,mBAAmB,CAAC,IAAI,SAAW,IAAI,KAAK,CAAC,AAAC,mBAAqB,OAAH,IAAG,YAAU;YAAE;QAAO;IACtF,cAAc,CAAC,SAAW,IAAI,GAAG,CAAC,sBAAsB;YAAE;QAAO;IACjE,YAAY,CAAC,SAAW,IAAI,GAAG,CAAC,oBAAoB;YAAE;QAAO;IAC7D,kBAAkB,CAAC,SAAW,IAAI,GAAG,CAAC,0BAA0B;YAAE;QAAO;AAC3E;AAGO,MAAM,cAAc;IACzB,cAAc,IAAM,IAAI,GAAG,CAAC;IAC5B,WAAW,CAAC,SAAW,IAAI,GAAG,CAAC,oBAAoB;YAAE;QAAO;IAC5D,aAAa,CAAC,KAAO,IAAI,KAAK,CAAC,AAAC,oBAAsB,OAAH,IAAG;IACtD,aAAa,CAAC,KAAO,IAAI,KAAK,CAAC,AAAC,oBAAsB,OAAH,IAAG;IACtD,cAAc,CAAC,KAAO,IAAI,KAAK,CAAC,AAAC,oBAAsB,OAAH,IAAG;IACvD,aAAa,CAAC,SAAW,IAAI,GAAG,CAAC,sBAAsB;YAAE;QAAO;IAChE,YAAY,CAAC,SAAW,IAAI,GAAG,CAAC,qBAAqB;YAAE;QAAO;IAC9D,gBAAgB,CAAC,OAAS,IAAI,KAAK,CAAC,sBAAsB;IAC1D,cAAc,CAAC,SAAW,IAAI,KAAK,CAAC,oBAAoB;YAAE;QAAO;AACnE;AAGO,MAAM,YAAY;IACvB,WAAW,CAAC,SAAW,IAAI,GAAG,CAAC,WAAW;YAAE;QAAO;IACnD,cAAc,CAAC,KAAO,IAAI,GAAG,CAAC,AAAC,WAAa,OAAH;IACzC,YAAY,CAAC,MAAM,YAAc,IAAI,IAAI,CAAC,iBAAiB;YAAE;YAAM,GAAG,SAAS;QAAC;IAChF,eAAe,CAAC,OAAS,IAAI,IAAI,CAAC,oBAAoB;YAAE;QAAK;AAC/D;AAGO,MAAM,mBAAmB;IAC9B,kBAAkB,CAAC,SAAW,IAAI,GAAG,CAAC,kBAAkB;YAAE;QAAO;IACjE,gBAAgB,IAAM,IAAI,GAAG,CAAC;IAC9B,YAAY,CAAC,KAAO,IAAI,KAAK,CAAC,AAAC,kBAAoB,OAAH,IAAG;IACnD,eAAe,IAAM,IAAI,KAAK,CAAC;IAC/B,oBAAoB,CAAC,KAAO,IAAI,MAAM,CAAC,AAAC,kBAAoB,OAAH;AAC3D;uCAEe", "debugId": null}}, {"offset": {"line": 279, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/project%20food-order%206k/food-ordering-system-modern/frontend/src/store/useAuthStore.js"], "sourcesContent": ["import { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport { authAPI } from '@/lib/api';\nimport { toast } from 'react-hot-toast';\n\nconst useAuthStore = create(\n  persist(\n    (set, get) => ({\n      user: null,\n      token: null,\n      isLoading: false,\n      isAuthenticated: false,\n\n      // Login action\n      login: async (credentials) => {\n        set({ isLoading: true });\n        try {\n          const response = await authAPI.login(credentials);\n          const { user, token } = response.data.data;\n\n          localStorage.setItem('token', token);\n          localStorage.setItem('user', JSON.stringify(user));\n          set({\n            user,\n            token,\n            isAuthenticated: true,\n            isLoading: false\n          });\n\n          toast.success('Login successful!');\n          return { success: true, user };\n        } catch (error) {\n          set({ isLoading: false });\n          return {\n            success: false,\n            error: error.response?.data?.message || 'Login failed'\n          };\n        }\n      },\n\n      // Register action\n      register: async (userData) => {\n        set({ isLoading: true });\n        try {\n          const response = await authAPI.register(userData);\n          const { user, token } = response.data.data;\n          \n          localStorage.setItem('token', token);\n          localStorage.setItem('user', JSON.stringify(user));\n          set({\n            user,\n            token,\n            isAuthenticated: true, \n            isLoading: false \n          });\n          \n          toast.success('Registration successful!');\n          return { success: true };\n        } catch (error) {\n          set({ isLoading: false });\n          return { \n            success: false, \n            error: error.response?.data?.message || 'Registration failed' \n          };\n        }\n      },\n\n      // Logout action\n      logout: async () => {\n        try {\n          await authAPI.logout();\n        } catch (error) {\n          console.error('Logout error:', error);\n        } finally {\n          localStorage.removeItem('token');\n          localStorage.removeItem('user');\n          set({ \n            user: null, \n            token: null, \n            isAuthenticated: false \n          });\n          toast.success('Logged out successfully');\n        }\n      },\n\n      // Get current user profile\n      getProfile: async () => {\n        try {\n          const response = await authAPI.getProfile();\n          const user = response.data.data;\n          set({ user });\n          return user;\n        } catch (error) {\n          console.error('Get profile error:', error);\n          return null;\n        }\n      },\n\n      // Update user profile\n      updateProfile: async (userData) => {\n        try {\n          const response = await authAPI.updateProfile(userData);\n          const user = response.data.data;\n          set({ user });\n          toast.success('Profile updated successfully');\n          return { success: true, user };\n        } catch (error) {\n          return { \n            success: false, \n            error: error.response?.data?.message || 'Update failed' \n          };\n        }\n      },\n\n      // Initialize auth state\n      initialize: async () => {\n        set({ isLoading: true });\n        try {\n          const token = localStorage.getItem('token');\n          if (token) {\n            set({ token, isAuthenticated: true });\n            const user = await get().getProfile();\n            if (user) {\n              set({ isLoading: false });\n            } else {\n              // Profile fetch failed, clear auth\n              get().clearAuth();\n              set({ isLoading: false });\n            }\n          } else {\n            set({ isLoading: false });\n          }\n        } catch (error) {\n          console.error('Auth initialization error:', error);\n          get().clearAuth();\n          set({ isLoading: false });\n        }\n      },\n\n      // Set token (for token refresh)\n      setToken: (token) => {\n        set({ token });\n        localStorage.setItem('token', token);\n      },\n\n      // Initialize auth state from localStorage\n      initializeAuth: () => {\n        const token = localStorage.getItem('token');\n        const storedUser = localStorage.getItem('user');\n\n        if (token && storedUser) {\n          try {\n            const user = JSON.parse(storedUser);\n            set({\n              user,\n              token,\n              isAuthenticated: true,\n              isLoading: false\n            });\n          } catch (error) {\n            console.error('Error parsing stored user:', error);\n            get().clearAuth();\n          }\n        } else {\n          set({ isLoading: false });\n        }\n      },\n\n      // Clear auth state\n      clearAuth: () => {\n        localStorage.removeItem('token');\n        localStorage.removeItem('user');\n        set({\n          user: null,\n          token: null,\n          isAuthenticated: false\n        });\n      },\n    }),\n    {\n      name: 'auth-storage',\n      partialize: (state) => ({ \n        user: state.user, \n        token: state.token, \n        isAuthenticated: state.isAuthenticated \n      }),\n    }\n  )\n);\n\nexport default useAuthStore;\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEA,MAAM,eAAe,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,EACxB,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,MAAM;QACN,OAAO;QACP,WAAW;QACX,iBAAiB;QAEjB,eAAe;QACf,OAAO,OAAO;YACZ,IAAI;gBAAE,WAAW;YAAK;YACtB,IAAI;gBACF,MAAM,WAAW,MAAM,oHAAA,CAAA,UAAO,CAAC,KAAK,CAAC;gBACrC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,SAAS,IAAI,CAAC,IAAI;gBAE1C,aAAa,OAAO,CAAC,SAAS;gBAC9B,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;gBAC5C,IAAI;oBACF;oBACA;oBACA,iBAAiB;oBACjB,WAAW;gBACb;gBAEA,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO;oBAAE,SAAS;oBAAM;gBAAK;YAC/B,EAAE,OAAO,OAAO;oBAIL,sBAAA;gBAHT,IAAI;oBAAE,WAAW;gBAAM;gBACvB,OAAO;oBACL,SAAS;oBACT,OAAO,EAAA,kBAAA,MAAM,QAAQ,cAAd,uCAAA,uBAAA,gBAAgB,IAAI,cAApB,2CAAA,qBAAsB,OAAO,KAAI;gBAC1C;YACF;QACF;QAEA,kBAAkB;QAClB,UAAU,OAAO;YACf,IAAI;gBAAE,WAAW;YAAK;YACtB,IAAI;gBACF,MAAM,WAAW,MAAM,oHAAA,CAAA,UAAO,CAAC,QAAQ,CAAC;gBACxC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,SAAS,IAAI,CAAC,IAAI;gBAE1C,aAAa,OAAO,CAAC,SAAS;gBAC9B,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;gBAC5C,IAAI;oBACF;oBACA;oBACA,iBAAiB;oBACjB,WAAW;gBACb;gBAEA,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO;oBAAE,SAAS;gBAAK;YACzB,EAAE,OAAO,OAAO;oBAIL,sBAAA;gBAHT,IAAI;oBAAE,WAAW;gBAAM;gBACvB,OAAO;oBACL,SAAS;oBACT,OAAO,EAAA,kBAAA,MAAM,QAAQ,cAAd,uCAAA,uBAAA,gBAAgB,IAAI,cAApB,2CAAA,qBAAsB,OAAO,KAAI;gBAC1C;YACF;QACF;QAEA,gBAAgB;QAChB,QAAQ;YACN,IAAI;gBACF,MAAM,oHAAA,CAAA,UAAO,CAAC,MAAM;YACtB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iBAAiB;YACjC,SAAU;gBACR,aAAa,UAAU,CAAC;gBACxB,aAAa,UAAU,CAAC;gBACxB,IAAI;oBACF,MAAM;oBACN,OAAO;oBACP,iBAAiB;gBACnB;gBACA,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;QACF;QAEA,2BAA2B;QAC3B,YAAY;YACV,IAAI;gBACF,MAAM,WAAW,MAAM,oHAAA,CAAA,UAAO,CAAC,UAAU;gBACzC,MAAM,OAAO,SAAS,IAAI,CAAC,IAAI;gBAC/B,IAAI;oBAAE;gBAAK;gBACX,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,sBAAsB;gBACpC,OAAO;YACT;QACF;QAEA,sBAAsB;QACtB,eAAe,OAAO;YACpB,IAAI;gBACF,MAAM,WAAW,MAAM,oHAAA,CAAA,UAAO,CAAC,aAAa,CAAC;gBAC7C,MAAM,OAAO,SAAS,IAAI,CAAC,IAAI;gBAC/B,IAAI;oBAAE;gBAAK;gBACX,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO;oBAAE,SAAS;oBAAM;gBAAK;YAC/B,EAAE,OAAO,OAAO;oBAGL,sBAAA;gBAFT,OAAO;oBACL,SAAS;oBACT,OAAO,EAAA,kBAAA,MAAM,QAAQ,cAAd,uCAAA,uBAAA,gBAAgB,IAAI,cAApB,2CAAA,qBAAsB,OAAO,KAAI;gBAC1C;YACF;QACF;QAEA,wBAAwB;QACxB,YAAY;YACV,IAAI;gBAAE,WAAW;YAAK;YACtB,IAAI;gBACF,MAAM,QAAQ,aAAa,OAAO,CAAC;gBACnC,IAAI,OAAO;oBACT,IAAI;wBAAE;wBAAO,iBAAiB;oBAAK;oBACnC,MAAM,OAAO,MAAM,MAAM,UAAU;oBACnC,IAAI,MAAM;wBACR,IAAI;4BAAE,WAAW;wBAAM;oBACzB,OAAO;wBACL,mCAAmC;wBACnC,MAAM,SAAS;wBACf,IAAI;4BAAE,WAAW;wBAAM;oBACzB;gBACF,OAAO;oBACL,IAAI;wBAAE,WAAW;oBAAM;gBACzB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,8BAA8B;gBAC5C,MAAM,SAAS;gBACf,IAAI;oBAAE,WAAW;gBAAM;YACzB;QACF;QAEA,gCAAgC;QAChC,UAAU,CAAC;YACT,IAAI;gBAAE;YAAM;YACZ,aAAa,OAAO,CAAC,SAAS;QAChC;QAEA,0CAA0C;QAC1C,gBAAgB;YACd,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,aAAa,aAAa,OAAO,CAAC;YAExC,IAAI,SAAS,YAAY;gBACvB,IAAI;oBACF,MAAM,OAAO,KAAK,KAAK,CAAC;oBACxB,IAAI;wBACF;wBACA;wBACA,iBAAiB;wBACjB,WAAW;oBACb;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,8BAA8B;oBAC5C,MAAM,SAAS;gBACjB;YACF,OAAO;gBACL,IAAI;oBAAE,WAAW;gBAAM;YACzB;QACF;QAEA,mBAAmB;QACnB,WAAW;YACT,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;YACxB,IAAI;gBACF,MAAM;gBACN,OAAO;gBACP,iBAAiB;YACnB;QACF;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,MAAM,MAAM,IAAI;YAChB,OAAO,MAAM,KAAK;YAClB,iBAAiB,MAAM,eAAe;QACxC,CAAC;AACH;uCAIW", "debugId": null}}, {"offset": {"line": 505, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/project%20food-order%206k/food-ordering-system-modern/frontend/src/hooks/useSocket.js"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\nimport { io } from 'socket.io-client';\nimport { toast } from 'react-hot-toast';\nimport useAuthStore from '@/store/useAuthStore';\n\nconst useSocket = () => {\n  const socketRef = useRef(null);\n  const { user, isAuthenticated } = useAuthStore();\n\n  useEffect(() => {\n    if (!isAuthenticated || !user) return;\n\n    // Initialize socket connection\n    socketRef.current = io(process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000', {\n      withCredentials: true,\n      transports: ['websocket', 'polling']\n    });\n\n    const socket = socketRef.current;\n\n    // Connection events\n    socket.on('connect', () => {\n      console.log('Connected to server');\n      \n      // Join user-specific room\n      socket.emit('join-user-room', user._id);\n      \n      // Join restaurant room if user is a vendor\n      if (user.role === 'vendor' && user.restaurant) {\n        socket.emit('join-restaurant-room', user.restaurant._id);\n      }\n    });\n\n    socket.on('disconnect', () => {\n      console.log('Disconnected from server');\n    });\n\n    // Order-related events for customers\n    if (user.role === 'customer') {\n      socket.on('order-status-changed', (data) => {\n        toast.success(`Order ${data.status}: ${data.message}`);\n        // You can dispatch to a global state or trigger a refetch here\n      });\n\n      socket.on('order-placed', (data) => {\n        toast.success('Order placed successfully!');\n      });\n    }\n\n    // Restaurant-related events for vendors\n    if (user.role === 'vendor') {\n      socket.on('new-order-received', (orderData) => {\n        toast.success('New order received!');\n        // Play notification sound\n        playNotificationSound();\n      });\n\n      socket.on('payment-received', (data) => {\n        toast.success(`Payment received: ₹${data.amount}`);\n      });\n\n      socket.on('order-cancelled', (data) => {\n        toast.error(`Order cancelled: ${data.reason}`);\n      });\n    }\n\n    // Admin events\n    if (user.role === 'admin') {\n      socket.on('new-restaurant-registration', (data) => {\n        toast.info('New restaurant registration pending approval');\n      });\n\n      socket.on('new-user-registration', (data) => {\n        toast.info('New user registered');\n      });\n    }\n\n    // Cleanup on unmount\n    return () => {\n      if (socket) {\n        socket.disconnect();\n      }\n    };\n  }, [isAuthenticated, user]);\n\n  // Helper function to play notification sound\n  const playNotificationSound = () => {\n    try {\n      const audio = new Audio('/sounds/notification.mp3');\n      audio.play().catch(console.error);\n    } catch (error) {\n      console.error('Error playing notification sound:', error);\n    }\n  };\n\n  // Emit order status update (for vendors)\n  const updateOrderStatus = (orderData) => {\n    if (socketRef.current) {\n      socketRef.current.emit('order-status-update', orderData);\n    }\n  };\n\n  // Emit new order (for customers)\n  const emitNewOrder = (orderData) => {\n    if (socketRef.current) {\n      socketRef.current.emit('new-order', orderData);\n    }\n  };\n\n  return {\n    socket: socketRef.current,\n    updateOrderStatus,\n    emitNewOrder,\n    isConnected: socketRef.current?.connected || false\n  };\n};\n\nexport default useSocket;\n"], "names": [], "mappings": ";;;AAa2B;AAb3B;AACA;AAAA;AACA;AACA;;;;;;AAEA,MAAM,YAAY;QA4GD;;IA3Gf,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACzB,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAY,AAAD;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,CAAC,mBAAmB,CAAC,MAAM;YAE/B,+BAA+B;YAC/B,UAAU,OAAO,GAAG,CAAA,GAAA,kLAAA,CAAA,KAAE,AAAD,EAAE,iEAAmC,yBAAyB;gBACjF,iBAAiB;gBACjB,YAAY;oBAAC;oBAAa;iBAAU;YACtC;YAEA,MAAM,SAAS,UAAU,OAAO;YAEhC,oBAAoB;YACpB,OAAO,EAAE,CAAC;uCAAW;oBACnB,QAAQ,GAAG,CAAC;oBAEZ,0BAA0B;oBAC1B,OAAO,IAAI,CAAC,kBAAkB,KAAK,GAAG;oBAEtC,2CAA2C;oBAC3C,IAAI,KAAK,IAAI,KAAK,YAAY,KAAK,UAAU,EAAE;wBAC7C,OAAO,IAAI,CAAC,wBAAwB,KAAK,UAAU,CAAC,GAAG;oBACzD;gBACF;;YAEA,OAAO,EAAE,CAAC;uCAAc;oBACtB,QAAQ,GAAG,CAAC;gBACd;;YAEA,qCAAqC;YACrC,IAAI,KAAK,IAAI,KAAK,YAAY;gBAC5B,OAAO,EAAE,CAAC;2CAAwB,CAAC;wBACjC,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC,AAAC,SAAwB,OAAhB,KAAK,MAAM,EAAC,MAAiB,OAAb,KAAK,OAAO;oBACnD,+DAA+D;oBACjE;;gBAEA,OAAO,EAAE,CAAC;2CAAgB,CAAC;wBACzB,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBAChB;;YACF;YAEA,wCAAwC;YACxC,IAAI,KAAK,IAAI,KAAK,UAAU;gBAC1B,OAAO,EAAE,CAAC;2CAAsB,CAAC;wBAC/B,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;wBACd,0BAA0B;wBAC1B;oBACF;;gBAEA,OAAO,EAAE,CAAC;2CAAoB,CAAC;wBAC7B,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC,AAAC,sBAAiC,OAAZ,KAAK,MAAM;oBACjD;;gBAEA,OAAO,EAAE,CAAC;2CAAmB,CAAC;wBAC5B,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,AAAC,oBAA+B,OAAZ,KAAK,MAAM;oBAC7C;;YACF;YAEA,eAAe;YACf,IAAI,KAAK,IAAI,KAAK,SAAS;gBACzB,OAAO,EAAE,CAAC;2CAA+B,CAAC;wBACxC,0JAAA,CAAA,QAAK,CAAC,IAAI,CAAC;oBACb;;gBAEA,OAAO,EAAE,CAAC;2CAAyB,CAAC;wBAClC,0JAAA,CAAA,QAAK,CAAC,IAAI,CAAC;oBACb;;YACF;YAEA,qBAAqB;YACrB;uCAAO;oBACL,IAAI,QAAQ;wBACV,OAAO,UAAU;oBACnB;gBACF;;QACF;8BAAG;QAAC;QAAiB;KAAK;IAE1B,6CAA6C;IAC7C,MAAM,wBAAwB;QAC5B,IAAI;YACF,MAAM,QAAQ,IAAI,MAAM;YACxB,MAAM,IAAI,GAAG,KAAK,CAAC,QAAQ,KAAK;QAClC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;QACrD;IACF;IAEA,yCAAyC;IACzC,MAAM,oBAAoB,CAAC;QACzB,IAAI,UAAU,OAAO,EAAE;YACrB,UAAU,OAAO,CAAC,IAAI,CAAC,uBAAuB;QAChD;IACF;IAEA,iCAAiC;IACjC,MAAM,eAAe,CAAC;QACpB,IAAI,UAAU,OAAO,EAAE;YACrB,UAAU,OAAO,CAAC,IAAI,CAAC,aAAa;QACtC;IACF;IAEA,OAAO;QACL,QAAQ,UAAU,OAAO;QACzB;QACA;QACA,aAAa,EAAA,qBAAA,UAAU,OAAO,cAAjB,yCAAA,mBAAmB,SAAS,KAAI;IAC/C;AACF;GA9GM;;QAE8B,+HAAA,CAAA,UAAY;;;uCA8GjC", "debugId": null}}, {"offset": {"line": 656, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/project%20food-order%206k/food-ordering-system-modern/frontend/src/app/providers.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query';\nimport useAuthStore from '@/store/useAuthStore';\nimport useSocket from '@/hooks/useSocket';\n\nexport function Providers({ children }: { children: React.ReactNode }) {\n  const [queryClient] = useState(() => new QueryClient({\n    defaultOptions: {\n      queries: {\n        staleTime: 60 * 1000, // 1 minute\n        retry: 1,\n        refetchOnWindowFocus: false,\n      },\n    },\n  }));\n\n  const initializeAuth = useAuthStore((state) => state.initializeAuth);\n\n  // Initialize auth state on app load\n  useEffect(() => {\n    // Make auth store globally accessible for token refresh\n    if (typeof window !== 'undefined') {\n      window.useAuthStore = useAuthStore;\n    }\n    initializeAuth();\n  }, [initializeAuth]);\n\n  return (\n    <QueryClientProvider client={queryClient}>\n      <SocketProvider>\n        {children}\n      </SocketProvider>\n    </QueryClientProvider>\n  );\n}\n\n// Socket provider component\nfunction SocketProvider({ children }: { children: React.ReactNode }) {\n  // Initialize socket connection\n  useSocket();\n  \n  return <>{children}</>;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;;;AALA;;;;;AAOO,SAAS,UAAU,KAA2C;QAA3C,EAAE,QAAQ,EAAiC,GAA3C;;IACxB,MAAM,CAAC,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;8BAAE,IAAM,IAAI,gLAAA,CAAA,cAAW,CAAC;gBACnD,gBAAgB;oBACd,SAAS;wBACP,WAAW,KAAK;wBAChB,OAAO;wBACP,sBAAsB;oBACxB;gBACF;YACF;;IAEA,MAAM,iBAAiB,CAAA,GAAA,+HAAA,CAAA,UAAY,AAAD;kDAAE,CAAC,QAAU,MAAM,cAAc;;IAEnE,oCAAoC;IACpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,wDAAwD;YACxD,wCAAmC;gBACjC,OAAO,YAAY,GAAG,+HAAA,CAAA,UAAY;YACpC;YACA;QACF;8BAAG;QAAC;KAAe;IAEnB,qBACE,6LAAC,yLAAA,CAAA,sBAAmB;QAAC,QAAQ;kBAC3B,cAAA,6LAAC;sBACE;;;;;;;;;;;AAIT;GA7BgB;;QAWS,+HAAA,CAAA,UAAY;;;KAXrB;AA+BhB,4BAA4B;AAC5B,SAAS,eAAe,KAA2C;QAA3C,EAAE,QAAQ,EAAiC,GAA3C;;IACtB,+BAA+B;IAC/B,CAAA,GAAA,4HAAA,CAAA,UAAS,AAAD;IAER,qBAAO;kBAAG;;AACZ;IALS;;QAEP,4HAAA,CAAA,UAAS;;;MAFF", "debugId": null}}]}