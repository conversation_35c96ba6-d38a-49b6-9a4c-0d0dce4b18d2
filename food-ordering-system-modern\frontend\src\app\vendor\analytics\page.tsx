'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useQuery } from '@tanstack/react-query';
import { 
  BarChart3, 
  TrendingUp, 
  DollarSign, 
  Package, 
  Users,
  Calendar,
  ArrowUpRight,
  ArrowDownRight
} from 'lucide-react';
import VendorLayout from '@/components/VendorLayout';
import { vendorAPI } from '@/lib/api';

const VendorAnalytics = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('7d');

  // Fetch analytics data
  const { data: analytics, isLoading } = useQuery({
    queryKey: ['vendor-analytics', selectedPeriod],
    queryFn: () => vendorAPI.getAnalytics(selectedPeriod),
  });

  const periods = [
    { value: '7d', label: '7 Days' },
    { value: '30d', label: '30 Days' },
    { value: '90d', label: '90 Days' },
  ];

  const stats = [
    {
      title: 'Total Revenue',
      value: analytics?.data?.totalRevenue || 0,
      change: '+12.5%',
      trend: 'up',
      icon: DollarSign,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
    },
    {
      title: 'Total Orders',
      value: analytics?.data?.totalOrders || 0,
      change: '+8.2%',
      trend: 'up',
      icon: Package,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
    },
    {
      title: 'Period Orders',
      value: analytics?.data?.periodOrders || 0,
      change: '+15.3%',
      trend: 'up',
      icon: TrendingUp,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
    },
    {
      title: 'Period Revenue',
      value: analytics?.data?.periodRevenue || 0,
      change: '+5.7%',
      trend: 'up',
      icon: BarChart3,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
    },
  ];

  if (isLoading) {
    return (
      <VendorLayout title="Analytics" subtitle="Track your restaurant performance">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded-lg"></div>
            ))}
          </div>
        </div>
      </VendorLayout>
    );
  }

  const headerActions = (
    <div className="flex bg-white rounded-lg p-1 shadow-sm border">
      {periods.map((period) => (
        <button
          key={period.value}
          onClick={() => setSelectedPeriod(period.value)}
          className={`px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
            selectedPeriod === period.value
              ? 'bg-orange-500 text-white shadow-sm'
              : 'text-gray-600 hover:text-orange-600'
          }`}
        >
          {period.label}
        </button>
      ))}
    </div>
  );

  return (
    <VendorLayout
      title="Analytics"
      subtitle="Track your restaurant's performance"
      headerActions={headerActions}
    >

          {/* Stats Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {stats.map((stat, index) => (
              <motion.div
                key={stat.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-white rounded-2xl p-6 shadow-md hover:shadow-lg transition-shadow duration-200"
              >
                <div className="flex items-center justify-between mb-4">
                  <div className={`w-12 h-12 ${stat.bgColor} rounded-lg flex items-center justify-center`}>
                    <stat.icon className={`w-6 h-6 ${stat.color}`} />
                  </div>
                  <div className={`flex items-center gap-1 text-sm ${
                    stat.trend === 'up' ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {stat.trend === 'up' ? (
                      <ArrowUpRight className="w-4 h-4" />
                    ) : (
                      <ArrowDownRight className="w-4 h-4" />
                    )}
                    {stat.change}
                  </div>
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-1">
                    {stat.title.includes('Revenue') ? `₹${stat.value.toLocaleString()}` : stat.value.toLocaleString()}
                  </h3>
                  <p className="text-gray-600 text-sm">{stat.title}</p>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Charts Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Revenue Chart */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="bg-white rounded-2xl p-6 shadow-md"
            >
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Revenue Trend</h3>
              <div className="h-64 flex items-center justify-center text-gray-500">
                <div className="text-center">
                  <BarChart3 className="w-12 h-12 mx-auto mb-2 text-gray-300" />
                  <p>Chart visualization coming soon</p>
                </div>
              </div>
            </motion.div>

            {/* Orders Chart */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              className="bg-white rounded-2xl p-6 shadow-md"
            >
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Volume</h3>
              <div className="h-64 flex items-center justify-center text-gray-500">
                <div className="text-center">
                  <Package className="w-12 h-12 mx-auto mb-2 text-gray-300" />
                  <p>Chart visualization coming soon</p>
                </div>
              </div>
            </motion.div>
          </div>

          {/* Performance Insights */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="mt-6 bg-white rounded-2xl p-6 shadow-md"
          >
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Performance Insights</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="p-4 bg-green-50 rounded-lg">
                <h4 className="font-medium text-green-800 mb-2">Best Performing Day</h4>
                <p className="text-green-600">Sunday - ₹2,450 revenue</p>
              </div>
              <div className="p-4 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-800 mb-2">Peak Hours</h4>
                <p className="text-blue-600">7:00 PM - 9:00 PM</p>
              </div>
              <div className="p-4 bg-orange-50 rounded-lg">
                <h4 className="font-medium text-orange-800 mb-2">Average Order Value</h4>
                <p className="text-orange-600">₹{Math.round((analytics?.data?.periodRevenue || 0) / (analytics?.data?.periodOrders || 1))}</p>
              </div>
            </div>
          </motion.div>
    </VendorLayout>
  );
};

export default VendorAnalytics;
