'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  Clock,
  CheckCircle,
  Package,
  Truck,
  ChefHat,
  Search,
  Eye,
  Phone,
  MapPin,
  DollarSign,
  Calendar,
  RefreshCw
} from 'lucide-react';
import Image from 'next/image';
import VendorLayout from '@/components/VendorLayout';
import { vendorAPI } from '@/lib/api';
import { formatCurrency, getOptimizedImageUrl } from '@/lib/utils';
import useAuthStore from '@/store/useAuthStore';
import { toast } from 'react-hot-toast';

const VendorOrdersPage = () => {
  const { user, isAuthenticated } = useAuthStore();
  const [statusFilter, setStatusFilter] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const queryClient = useQueryClient();

  // Fetch vendor orders
  const { data: ordersData, isLoading, error } = useQuery({
    queryKey: ['vendor-orders', { status: statusFilter, search: searchQuery }],
    queryFn: () => vendorAPI.getOrders({ 
      status: statusFilter === 'all' ? undefined : statusFilter,
      search: searchQuery || undefined 
    }),
    enabled: isAuthenticated && user?.role === 'vendor',
    refetchInterval: 30000, // Refetch every 30 seconds
  });

  // Update order status mutation
  const updateOrderMutation = useMutation({
    mutationFn: ({ orderId, status }: { orderId: string; status: string }) =>
      vendorAPI.updateOrderStatus(orderId, status),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['vendor-orders'] });
      toast.success('Order status updated successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to update order status');
    },
  });

  const orders = ordersData?.data?.data || [];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'text-yellow-600 bg-yellow-100 border-yellow-200';
      case 'confirmed': return 'text-blue-600 bg-blue-100 border-blue-200';
      case 'preparing': return 'text-orange-600 bg-orange-100 border-orange-200';
      case 'ready': return 'text-purple-600 bg-purple-100 border-purple-200';
      case 'picked-up': return 'text-indigo-600 bg-indigo-100 border-indigo-200';
      case 'delivered': return 'text-green-600 bg-green-100 border-green-200';
      case 'cancelled': return 'text-red-600 bg-red-100 border-red-200';
      default: return 'text-gray-600 bg-gray-100 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return Clock;
      case 'confirmed': return CheckCircle;
      case 'preparing': return ChefHat;
      case 'ready': return Package;
      case 'picked-up': return Truck;
      case 'delivered': return CheckCircle;
      case 'cancelled': return Clock;
      default: return Clock;
    }
  };

  const getNextStatus = (currentStatus: string) => {
    switch (currentStatus) {
      case 'pending': return 'confirmed';
      case 'confirmed': return 'preparing';
      case 'preparing': return 'ready';
      case 'ready': return 'picked-up';
      case 'picked-up': return 'delivered';
      default: return null;
    }
  };

  const getNextStatusLabel = (currentStatus: string) => {
    switch (currentStatus) {
      case 'pending': return 'Confirm Order';
      case 'confirmed': return 'Start Preparing';
      case 'preparing': return 'Mark Ready';
      case 'ready': return 'Mark Picked Up';
      case 'picked-up': return 'Mark Delivered';
      default: return null;
    }
  };

  const handleStatusUpdate = (orderId: string, newStatus: string) => {
    updateOrderMutation.mutate({ orderId, status: newStatus });
  };

  const statusOptions = [
    { value: 'all', label: 'All Orders' },
    { value: 'pending', label: 'Pending' },
    { value: 'confirmed', label: 'Confirmed' },
    { value: 'preparing', label: 'Preparing' },
    { value: 'ready', label: 'Ready' },
    { value: 'picked-up', label: 'Picked Up' },
    { value: 'delivered', label: 'Delivered' },
    { value: 'cancelled', label: 'Cancelled' },
  ];

  if (!isAuthenticated || user?.role !== 'vendor') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="py-12">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h1>
            <p className="text-gray-600 mb-8">You need to be a vendor to access this page.</p>
          </div>
        </div>
      </div>
    );
  }

  const headerActions = (
    <motion.button
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      onClick={() => queryClient.invalidateQueries({ queryKey: ['vendor-orders'] })}
      className="flex items-center gap-2 px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors duration-200"
    >
      <RefreshCw className="w-4 h-4" />
      Refresh
    </motion.button>
  );

  return (
    <VendorLayout
      title="Orders Management"
      subtitle="Manage and track your restaurant orders"
      headerActions={headerActions}
    >

              {/* Filters */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                className="bg-white rounded-2xl shadow-sm p-6 mb-8"
              >
                <div className="flex flex-col sm:flex-row gap-4">
                  {/* Search */}
                  <div className="flex-1 relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Search className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      type="text"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                      placeholder="Search orders by order number or customer..."
                    />
                  </div>

                  {/* Status Filter */}
                  <select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    className="px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  >
                    {statusOptions.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
              </motion.div>

              {/* Orders List */}
              {isLoading ? (
                <div className="space-y-6">
                  {[...Array(3)].map((_, index) => (
                    <div key={index} className="animate-pulse">
                      <div className="bg-white rounded-2xl p-6">
                        <div className="flex items-center justify-between mb-4">
                          <div className="h-6 bg-gray-200 rounded w-1/4"></div>
                          <div className="h-6 bg-gray-200 rounded w-20"></div>
                        </div>
                        <div className="space-y-3">
                          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : error ? (
                <div className="text-center py-12">
                  <div className="text-gray-500 mb-4">
                    <Package className="w-16 h-16 mx-auto mb-4 opacity-50" />
                    <p className="text-lg">Something went wrong</p>
                    <p className="text-sm">Please try again later</p>
                  </div>
                </div>
              ) : orders.length === 0 ? (
                <div className="text-center py-12">
                  <div className="text-gray-500 mb-4">
                    <Package className="w-16 h-16 mx-auto mb-4 opacity-50" />
                    <p className="text-lg">No orders found</p>
                    <p className="text-sm">Orders will appear here when customers place them</p>
                  </div>
                </div>
              ) : (
                <div className="space-y-6">
                  {orders.map((order: any, index: number) => {
                    const StatusIcon = getStatusIcon(order.status);
                    const nextStatus = getNextStatus(order.status);
                    const nextStatusLabel = getNextStatusLabel(order.status);
                    
                    return (
                      <motion.div
                        key={order._id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.5, delay: index * 0.1 }}
                        className="bg-white rounded-2xl shadow-sm p-6"
                      >
                        {/* Order Header */}
                        <div className="flex items-center justify-between mb-6">
                          <div className="flex items-center gap-4">
                            <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl flex items-center justify-center">
                              <StatusIcon className="w-6 h-6 text-white" />
                            </div>
                            <div>
                              <h3 className="text-lg font-bold text-gray-900">
                                Order #{order.orderNumber}
                              </h3>
                              <p className="text-sm text-gray-500">
                                {new Date(order.createdAt).toLocaleString()}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center gap-4">
                            <span className={`px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(order.status)}`}>
                              {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                            </span>
                            <span className="text-lg font-bold text-gray-900">
                              {formatCurrency(order.pricing.total)}
                            </span>
                          </div>
                        </div>

                        {/* Customer Info */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                          <div>
                            <h4 className="font-medium text-gray-900 mb-2">Customer Details</h4>
                            <div className="space-y-2 text-sm text-gray-600">
                              <div className="flex items-center gap-2">
                                <span className="font-medium">{order.customer.name}</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <Phone className="w-4 h-4" />
                                <span>{order.customer.phone}</span>
                              </div>
                            </div>
                          </div>
                          <div>
                            <h4 className="font-medium text-gray-900 mb-2">Delivery Address</h4>
                            <div className="flex items-start gap-2 text-sm text-gray-600">
                              <MapPin className="w-4 h-4 mt-0.5 flex-shrink-0" />
                              <div>
                                <p>{order.deliveryAddress.street}</p>
                                <p>{order.deliveryAddress.city}, {order.deliveryAddress.state} {order.deliveryAddress.zipCode}</p>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Order Items */}
                        <div className="mb-6">
                          <h4 className="font-medium text-gray-900 mb-3">Order Items</h4>
                          <div className="space-y-3">
                            {order.items.map((item: any, itemIndex: number) => (
                              <div key={itemIndex} className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                                <div className="relative w-12 h-12 flex-shrink-0">
                                  <Image
                                    src={getOptimizedImageUrl(item.food.images?.[0] || '/images/food-placeholder.jpg')}
                                    alt={item.food.name}
                                    fill
                                    className="object-cover rounded-lg"
                                  />
                                </div>
                                <div className="flex-1">
                                  <h5 className="font-medium text-gray-900">{item.food.name}</h5>
                                  {item.variant && (
                                    <p className="text-sm text-gray-500">{item.variant.name}</p>
                                  )}
                                  {item.addons.length > 0 && (
                                    <p className="text-sm text-gray-500">
                                      +{item.addons.map((a: any) => a.name).join(', ')}
                                    </p>
                                  )}
                                  {item.specialInstructions && (
                                    <p className="text-sm text-orange-600 italic">
                                      Note: {item.specialInstructions}
                                    </p>
                                  )}
                                </div>
                                <div className="text-right">
                                  <p className="font-medium text-gray-900">
                                    {item.quantity}x {formatCurrency(item.price)}
                                  </p>
                                  <p className="text-sm text-gray-500">
                                    {formatCurrency(item.totalPrice)}
                                  </p>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>

                        {/* Order Actions */}
                        <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                          <div className="flex items-center gap-4 text-sm text-gray-600">
                            <div className="flex items-center gap-1">
                              <DollarSign className="w-4 h-4" />
                              <span>{order.paymentMethod.charAt(0).toUpperCase() + order.paymentMethod.slice(1)}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Calendar className="w-4 h-4" />
                              <span>
                                {new Date(order.createdAt).toLocaleDateString()}
                              </span>
                            </div>
                          </div>
                          
                          <div className="flex items-center gap-3">
                            {nextStatus && (
                              <motion.button
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                                onClick={() => handleStatusUpdate(order._id, nextStatus)}
                                disabled={updateOrderMutation.isPending}
                                className="px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                              >
                                {updateOrderMutation.isPending ? 'Updating...' : nextStatusLabel}
                              </motion.button>
                            )}
                            
                            {order.status === 'pending' && (
                              <motion.button
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                                onClick={() => handleStatusUpdate(order._id, 'cancelled')}
                                disabled={updateOrderMutation.isPending}
                                className="px-4 py-2 border border-red-300 text-red-600 rounded-lg hover:bg-red-50 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                              >
                                Cancel Order
                              </motion.button>
                            )}
                          </div>
                        </div>
                      </motion.div>
                    );
                  })}
                </div>
              )}
    </VendorLayout>
  );
};

export default VendorOrdersPage;
