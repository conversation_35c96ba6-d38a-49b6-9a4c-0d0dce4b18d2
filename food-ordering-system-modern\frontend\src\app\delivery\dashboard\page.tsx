'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { useQuery } from '@tanstack/react-query';
import { 
  Truck, 
  Package,
  Clock,
  DollarSign,
  MapPin,
  Navigation,
  CheckCircle,
  AlertCircle,
  Star,
  Phone
} from 'lucide-react';
import DeliverySidebar from '@/components/DeliverySidebar';
import { deliveryAPI } from '@/lib/api';

const DeliveryDashboard = () => {
  const [selectedStatus, setSelectedStatus] = useState('available');

  // Fetch delivery dashboard data
  const { data: dashboardData, isLoading } = useQuery({
    queryKey: ['delivery-dashboard'],
    queryFn: () => deliveryAPI.getDashboard(),
    refetchInterval: 30000, // Refresh every 30 seconds
  });

  // Extract stats from API data
  const stats = dashboardData?.data?.stats || {
    todayDeliveries: 0,
    todayEarnings: 0,
    averageRating: 0,
    completionRate: 0
  };

  // Get orders from API data
  const orders = dashboardData?.data?.orders || [];

  const statsCards = [
    {
      title: 'Today\'s Deliveries',
      value: stats.todayDeliveries,
      icon: Package,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    },
    {
      title: 'Today\'s Earnings',
      value: `₹${stats.todayEarnings}`,
      icon: DollarSign,
      color: 'text-green-600',
      bgColor: 'bg-green-100'
    },
    {
      title: 'Average Rating',
      value: stats.averageRating.toFixed(1),
      icon: Star,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100'
    },
    {
      title: 'Completion Rate',
      value: `${stats.completionRate.toFixed(1)}%`,
      icon: CheckCircle,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100'
    }
  ];

  const statusOptions = [
    { value: 'available', label: 'Available', color: 'bg-green-500' },
    { value: 'busy', label: 'Busy', color: 'bg-orange-500' },
    { value: 'offline', label: 'Offline', color: 'bg-gray-500' }
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case 'ready_for_pickup':
        return 'bg-orange-100 text-orange-800';
      case 'picked_up':
        return 'bg-blue-100 text-blue-800';
      case 'delivered':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleAcceptOrder = (orderId) => {
    console.log('Accept order:', orderId);
    // Implement accept order functionality
  };

  const handleStartDelivery = (orderId) => {
    console.log('Start delivery:', orderId);
    // Implement start delivery functionality
  };

  const handleCompleteDelivery = (orderId) => {
    console.log('Complete delivery:', orderId);
    // Implement complete delivery functionality
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex">
        <DeliverySidebar />
        <div className="flex-1 p-8">
          <div className="max-w-6xl mx-auto">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                {[...Array(4)].map((_, i) => (
                  <div key={i} className="h-32 bg-gray-200 rounded-lg"></div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex">
      <DeliverySidebar />
      
      <div className="flex-1 p-8">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <Truck className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Delivery Dashboard</h1>
                <p className="text-gray-600">Manage your deliveries and track earnings</p>
              </div>
            </div>

            {/* Status Toggle */}
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-gray-700">Status:</span>
              <div className="flex bg-white rounded-lg p-1 shadow-sm border">
                {statusOptions.map((status) => (
                  <button
                    key={status.value}
                    onClick={() => setSelectedStatus(status.value)}
                    className={`px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 flex items-center gap-2 ${
                      selectedStatus === status.value
                        ? 'bg-gray-100 text-gray-900'
                        : 'text-gray-600 hover:text-gray-900'
                    }`}
                  >
                    <div className={`w-2 h-2 rounded-full ${status.color}`}></div>
                    {status.label}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {statsCards.map((stat, index) => (
              <motion.div
                key={stat.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-white rounded-2xl p-6 shadow-md hover:shadow-lg transition-shadow duration-200"
              >
                <div className="flex items-center justify-between mb-4">
                  <div className={`w-12 h-12 ${stat.bgColor} rounded-lg flex items-center justify-center`}>
                    <stat.icon className={`w-6 h-6 ${stat.color}`} />
                  </div>
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-1">{stat.value}</h3>
                  <p className="text-gray-600 text-sm">{stat.title}</p>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Available Orders */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="bg-white rounded-2xl p-6 shadow-md mb-6"
          >
            <h2 className="text-lg font-semibold text-gray-900 mb-6">Available Orders</h2>
            
            <div className="space-y-4">
              {orders.map((order, index) => (
                <motion.div
                  key={order.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="border border-gray-200 rounded-lg p-4 hover:border-blue-300 transition-colors duration-200"
                >
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                        <Package className="w-5 h-5 text-orange-600" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900">{order.id}</h3>
                        <p className="text-sm text-gray-600">{order.restaurant}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-gray-900">₹{order.amount}</p>
                      <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                        {order.status.replace('_', ' ').toUpperCase()}
                      </span>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div className="flex items-center gap-2">
                      <MapPin className="w-4 h-4 text-gray-400" />
                      <span className="text-sm text-gray-600">{order.address}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Navigation className="w-4 h-4 text-gray-400" />
                      <span className="text-sm text-gray-600">{order.distance}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="w-4 h-4 text-gray-400" />
                      <span className="text-sm text-gray-600">ETA: {order.estimatedTime}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Phone className="w-4 h-4 text-gray-400" />
                      <span className="text-sm text-gray-600">{order.phone}</span>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    {order.status === 'ready_for_pickup' && (
                      <button
                        onClick={() => handleAcceptOrder(order.id)}
                        className="flex-1 bg-blue-500 text-white py-2 px-4 rounded-lg hover:bg-blue-600 transition-colors duration-200"
                      >
                        Accept Order
                      </button>
                    )}
                    {order.status === 'picked_up' && (
                      <button
                        onClick={() => handleCompleteDelivery(order.id)}
                        className="flex-1 bg-green-500 text-white py-2 px-4 rounded-lg hover:bg-green-600 transition-colors duration-200"
                      >
                        Mark as Delivered
                      </button>
                    )}
                    <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                      View Details
                    </button>
                  </div>
                </motion.div>
              ))}
            </div>

            {orders.length === 0 && (
              <div className="text-center py-12">
                <Package className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">No orders available</h3>
                <p className="text-gray-600">Check back later for new delivery opportunities.</p>
              </div>
            )}
          </motion.div>

          {/* Quick Actions */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
            className="grid grid-cols-1 md:grid-cols-3 gap-6"
          >
            <div className="bg-white rounded-2xl p-6 shadow-md text-center">
              <MapPin className="w-12 h-12 text-blue-500 mx-auto mb-4" />
              <h3 className="font-semibold text-gray-900 mb-2">View Map</h3>
              <p className="text-gray-600 text-sm mb-4">See all delivery locations on map</p>
              <button className="w-full bg-blue-500 text-white py-2 px-4 rounded-lg hover:bg-blue-600 transition-colors duration-200">
                Open Map
              </button>
            </div>

            <div className="bg-white rounded-2xl p-6 shadow-md text-center">
              <Clock className="w-12 h-12 text-green-500 mx-auto mb-4" />
              <h3 className="font-semibold text-gray-900 mb-2">Delivery History</h3>
              <p className="text-gray-600 text-sm mb-4">View your past deliveries</p>
              <button className="w-full bg-green-500 text-white py-2 px-4 rounded-lg hover:bg-green-600 transition-colors duration-200">
                View History
              </button>
            </div>

            <div className="bg-white rounded-2xl p-6 shadow-md text-center">
              <DollarSign className="w-12 h-12 text-orange-500 mx-auto mb-4" />
              <h3 className="font-semibold text-gray-900 mb-2">Earnings Report</h3>
              <p className="text-gray-600 text-sm mb-4">Track your daily earnings</p>
              <button className="w-full bg-orange-500 text-white py-2 px-4 rounded-lg hover:bg-orange-600 transition-colors duration-200">
                View Earnings
              </button>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default DeliveryDashboard;
