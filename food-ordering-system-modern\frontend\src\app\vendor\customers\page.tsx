'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useQuery } from '@tanstack/react-query';
import { 
  Users, 
  Search,
  Filter,
  Star,
  Package,
  DollarSign,
  Calendar,
  Phone,
  Mail,
  MapPin,
  TrendingUp
} from 'lucide-react';
import VendorLayout from '@/components/VendorLayout';
import { vendorAPI } from '@/lib/api';

const VendorCustomers = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('orders');

  // Fetch customers data
  const { data: customersData, isLoading } = useQuery({
    queryKey: ['vendor-customers'],
    queryFn: () => vendorAPI.getCustomers(),
  });

  const customers = customersData?.data || [];


  const sortOptions = [
    { value: 'orders', label: 'Most Orders' },
    { value: 'spent', label: 'Highest Spent' },
    { value: 'rating', label: 'Highest Rating' },
    { value: 'recent', label: 'Most Recent' },
  ];

  const filteredAndSortedCustomers = customers
    .filter(customer =>
      customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (customer.phone && customer.phone.includes(searchTerm))
    )
    .sort((a, b) => {
      switch (sortBy) {
        case 'orders':
          return b.totalOrders - a.totalOrders;
        case 'spent':
          return b.totalSpent - a.totalSpent;
        case 'rating':
          return (b.averageRating || 0) - (a.averageRating || 0);
        case 'recent':
          return new Date(b.lastOrder).getTime() - new Date(a.lastOrder).getTime();
        default:
          return 0;
      }
    });

  const totalCustomers = displayCustomers.length;
  const totalRevenue = displayCustomers.reduce((sum, customer) => sum + customer.totalSpent, 0);
  const averageOrderValue = totalRevenue / displayCustomers.reduce((sum, customer) => sum + customer.totalOrders, 0);
  const repeatCustomers = displayCustomers.filter(customer => customer.totalOrders > 1).length;

  const renderStars = (rating) => {
    return [...Array(5)].map((_, index) => (
      <Star
        key={index}
        className={`w-4 h-4 ${
          index < Math.floor(rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ));
  };

  return (
    <div className="min-h-screen bg-gray-50 flex">
      <VendorSidebar />
      
      <div className="flex-1 p-8">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                <Users className="w-5 h-5 text-orange-600" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Customers</h1>
                <p className="text-gray-600">Manage your customer relationships</p>
              </div>
            </div>
          </div>

          {/* Stats Overview */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="bg-white rounded-2xl p-6 shadow-md"
            >
              <div className="flex items-center gap-3 mb-2">
                <Users className="w-6 h-6 text-blue-500" />
                <h3 className="text-lg font-semibold text-gray-900">Total Customers</h3>
              </div>
              <p className="text-3xl font-bold text-gray-900">{totalCustomers}</p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="bg-white rounded-2xl p-6 shadow-md"
            >
              <div className="flex items-center gap-3 mb-2">
                <DollarSign className="w-6 h-6 text-green-500" />
                <h3 className="text-lg font-semibold text-gray-900">Total Revenue</h3>
              </div>
              <p className="text-3xl font-bold text-gray-900">₹{totalRevenue.toLocaleString()}</p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="bg-white rounded-2xl p-6 shadow-md"
            >
              <div className="flex items-center gap-3 mb-2">
                <Package className="w-6 h-6 text-orange-500" />
                <h3 className="text-lg font-semibold text-gray-900">Avg Order Value</h3>
              </div>
              <p className="text-3xl font-bold text-gray-900">₹{Math.round(averageOrderValue)}</p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="bg-white rounded-2xl p-6 shadow-md"
            >
              <div className="flex items-center gap-3 mb-2">
                <TrendingUp className="w-6 h-6 text-purple-500" />
                <h3 className="text-lg font-semibold text-gray-900">Repeat Customers</h3>
              </div>
              <p className="text-3xl font-bold text-gray-900">{repeatCustomers}</p>
              <p className="text-sm text-gray-600">{Math.round((repeatCustomers / totalCustomers) * 100)}% retention</p>
            </motion.div>
          </div>

          {/* Search and Filter */}
          <div className="bg-white rounded-2xl p-6 shadow-md mb-6">
            <div className="flex flex-col md:flex-row gap-4">
              {/* Search */}
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    type="text"
                    placeholder="Search customers by name, email, or phone..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  />
                </div>
              </div>

              {/* Sort */}
              <div className="flex items-center gap-2">
                <Filter className="w-5 h-5 text-gray-400" />
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                >
                  {sortOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Customers List */}
          <div className="space-y-6">
            {filteredAndSortedCustomers.map((customer, index) => (
              <motion.div
                key={customer.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-white rounded-2xl p-6 shadow-md hover:shadow-lg transition-shadow duration-200"
              >
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {/* Customer Info */}
                  <div className="lg:col-span-1">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center">
                        <span className="text-orange-600 font-semibold text-lg">
                          {customer.name.charAt(0)}
                        </span>
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900">{customer.name}</h3>
                        <div className="flex items-center gap-1">
                          {renderStars(customer.averageRating)}
                          <span className="text-sm text-gray-500 ml-1">
                            ({customer.averageRating})
                          </span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="space-y-2 text-sm text-gray-600">
                      <div className="flex items-center gap-2">
                        <Mail className="w-4 h-4" />
                        <span>{customer.email}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Phone className="w-4 h-4" />
                        <span>{customer.phone}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <MapPin className="w-4 h-4" />
                        <span>{customer.location}</span>
                      </div>
                    </div>
                  </div>

                  {/* Order Stats */}
                  <div className="lg:col-span-1">
                    <h4 className="font-semibold text-gray-900 mb-3">Order Statistics</h4>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center p-3 bg-blue-50 rounded-lg">
                        <p className="text-2xl font-bold text-blue-600">{customer.totalOrders}</p>
                        <p className="text-sm text-blue-600">Total Orders</p>
                      </div>
                      <div className="text-center p-3 bg-green-50 rounded-lg">
                        <p className="text-2xl font-bold text-green-600">₹{customer.totalSpent.toLocaleString()}</p>
                        <p className="text-sm text-green-600">Total Spent</p>
                      </div>
                    </div>
                    <div className="mt-3 text-sm text-gray-600">
                      <div className="flex items-center gap-2">
                        <Calendar className="w-4 h-4" />
                        <span>Last order: {customer.lastOrder}</span>
                      </div>
                    </div>
                  </div>

                  {/* Favorite Items */}
                  <div className="lg:col-span-1">
                    <h4 className="font-semibold text-gray-900 mb-3">Favorite Items</h4>
                    <div className="space-y-2">
                      {customer.favoriteItems.map((item, idx) => (
                        <span
                          key={idx}
                          className="inline-block px-3 py-1 bg-orange-50 text-orange-700 rounded-full text-sm mr-2 mb-2"
                        >
                          {item}
                        </span>
                      ))}
                    </div>
                    <div className="mt-4">
                      <button className="text-orange-600 hover:text-orange-700 text-sm font-medium">
                        View Order History
                      </button>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {filteredAndSortedCustomers.length === 0 && (
            <div className="text-center py-12">
              <Users className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No customers found</h3>
              <p className="text-gray-600">Try adjusting your search terms.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default VendorCustomers;
