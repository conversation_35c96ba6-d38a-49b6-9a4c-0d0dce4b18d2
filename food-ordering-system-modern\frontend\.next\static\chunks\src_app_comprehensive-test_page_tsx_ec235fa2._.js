(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/app/comprehensive-test/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = /*#__PURE__*/ __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2d$big$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/circle-check-big.js [app-client] (ecmascript) <export default as CheckCircle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__XCircle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/circle-x.js [app-client] (ecmascript) <export default as XCircle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/clock.js [app-client] (ecmascript) <export default as Clock>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertCircle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/circle-alert.js [app-client] (ecmascript) <export default as AlertCircle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$upload$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Upload$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/upload.js [app-client] (ecmascript) <export default as Upload>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$server$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Server$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/server.js [app-client] (ecmascript) <export default as Server>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$globe$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Globe$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/globe.js [app-client] (ecmascript) <export default as Globe>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$database$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Database$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/database.js [app-client] (ecmascript) <export default as Database>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__User$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/user.js [app-client] (ecmascript) <export default as User>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$shopping$2d$cart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ShoppingCart$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/shopping-cart.js [app-client] (ecmascript) <export default as ShoppingCart>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$settings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Settings$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/settings.js [app-client] (ecmascript) <export default as Settings>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bell$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Bell$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/bell.js [app-client] (ecmascript) <export default as Bell>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$truck$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Truck$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/truck.js [app-client] (ecmascript) <export default as Truck>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$shield$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Shield$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/shield.js [app-client] (ecmascript) <export default as Shield>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$play$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Play$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/play.js [app-client] (ecmascript) <export default as Play>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$pause$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Pause$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/pause.js [app-client] (ecmascript) <export default as Pause>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$rotate$2d$ccw$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__RotateCcw$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/rotate-ccw.js [app-client] (ecmascript) <export default as RotateCcw>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$external$2d$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ExternalLink$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/external-link.js [app-client] (ecmascript) <export default as ExternalLink>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$text$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FileText$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/file-text.js [app-client] (ecmascript) <export default as FileText>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$credit$2d$card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CreditCard$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/credit-card.js [app-client] (ecmascript) <export default as CreditCard>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
const ComprehensiveTestPage = ()=>{
    _s();
    const [results, setResults] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [isRunning, setIsRunning] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [selectedFile, setSelectedFile] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [activeTab, setActiveTab] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('apis');
    const [selectedCategory, setSelectedCategory] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('all');
    const [testProgress, setTestProgress] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    // Comprehensive API Tests
    const apiTests = [
        // Authentication APIs
        {
            name: 'Health Check',
            category: 'System',
            endpoint: '/api/health',
            method: 'GET',
            description: 'Check if backend is running',
            expectedStatus: 200
        },
        {
            name: 'Get Current User',
            category: 'Auth',
            endpoint: '/api/auth/me',
            method: 'GET',
            requiresAuth: true,
            description: 'Get authenticated user info'
        },
        {
            name: 'Refresh Token',
            category: 'Auth',
            endpoint: '/api/auth/refresh',
            method: 'POST',
            description: 'Refresh authentication token'
        },
        {
            name: 'Logout',
            category: 'Auth',
            endpoint: '/api/auth/logout',
            method: 'POST',
            requiresAuth: true,
            description: 'User logout'
        },
        // Restaurant APIs
        {
            name: 'Get All Restaurants',
            category: 'Restaurants',
            endpoint: '/api/restaurants',
            method: 'GET',
            description: 'Fetch all restaurants'
        },
        {
            name: 'Get Restaurant by ID',
            category: 'Restaurants',
            endpoint: '/api/restaurants/test-id',
            method: 'GET',
            description: 'Fetch specific restaurant'
        },
        {
            name: 'Search Restaurants',
            category: 'Restaurants',
            endpoint: '/api/restaurants?search=pizza',
            method: 'GET',
            description: 'Search restaurants by query'
        },
        {
            name: 'Filter by Cuisine',
            category: 'Restaurants',
            endpoint: '/api/restaurants?cuisine=italian',
            method: 'GET',
            description: 'Filter restaurants by cuisine'
        },
        {
            name: 'Filter by Rating',
            category: 'Restaurants',
            endpoint: '/api/restaurants?minRating=4',
            method: 'GET',
            description: 'Filter restaurants by rating'
        },
        // Food APIs
        {
            name: 'Get All Foods',
            category: 'Foods',
            endpoint: '/api/foods',
            method: 'GET',
            description: 'Fetch all food items'
        },
        {
            name: 'Get Foods by Restaurant',
            category: 'Foods',
            endpoint: '/api/foods/restaurant/test-id',
            method: 'GET',
            description: 'Get foods for specific restaurant'
        },
        {
            name: 'Search Foods',
            category: 'Foods',
            endpoint: '/api/foods?search=burger',
            method: 'GET',
            description: 'Search food items'
        },
        {
            name: 'Filter by Category',
            category: 'Foods',
            endpoint: '/api/foods?category=main-course',
            method: 'GET',
            description: 'Filter foods by category'
        },
        {
            name: 'Filter by Dietary',
            category: 'Foods',
            endpoint: '/api/foods?isVegetarian=true',
            method: 'GET',
            description: 'Filter by dietary preferences'
        },
        // Vendor APIs
        {
            name: 'Get Vendor Restaurant',
            category: 'Vendor',
            endpoint: '/api/vendors/restaurant',
            method: 'GET',
            requiresAuth: true,
            description: 'Get vendor restaurant info'
        },
        {
            name: 'Get Vendor Foods',
            category: 'Vendor',
            endpoint: '/api/vendors/foods',
            method: 'GET',
            requiresAuth: true,
            description: 'Get vendor food items'
        },
        {
            name: 'Get Vendor Orders',
            category: 'Vendor',
            endpoint: '/api/vendors/orders',
            method: 'GET',
            requiresAuth: true,
            description: 'Get vendor orders'
        },
        {
            name: 'Get Recent Orders',
            category: 'Vendor',
            endpoint: '/api/vendors/orders/recent?limit=5',
            method: 'GET',
            requiresAuth: true,
            description: 'Get recent orders'
        },
        {
            name: 'Get Order Stats',
            category: 'Vendor',
            endpoint: '/api/vendors/orders/stats',
            method: 'GET',
            requiresAuth: true,
            description: 'Get order statistics'
        },
        {
            name: 'Get Analytics',
            category: 'Vendor',
            endpoint: '/api/vendors/analytics?period=7d',
            method: 'GET',
            requiresAuth: true,
            description: 'Get 7-day analytics'
        },
        {
            name: 'Get Monthly Analytics',
            category: 'Vendor',
            endpoint: '/api/vendors/analytics?period=30d',
            method: 'GET',
            requiresAuth: true,
            description: 'Get 30-day analytics'
        },
        {
            name: 'Get Reviews',
            category: 'Vendor',
            endpoint: '/api/vendors/reviews?rating=all',
            method: 'GET',
            requiresAuth: true,
            description: 'Get all reviews'
        },
        {
            name: 'Get 5-Star Reviews',
            category: 'Vendor',
            endpoint: '/api/vendors/reviews?rating=5',
            method: 'GET',
            requiresAuth: true,
            description: 'Get 5-star reviews'
        },
        {
            name: 'Get Customers',
            category: 'Vendor',
            endpoint: '/api/vendors/customers',
            method: 'GET',
            requiresAuth: true,
            description: 'Get customer list'
        },
        {
            name: 'Get Notifications',
            category: 'Vendor',
            endpoint: '/api/vendors/notifications',
            method: 'GET',
            requiresAuth: true,
            description: 'Get notifications'
        },
        // Upload APIs
        {
            name: 'Upload Single Image',
            category: 'Upload',
            endpoint: '/api/upload/image',
            method: 'POST',
            requiresAuth: true,
            description: 'Upload single image file'
        },
        {
            name: 'Upload Multiple Images',
            category: 'Upload',
            endpoint: '/api/upload/images',
            method: 'POST',
            requiresAuth: true,
            description: 'Upload multiple images'
        },
        {
            name: 'Upload Food Images',
            category: 'Upload',
            endpoint: '/api/upload/food/images',
            method: 'POST',
            requiresAuth: true,
            description: 'Upload food images'
        },
        {
            name: 'Upload Restaurant Logo',
            category: 'Upload',
            endpoint: '/api/upload/restaurant/logo',
            method: 'POST',
            requiresAuth: true,
            description: 'Upload restaurant logo'
        },
        {
            name: 'Upload Restaurant Banner',
            category: 'Upload',
            endpoint: '/api/upload/restaurant/banner',
            method: 'POST',
            requiresAuth: true,
            description: 'Upload restaurant banner'
        },
        // Order APIs
        {
            name: 'Get All Orders',
            category: 'Orders',
            endpoint: '/api/orders',
            method: 'GET',
            requiresAuth: true,
            description: 'Get all orders'
        },
        {
            name: 'Get Order by ID',
            category: 'Orders',
            endpoint: '/api/orders/test-id',
            method: 'GET',
            requiresAuth: true,
            description: 'Get specific order'
        },
        {
            name: 'Get Order History',
            category: 'Orders',
            endpoint: '/api/orders/history',
            method: 'GET',
            requiresAuth: true,
            description: 'Get order history'
        },
        // User APIs
        {
            name: 'Get User Profile',
            category: 'Users',
            endpoint: '/api/users/profile',
            method: 'GET',
            requiresAuth: true,
            description: 'Get user profile'
        },
        {
            name: 'Get All Users',
            category: 'Users',
            endpoint: '/api/users',
            method: 'GET',
            requiresAuth: true,
            description: 'Get all users (admin)'
        },
        // Admin APIs
        {
            name: 'Admin Dashboard',
            category: 'Admin',
            endpoint: '/api/admin/dashboard',
            method: 'GET',
            requiresAuth: true,
            description: 'Get admin dashboard data'
        },
        {
            name: 'Admin Users',
            category: 'Admin',
            endpoint: '/api/admin/users',
            method: 'GET',
            requiresAuth: true,
            description: 'Get all users for admin'
        },
        {
            name: 'Admin Restaurants',
            category: 'Admin',
            endpoint: '/api/admin/restaurants',
            method: 'GET',
            requiresAuth: true,
            description: 'Get all restaurants for admin'
        },
        {
            name: 'Admin Orders',
            category: 'Admin',
            endpoint: '/api/admin/orders',
            method: 'GET',
            requiresAuth: true,
            description: 'Get all orders for admin'
        },
        // Delivery APIs
        {
            name: 'Delivery Orders',
            category: 'Delivery',
            endpoint: '/api/delivery/orders',
            method: 'GET',
            requiresAuth: true,
            description: 'Get delivery orders'
        },
        {
            name: 'Available Orders',
            category: 'Delivery',
            endpoint: '/api/delivery/orders/available',
            method: 'GET',
            requiresAuth: true,
            description: 'Get available delivery orders'
        },
        // Payment APIs
        {
            name: 'Payment Methods',
            category: 'Payment',
            endpoint: '/api/payments/methods',
            method: 'GET',
            requiresAuth: true,
            description: 'Get payment methods'
        },
        {
            name: 'Payment History',
            category: 'Payment',
            endpoint: '/api/payments/history',
            method: 'GET',
            requiresAuth: true,
            description: 'Get payment history'
        },
        // Notification APIs
        {
            name: 'Get Notifications',
            category: 'Notifications',
            endpoint: '/api/notifications',
            method: 'GET',
            requiresAuth: true,
            description: 'Get user notifications'
        },
        {
            name: 'Mark as Read',
            category: 'Notifications',
            endpoint: '/api/notifications/read',
            method: 'POST',
            requiresAuth: true,
            description: 'Mark notifications as read'
        }
    ];
    // Comprehensive Page Tests
    const pageTests = [
        // Public Pages
        {
            name: 'Home Page',
            category: 'Public',
            path: '/',
            description: 'Landing page with restaurant listings',
            features: [
                'Restaurant grid',
                'Search functionality',
                'Filter options',
                'Hero section',
                'Navigation'
            ]
        },
        {
            name: 'Restaurants Page',
            category: 'Public',
            path: '/restaurants',
            description: 'Restaurant browsing and filtering',
            features: [
                'Restaurant cards',
                'Search bar',
                'Cuisine filters',
                'Rating filters',
                'Pagination'
            ]
        },
        {
            name: 'Restaurant Details',
            category: 'Public',
            path: '/restaurants/test-id',
            description: 'Individual restaurant page',
            features: [
                'Restaurant info',
                'Menu display',
                'Reviews',
                'Add to cart',
                'Image gallery'
            ]
        },
        {
            name: 'Login Page',
            category: 'Auth',
            path: '/auth/login',
            description: 'User authentication',
            features: [
                'Login form',
                'Role selection',
                'Remember me',
                'Forgot password',
                'Social login'
            ]
        },
        {
            name: 'Register Page',
            category: 'Auth',
            path: '/auth/register',
            description: 'User registration',
            features: [
                'Registration form',
                'Email verification',
                'Terms acceptance',
                'Role selection'
            ]
        },
        // Customer Pages
        {
            name: 'Customer Dashboard',
            category: 'Customer',
            path: '/customer/dashboard',
            description: 'Customer main dashboard',
            requiresAuth: true,
            userType: 'customer',
            features: [
                'Order history',
                'Favorite restaurants',
                'Quick reorder',
                'Profile summary',
                'Recommendations'
            ]
        },
        {
            name: 'Customer Orders',
            category: 'Customer',
            path: '/customer/orders',
            description: 'Order history and tracking',
            requiresAuth: true,
            userType: 'customer',
            features: [
                'Order list',
                'Order tracking',
                'Reorder button',
                'Order details',
                'Cancel order'
            ]
        },
        {
            name: 'Customer Profile',
            category: 'Customer',
            path: '/customer/profile',
            description: 'Profile management',
            requiresAuth: true,
            userType: 'customer',
            features: [
                'Personal info',
                'Address management',
                'Payment methods',
                'Preferences',
                'Avatar upload'
            ]
        },
        {
            name: 'Shopping Cart',
            category: 'Customer',
            path: '/customer/cart',
            description: 'Shopping cart management',
            requiresAuth: true,
            userType: 'customer',
            features: [
                'Item list',
                'Quantity controls',
                'Remove items',
                'Total calculation',
                'Checkout button'
            ]
        },
        // Vendor Pages
        {
            name: 'Vendor Dashboard',
            category: 'Vendor',
            path: '/vendor/dashboard',
            description: 'Vendor main dashboard with analytics',
            requiresAuth: true,
            userType: 'vendor',
            features: [
                'Sales overview',
                'Recent orders',
                'Analytics charts',
                'Quick actions',
                'Notifications'
            ]
        },
        {
            name: 'Vendor Menu',
            category: 'Vendor',
            path: '/vendor/menu',
            description: 'Menu management with image upload',
            requiresAuth: true,
            userType: 'vendor',
            features: [
                'Food list',
                'Add food modal',
                'Image upload',
                'Category filters',
                'Edit/Delete items'
            ]
        },
        {
            name: 'Vendor Restaurant',
            category: 'Vendor',
            path: '/vendor/restaurant',
            description: 'Restaurant profile management',
            requiresAuth: true,
            userType: 'vendor',
            features: [
                'Restaurant info',
                'Logo upload',
                'Banner upload',
                'Gallery management',
                'Operating hours'
            ]
        },
        {
            name: 'Vendor Orders',
            category: 'Vendor',
            path: '/vendor/orders',
            description: 'Order management and processing',
            requiresAuth: true,
            userType: 'vendor',
            features: [
                'Order list',
                'Status updates',
                'Order details',
                'Print receipts',
                'Filter orders'
            ]
        },
        {
            name: 'Vendor Analytics',
            category: 'Vendor',
            path: '/vendor/analytics',
            description: 'Sales analytics and reports',
            requiresAuth: true,
            userType: 'vendor',
            features: [
                'Sales charts',
                'Period filters',
                'Revenue metrics',
                'Popular items',
                'Export data'
            ]
        },
        {
            name: 'Vendor Reviews',
            category: 'Vendor',
            path: '/vendor/reviews',
            description: 'Customer reviews and ratings',
            requiresAuth: true,
            userType: 'vendor',
            features: [
                'Review list',
                'Rating filters',
                'Response system',
                'Review analytics',
                'Export reviews'
            ]
        },
        {
            name: 'Vendor Customers',
            category: 'Vendor',
            path: '/vendor/customers',
            description: 'Customer management',
            requiresAuth: true,
            userType: 'vendor',
            features: [
                'Customer list',
                'Order history',
                'Customer insights',
                'Contact info',
                'Loyalty tracking'
            ]
        },
        {
            name: 'Vendor Notifications',
            category: 'Vendor',
            path: '/vendor/notifications',
            description: 'Notification center',
            requiresAuth: true,
            userType: 'vendor',
            features: [
                'Notification list',
                'Mark as read',
                'Filter by type',
                'Notification settings',
                'Real-time updates'
            ]
        },
        {
            name: 'Vendor Settings',
            category: 'Vendor',
            path: '/vendor/settings',
            description: 'Account and business settings',
            requiresAuth: true,
            userType: 'vendor',
            features: [
                'Account info',
                'Business settings',
                'Notification preferences',
                'Payment settings',
                'Security'
            ]
        },
        // Admin Pages
        {
            name: 'Admin Dashboard',
            category: 'Admin',
            path: '/admin/dashboard',
            description: 'System administration dashboard',
            requiresAuth: true,
            userType: 'admin',
            features: [
                'System overview',
                'User statistics',
                'Revenue metrics',
                'Recent activity',
                'System health'
            ]
        },
        {
            name: 'Admin Users',
            category: 'Admin',
            path: '/admin/users',
            description: 'User management',
            requiresAuth: true,
            userType: 'admin',
            features: [
                'User list',
                'User details',
                'Role management',
                'Account status',
                'User actions'
            ]
        },
        {
            name: 'Admin Restaurants',
            category: 'Admin',
            path: '/admin/restaurants',
            description: 'Restaurant management',
            requiresAuth: true,
            userType: 'admin',
            features: [
                'Restaurant list',
                'Approval system',
                'Restaurant details',
                'Performance metrics',
                'Actions'
            ]
        },
        {
            name: 'Admin Orders',
            category: 'Admin',
            path: '/admin/orders',
            description: 'Order monitoring and management',
            requiresAuth: true,
            userType: 'admin',
            features: [
                'Order overview',
                'Order details',
                'Dispute resolution',
                'Refund management',
                'Analytics'
            ]
        },
        // Delivery Pages
        {
            name: 'Delivery Dashboard',
            category: 'Delivery',
            path: '/delivery/dashboard',
            description: 'Delivery partner dashboard',
            requiresAuth: true,
            userType: 'delivery',
            features: [
                'Available orders',
                'Current deliveries',
                'Earnings',
                'Performance metrics',
                'Map integration'
            ]
        },
        {
            name: 'Delivery Orders',
            category: 'Delivery',
            path: '/delivery/orders',
            description: 'Delivery order management',
            requiresAuth: true,
            userType: 'delivery',
            features: [
                'Order list',
                'Accept/Decline',
                'Navigation',
                'Status updates',
                'Customer contact'
            ]
        },
        // Testing Pages
        {
            name: 'Debug Dashboard',
            category: 'Testing',
            path: '/debug',
            description: 'System debugging and testing',
            features: [
                'API tests',
                'Page tests',
                'Upload tests',
                'System status',
                'Performance metrics'
            ]
        },
        {
            name: 'System Test',
            category: 'Testing',
            path: '/test-system',
            description: 'Comprehensive system testing',
            features: [
                'Automated tests',
                'Manual tests',
                'Test reports',
                'Performance tests',
                'Load tests'
            ]
        }
    ];
    const categories = [
        'all',
        ...Array.from(new Set([
            ...apiTests.map((t)=>t.category),
            ...pageTests.map((t)=>t.category)
        ]))
    ];
    const runAPITest = async (test)=>{
        const startTime = Date.now();
        try {
            const baseUrl = ("TURBOPACK compile-time value", "http://localhost:5000/api") || 'http://localhost:5000';
            let response;
            if (test.method === 'POST' && test.endpoint.includes('upload')) {
                // Skip upload tests that require files
                return {
                    name: test.name,
                    category: test.category,
                    status: 'skipped',
                    message: 'Requires file upload',
                    duration: Date.now() - startTime
                };
            } else {
                const options = {
                    method: test.method,
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                };
                if (test.testData && (test.method === 'POST' || test.method === 'PUT')) {
                    options.body = JSON.stringify(test.testData);
                }
                response = await fetch("".concat(baseUrl).concat(test.endpoint), options);
            }
            const duration = Date.now() - startTime;
            let data;
            try {
                data = await response.json();
            } catch (e) {
                data = await response.text();
            }
            const isSuccess = test.expectedStatus ? response.status === test.expectedStatus : response.ok;
            return {
                name: test.name,
                category: test.category,
                status: isSuccess ? 'success' : 'error',
                message: isSuccess ? "".concat(response.status, " OK") : "".concat(response.status, " ").concat(response.statusText),
                data: data,
                duration,
                details: test.description
            };
        } catch (error) {
            return {
                name: test.name,
                category: test.category,
                status: 'error',
                message: error instanceof Error ? error.message : 'Network error',
                duration: Date.now() - startTime,
                details: test.description
            };
        }
    };
    const runAllAPITests = async ()=>{
        setIsRunning(true);
        setResults([]);
        setTestProgress(0);
        const filteredTests = selectedCategory === 'all' ? apiTests : apiTests.filter((test)=>test.category === selectedCategory);
        for(let i = 0; i < filteredTests.length; i++){
            const test = filteredTests[i];
            setResults((prev)=>[
                    ...prev,
                    {
                        name: test.name,
                        category: test.category,
                        status: 'loading',
                        details: test.description
                    }
                ]);
            const result = await runAPITest(test);
            setResults((prev)=>prev.map((r)=>r.name === test.name ? result : r));
            setTestProgress((i + 1) / filteredTests.length * 100);
            // Small delay between tests
            await new Promise((resolve)=>setTimeout(resolve, 200));
        }
        setIsRunning(false);
    };
    const testPageLoad = async (page)=>{
        const startTime = Date.now();
        try {
            // Test if page loads without errors
            const response = await fetch("http://localhost:3000".concat(page.path), {
                method: 'GET',
                credentials: 'include'
            });
            const duration = Date.now() - startTime;
            return {
                name: page.name,
                category: page.category,
                status: response.ok ? 'success' : 'error',
                message: response.ok ? 'Page loads successfully' : "".concat(response.status, " ").concat(response.statusText),
                duration,
                details: "Features: ".concat(page.features.join(', '))
            };
        } catch (error) {
            return {
                name: page.name,
                category: page.category,
                status: 'error',
                message: error instanceof Error ? error.message : 'Page load failed',
                duration: Date.now() - startTime,
                details: page.description
            };
        }
    };
    const runAllPageTests = async ()=>{
        setIsRunning(true);
        setResults([]);
        setTestProgress(0);
        const filteredTests = selectedCategory === 'all' ? pageTests : pageTests.filter((test)=>test.category === selectedCategory);
        for(let i = 0; i < filteredTests.length; i++){
            const test = filteredTests[i];
            setResults((prev)=>[
                    ...prev,
                    {
                        name: test.name,
                        category: test.category,
                        status: 'loading',
                        details: test.description
                    }
                ]);
            const result = await testPageLoad(test);
            setResults((prev)=>prev.map((r)=>r.name === test.name ? result : r));
            setTestProgress((i + 1) / filteredTests.length * 100);
            // Small delay between tests
            await new Promise((resolve)=>setTimeout(resolve, 300));
        }
        setIsRunning(false);
    };
    const getStatusIcon = (status)=>{
        switch(status){
            case 'success':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2d$big$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle$3e$__["CheckCircle"], {
                    className: "w-5 h-5 text-green-500"
                }, void 0, false, {
                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                    lineNumber: 543,
                    columnNumber: 16
                }, ("TURBOPACK compile-time value", void 0));
            case 'error':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__XCircle$3e$__["XCircle"], {
                    className: "w-5 h-5 text-red-500"
                }, void 0, false, {
                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                    lineNumber: 545,
                    columnNumber: 16
                }, ("TURBOPACK compile-time value", void 0));
            case 'loading':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__["Clock"], {
                    className: "w-5 h-5 text-blue-500 animate-spin"
                }, void 0, false, {
                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                    lineNumber: 547,
                    columnNumber: 16
                }, ("TURBOPACK compile-time value", void 0));
            case 'pending':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertCircle$3e$__["AlertCircle"], {
                    className: "w-5 h-5 text-yellow-500"
                }, void 0, false, {
                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                    lineNumber: 549,
                    columnNumber: 16
                }, ("TURBOPACK compile-time value", void 0));
            case 'skipped':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$pause$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Pause$3e$__["Pause"], {
                    className: "w-5 h-5 text-gray-500"
                }, void 0, false, {
                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                    lineNumber: 551,
                    columnNumber: 16
                }, ("TURBOPACK compile-time value", void 0));
            default:
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__["Clock"], {
                    className: "w-5 h-5 text-gray-500"
                }, void 0, false, {
                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                    lineNumber: 553,
                    columnNumber: 16
                }, ("TURBOPACK compile-time value", void 0));
        }
    };
    const getStatusColor = (status)=>{
        switch(status){
            case 'success':
                return 'text-green-600 bg-green-50 border-green-200';
            case 'error':
                return 'text-red-600 bg-red-50 border-red-200';
            case 'loading':
                return 'text-blue-600 bg-blue-50 border-blue-200';
            case 'pending':
                return 'text-yellow-600 bg-yellow-50 border-yellow-200';
            case 'skipped':
                return 'text-gray-600 bg-gray-50 border-gray-200';
            default:
                return 'text-gray-600 bg-gray-50 border-gray-200';
        }
    };
    const getCategoryIcon = (category)=>{
        switch(category){
            case 'System':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$database$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Database$3e$__["Database"], {
                    className: "w-4 h-4"
                }, void 0, false, {
                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                    lineNumber: 576,
                    columnNumber: 29
                }, ("TURBOPACK compile-time value", void 0));
            case 'Auth':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$shield$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Shield$3e$__["Shield"], {
                    className: "w-4 h-4"
                }, void 0, false, {
                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                    lineNumber: 577,
                    columnNumber: 27
                }, ("TURBOPACK compile-time value", void 0));
            case 'Restaurants':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Store, {
                    className: "w-4 h-4"
                }, void 0, false, {
                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                    lineNumber: 578,
                    columnNumber: 34
                }, ("TURBOPACK compile-time value", void 0));
            case 'Foods':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$shopping$2d$cart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ShoppingCart$3e$__["ShoppingCart"], {
                    className: "w-4 h-4"
                }, void 0, false, {
                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                    lineNumber: 579,
                    columnNumber: 28
                }, ("TURBOPACK compile-time value", void 0));
            case 'Vendor':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__User$3e$__["User"], {
                    className: "w-4 h-4"
                }, void 0, false, {
                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                    lineNumber: 580,
                    columnNumber: 29
                }, ("TURBOPACK compile-time value", void 0));
            case 'Upload':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$upload$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Upload$3e$__["Upload"], {
                    className: "w-4 h-4"
                }, void 0, false, {
                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                    lineNumber: 581,
                    columnNumber: 29
                }, ("TURBOPACK compile-time value", void 0));
            case 'Orders':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$text$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FileText$3e$__["FileText"], {
                    className: "w-4 h-4"
                }, void 0, false, {
                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                    lineNumber: 582,
                    columnNumber: 29
                }, ("TURBOPACK compile-time value", void 0));
            case 'Users':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__User$3e$__["User"], {
                    className: "w-4 h-4"
                }, void 0, false, {
                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                    lineNumber: 583,
                    columnNumber: 28
                }, ("TURBOPACK compile-time value", void 0));
            case 'Admin':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$shield$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Shield$3e$__["Shield"], {
                    className: "w-4 h-4"
                }, void 0, false, {
                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                    lineNumber: 584,
                    columnNumber: 28
                }, ("TURBOPACK compile-time value", void 0));
            case 'Delivery':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$truck$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Truck$3e$__["Truck"], {
                    className: "w-4 h-4"
                }, void 0, false, {
                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                    lineNumber: 585,
                    columnNumber: 31
                }, ("TURBOPACK compile-time value", void 0));
            case 'Payment':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$credit$2d$card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CreditCard$3e$__["CreditCard"], {
                    className: "w-4 h-4"
                }, void 0, false, {
                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                    lineNumber: 586,
                    columnNumber: 30
                }, ("TURBOPACK compile-time value", void 0));
            case 'Notifications':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bell$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Bell$3e$__["Bell"], {
                    className: "w-4 h-4"
                }, void 0, false, {
                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                    lineNumber: 587,
                    columnNumber: 36
                }, ("TURBOPACK compile-time value", void 0));
            case 'Public':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$globe$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Globe$3e$__["Globe"], {
                    className: "w-4 h-4"
                }, void 0, false, {
                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                    lineNumber: 588,
                    columnNumber: 29
                }, ("TURBOPACK compile-time value", void 0));
            case 'Customer':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__User$3e$__["User"], {
                    className: "w-4 h-4"
                }, void 0, false, {
                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                    lineNumber: 589,
                    columnNumber: 31
                }, ("TURBOPACK compile-time value", void 0));
            case 'Testing':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$settings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Settings$3e$__["Settings"], {
                    className: "w-4 h-4"
                }, void 0, false, {
                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                    lineNumber: 590,
                    columnNumber: 30
                }, ("TURBOPACK compile-time value", void 0));
            default:
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$server$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Server$3e$__["Server"], {
                    className: "w-4 h-4"
                }, void 0, false, {
                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                    lineNumber: 591,
                    columnNumber: 23
                }, ("TURBOPACK compile-time value", void 0));
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen bg-gray-50 p-6",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "max-w-7xl mx-auto",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-white rounded-lg shadow-sm border p-6 mb-6",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                            className: "text-3xl font-bold text-gray-900 mb-2",
                            children: "🧪 Comprehensive System Testing"
                        }, void 0, false, {
                            fileName: "[project]/src/app/comprehensive-test/page.tsx",
                            lineNumber: 600,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-gray-600 mb-4",
                            children: "Complete testing of all APIs, pages, and features in the food ordering system"
                        }, void 0, false, {
                            fileName: "[project]/src/app/comprehensive-test/page.tsx",
                            lineNumber: 601,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        isRunning && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "mb-4",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex justify-between text-sm text-gray-600 mb-1",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            children: "Testing Progress"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                            lineNumber: 607,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            children: [
                                                Math.round(testProgress),
                                                "%"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                            lineNumber: 608,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0))
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                    lineNumber: 606,
                                    columnNumber: 15
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "w-full bg-gray-200 rounded-full h-2",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "bg-orange-500 h-2 rounded-full transition-all duration-300",
                                        style: {
                                            width: "".concat(testProgress, "%")
                                        }
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                        lineNumber: 611,
                                        columnNumber: 17
                                    }, ("TURBOPACK compile-time value", void 0))
                                }, void 0, false, {
                                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                    lineNumber: 610,
                                    columnNumber: 15
                                }, ("TURBOPACK compile-time value", void 0))
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/comprehensive-test/page.tsx",
                            lineNumber: 605,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                    lineNumber: 599,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-white rounded-lg shadow-sm border mb-6",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "border-b border-gray-200",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("nav", {
                                className: "flex space-x-8 px-6",
                                children: [
                                    {
                                        id: 'apis',
                                        label: 'API Tests',
                                        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$server$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Server$3e$__["Server"],
                                        count: apiTests.length
                                    },
                                    {
                                        id: 'pages',
                                        label: 'Page Tests',
                                        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$globe$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Globe$3e$__["Globe"],
                                        count: pageTests.length
                                    },
                                    {
                                        id: 'features',
                                        label: 'Feature Tests',
                                        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$settings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Settings$3e$__["Settings"],
                                        count: 0
                                    }
                                ].map((tab)=>{
                                    const Icon = tab.icon;
                                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        onClick: ()=>setActiveTab(tab.id),
                                        className: "flex items-center py-4 px-1 border-b-2 font-medium text-sm ".concat(activeTab === tab.id ? 'border-orange-500 text-orange-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Icon, {
                                                className: "w-5 h-5 mr-2"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                                lineNumber: 640,
                                                columnNumber: 21
                                            }, ("TURBOPACK compile-time value", void 0)),
                                            tab.label,
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "ml-2 bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs",
                                                children: tab.count
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                                lineNumber: 642,
                                                columnNumber: 21
                                            }, ("TURBOPACK compile-time value", void 0))
                                        ]
                                    }, tab.id, true, {
                                        fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                        lineNumber: 631,
                                        columnNumber: 19
                                    }, ("TURBOPACK compile-time value", void 0));
                                })
                            }, void 0, false, {
                                fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                lineNumber: 623,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        }, void 0, false, {
                            fileName: "[project]/src/app/comprehensive-test/page.tsx",
                            lineNumber: 622,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "p-6",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center justify-between mb-6",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center space-x-4",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                    className: "text-sm font-medium text-gray-700",
                                                    children: "Filter by Category:"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                                    lineNumber: 655,
                                                    columnNumber: 17
                                                }, ("TURBOPACK compile-time value", void 0)),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                                                    value: selectedCategory,
                                                    onChange: (e)=>setSelectedCategory(e.target.value),
                                                    className: "px-3 py-2 border border-gray-300 rounded-md text-sm",
                                                    children: categories.map((category)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                            value: category,
                                                            children: category === 'all' ? 'All Categories' : category
                                                        }, category, false, {
                                                            fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                                            lineNumber: 662,
                                                            columnNumber: 21
                                                        }, ("TURBOPACK compile-time value", void 0)))
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                                    lineNumber: 656,
                                                    columnNumber: 17
                                                }, ("TURBOPACK compile-time value", void 0))
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                            lineNumber: 654,
                                            columnNumber: 15
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex space-x-3",
                                            children: [
                                                activeTab === 'apis' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    onClick: runAllAPITests,
                                                    disabled: isRunning,
                                                    className: "bg-orange-500 text-white px-6 py-2 rounded-md hover:bg-orange-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center",
                                                    children: [
                                                        isRunning ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__["Clock"], {
                                                            className: "w-4 h-4 mr-2 animate-spin"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                                            lineNumber: 676,
                                                            columnNumber: 34
                                                        }, ("TURBOPACK compile-time value", void 0)) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$play$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Play$3e$__["Play"], {
                                                            className: "w-4 h-4 mr-2"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                                            lineNumber: 676,
                                                            columnNumber: 84
                                                        }, ("TURBOPACK compile-time value", void 0)),
                                                        isRunning ? 'Testing APIs...' : 'Test All APIs'
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                                    lineNumber: 671,
                                                    columnNumber: 19
                                                }, ("TURBOPACK compile-time value", void 0)),
                                                activeTab === 'pages' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    onClick: runAllPageTests,
                                                    disabled: isRunning,
                                                    className: "bg-blue-500 text-white px-6 py-2 rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center",
                                                    children: [
                                                        isRunning ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__["Clock"], {
                                                            className: "w-4 h-4 mr-2 animate-spin"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                                            lineNumber: 687,
                                                            columnNumber: 34
                                                        }, ("TURBOPACK compile-time value", void 0)) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$play$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Play$3e$__["Play"], {
                                                            className: "w-4 h-4 mr-2"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                                            lineNumber: 687,
                                                            columnNumber: 84
                                                        }, ("TURBOPACK compile-time value", void 0)),
                                                        isRunning ? 'Testing Pages...' : 'Test All Pages'
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                                    lineNumber: 682,
                                                    columnNumber: 19
                                                }, ("TURBOPACK compile-time value", void 0)),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    onClick: ()=>setResults([]),
                                                    className: "bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600 flex items-center",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$rotate$2d$ccw$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__RotateCcw$3e$__["RotateCcw"], {
                                                            className: "w-4 h-4 mr-2"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                                            lineNumber: 696,
                                                            columnNumber: 19
                                                        }, ("TURBOPACK compile-time value", void 0)),
                                                        "Clear Results"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                                    lineNumber: 692,
                                                    columnNumber: 17
                                                }, ("TURBOPACK compile-time value", void 0))
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                            lineNumber: 669,
                                            columnNumber: 15
                                        }, ("TURBOPACK compile-time value", void 0))
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                    lineNumber: 653,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0)),
                                activeTab === 'apis' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                            className: "text-xl font-semibold mb-4",
                                            children: "API Test Results"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                            lineNumber: 705,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        results.length === 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-center py-12 bg-gray-50 rounded-lg",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$server$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Server$3e$__["Server"], {
                                                    className: "w-12 h-12 text-gray-400 mx-auto mb-4"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                                    lineNumber: 709,
                                                    columnNumber: 21
                                                }, ("TURBOPACK compile-time value", void 0)),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "text-gray-500",
                                                    children: 'Click "Test All APIs" to start testing'
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                                    lineNumber: 710,
                                                    columnNumber: 21
                                                }, ("TURBOPACK compile-time value", void 0)),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "text-sm text-gray-400 mt-2",
                                                    children: selectedCategory === 'all' ? "".concat(apiTests.length, " APIs available for testing") : "".concat(apiTests.filter((t)=>t.category === selectedCategory).length, " APIs in ").concat(selectedCategory, " category")
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                                    lineNumber: 711,
                                                    columnNumber: 21
                                                }, ("TURBOPACK compile-time value", void 0))
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                            lineNumber: 708,
                                            columnNumber: 19
                                        }, ("TURBOPACK compile-time value", void 0)) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "space-y-3",
                                            children: results.map((result, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "p-4 rounded-lg border ".concat(getStatusColor(result.status)),
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "flex items-center justify-between mb-2",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "flex items-center space-x-3",
                                                                    children: [
                                                                        getStatusIcon(result.status),
                                                                        getCategoryIcon(result.category),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                            children: [
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                    className: "font-medium",
                                                                                    children: result.name
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                                                                    lineNumber: 730,
                                                                                    columnNumber: 31
                                                                                }, ("TURBOPACK compile-time value", void 0)),
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                    className: "ml-2 text-xs bg-white bg-opacity-50 px-2 py-1 rounded",
                                                                                    children: result.category
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                                                                    lineNumber: 731,
                                                                                    columnNumber: 31
                                                                                }, ("TURBOPACK compile-time value", void 0))
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                                                            lineNumber: 729,
                                                                            columnNumber: 29
                                                                        }, ("TURBOPACK compile-time value", void 0))
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                                                    lineNumber: 726,
                                                                    columnNumber: 27
                                                                }, ("TURBOPACK compile-time value", void 0)),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "flex items-center space-x-2 text-sm",
                                                                    children: [
                                                                        result.duration && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                            className: "text-gray-500",
                                                                            children: [
                                                                                result.duration,
                                                                                "ms"
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                                                            lineNumber: 738,
                                                                            columnNumber: 31
                                                                        }, ("TURBOPACK compile-time value", void 0)),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                            children: result.message
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                                                            lineNumber: 740,
                                                                            columnNumber: 29
                                                                        }, ("TURBOPACK compile-time value", void 0))
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                                                    lineNumber: 736,
                                                                    columnNumber: 27
                                                                }, ("TURBOPACK compile-time value", void 0))
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                                            lineNumber: 725,
                                                            columnNumber: 25
                                                        }, ("TURBOPACK compile-time value", void 0)),
                                                        result.details && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "text-sm text-gray-600 mb-2",
                                                            children: result.details
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                                            lineNumber: 745,
                                                            columnNumber: 27
                                                        }, ("TURBOPACK compile-time value", void 0)),
                                                        result.data && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("details", {
                                                            className: "mt-3",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("summary", {
                                                                    className: "cursor-pointer text-sm font-medium text-gray-700 hover:text-gray-900",
                                                                    children: "View Response Data"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                                                    lineNumber: 750,
                                                                    columnNumber: 29
                                                                }, ("TURBOPACK compile-time value", void 0)),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "mt-2 p-3 bg-white bg-opacity-50 rounded text-sm",
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("pre", {
                                                                        className: "overflow-x-auto whitespace-pre-wrap",
                                                                        children: typeof result.data === 'string' ? result.data : JSON.stringify(result.data, null, 2)
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                                                        lineNumber: 754,
                                                                        columnNumber: 31
                                                                    }, ("TURBOPACK compile-time value", void 0))
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                                                    lineNumber: 753,
                                                                    columnNumber: 29
                                                                }, ("TURBOPACK compile-time value", void 0))
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                                            lineNumber: 749,
                                                            columnNumber: 27
                                                        }, ("TURBOPACK compile-time value", void 0))
                                                    ]
                                                }, index, true, {
                                                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                                    lineNumber: 721,
                                                    columnNumber: 23
                                                }, ("TURBOPACK compile-time value", void 0)))
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                            lineNumber: 719,
                                            columnNumber: 19
                                        }, ("TURBOPACK compile-time value", void 0))
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                    lineNumber: 704,
                                    columnNumber: 15
                                }, ("TURBOPACK compile-time value", void 0)),
                                activeTab === 'pages' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                            className: "text-xl font-semibold mb-4",
                                            children: "Page Test Results"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                            lineNumber: 772,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        results.length === 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-center py-12 bg-gray-50 rounded-lg",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$globe$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Globe$3e$__["Globe"], {
                                                    className: "w-12 h-12 text-gray-400 mx-auto mb-4"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                                    lineNumber: 776,
                                                    columnNumber: 21
                                                }, ("TURBOPACK compile-time value", void 0)),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "text-gray-500",
                                                    children: 'Click "Test All Pages" to start testing'
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                                    lineNumber: 777,
                                                    columnNumber: 21
                                                }, ("TURBOPACK compile-time value", void 0)),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "text-sm text-gray-400 mt-2",
                                                    children: selectedCategory === 'all' ? "".concat(pageTests.length, " pages available for testing") : "".concat(pageTests.filter((t)=>t.category === selectedCategory).length, " pages in ").concat(selectedCategory, " category")
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                                    lineNumber: 778,
                                                    columnNumber: 21
                                                }, ("TURBOPACK compile-time value", void 0))
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                            lineNumber: 775,
                                            columnNumber: 19
                                        }, ("TURBOPACK compile-time value", void 0)) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "space-y-3",
                                            children: results.map((result, index)=>{
                                                var _pageTests_find;
                                                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "p-4 rounded-lg border ".concat(getStatusColor(result.status)),
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "flex items-center justify-between mb-2",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "flex items-center space-x-3",
                                                                    children: [
                                                                        getStatusIcon(result.status),
                                                                        getCategoryIcon(result.category),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                            children: [
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                    className: "font-medium",
                                                                                    children: result.name
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                                                                    lineNumber: 797,
                                                                                    columnNumber: 31
                                                                                }, ("TURBOPACK compile-time value", void 0)),
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                    className: "ml-2 text-xs bg-white bg-opacity-50 px-2 py-1 rounded",
                                                                                    children: result.category
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                                                                    lineNumber: 798,
                                                                                    columnNumber: 31
                                                                                }, ("TURBOPACK compile-time value", void 0))
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                                                            lineNumber: 796,
                                                                            columnNumber: 29
                                                                        }, ("TURBOPACK compile-time value", void 0))
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                                                    lineNumber: 793,
                                                                    columnNumber: 27
                                                                }, ("TURBOPACK compile-time value", void 0)),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "flex items-center space-x-2",
                                                                    children: [
                                                                        result.duration && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                            className: "text-sm text-gray-500",
                                                                            children: [
                                                                                result.duration,
                                                                                "ms"
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                                                            lineNumber: 805,
                                                                            columnNumber: 31
                                                                        }, ("TURBOPACK compile-time value", void 0)),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                            className: "text-sm",
                                                                            children: result.message
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                                                            lineNumber: 807,
                                                                            columnNumber: 29
                                                                        }, ("TURBOPACK compile-time value", void 0)),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                            href: ((_pageTests_find = pageTests.find((p)=>p.name === result.name)) === null || _pageTests_find === void 0 ? void 0 : _pageTests_find.path) || '/',
                                                                            target: "_blank",
                                                                            className: "text-orange-500 hover:text-orange-600",
                                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$external$2d$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ExternalLink$3e$__["ExternalLink"], {
                                                                                className: "w-4 h-4"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                                                                lineNumber: 813,
                                                                                columnNumber: 31
                                                                            }, ("TURBOPACK compile-time value", void 0))
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                                                            lineNumber: 808,
                                                                            columnNumber: 29
                                                                        }, ("TURBOPACK compile-time value", void 0))
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                                                    lineNumber: 803,
                                                                    columnNumber: 27
                                                                }, ("TURBOPACK compile-time value", void 0))
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                                            lineNumber: 792,
                                                            columnNumber: 25
                                                        }, ("TURBOPACK compile-time value", void 0)),
                                                        result.details && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "text-sm text-gray-600",
                                                            children: result.details
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                                            lineNumber: 819,
                                                            columnNumber: 27
                                                        }, ("TURBOPACK compile-time value", void 0))
                                                    ]
                                                }, index, true, {
                                                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                                    lineNumber: 788,
                                                    columnNumber: 23
                                                }, ("TURBOPACK compile-time value", void 0));
                                            })
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                            lineNumber: 786,
                                            columnNumber: 19
                                        }, ("TURBOPACK compile-time value", void 0))
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                    lineNumber: 771,
                                    columnNumber: 15
                                }, ("TURBOPACK compile-time value", void 0)),
                                activeTab === 'features' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                            className: "text-xl font-semibold mb-4",
                                            children: "Feature Tests"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                            lineNumber: 830,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-center py-12 bg-gray-50 rounded-lg",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$settings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Settings$3e$__["Settings"], {
                                                    className: "w-12 h-12 text-gray-400 mx-auto mb-4"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                                    lineNumber: 832,
                                                    columnNumber: 19
                                                }, ("TURBOPACK compile-time value", void 0)),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "text-gray-500",
                                                    children: "Feature testing coming soon..."
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                                    lineNumber: 833,
                                                    columnNumber: 19
                                                }, ("TURBOPACK compile-time value", void 0)),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "text-sm text-gray-400 mt-2",
                                                    children: "This will include upload functionality, form submissions, and interactive features"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                                    lineNumber: 834,
                                                    columnNumber: 19
                                                }, ("TURBOPACK compile-time value", void 0))
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                            lineNumber: 831,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0))
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                    lineNumber: 829,
                                    columnNumber: 15
                                }, ("TURBOPACK compile-time value", void 0))
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/comprehensive-test/page.tsx",
                            lineNumber: 651,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                    lineNumber: 621,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                results.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-white rounded-lg shadow-sm border p-6",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                            className: "text-xl font-semibold mb-4",
                            children: "Test Summary"
                        }, void 0, false, {
                            fileName: "[project]/src/app/comprehensive-test/page.tsx",
                            lineNumber: 846,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "grid grid-cols-2 md:grid-cols-5 gap-4",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-center p-4 bg-green-50 rounded-lg",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-2xl font-bold text-green-600",
                                            children: results.filter((r)=>r.status === 'success').length
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                            lineNumber: 850,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-sm text-green-600",
                                            children: "Passed"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                            lineNumber: 853,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0))
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                    lineNumber: 849,
                                    columnNumber: 15
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-center p-4 bg-red-50 rounded-lg",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-2xl font-bold text-red-600",
                                            children: results.filter((r)=>r.status === 'error').length
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                            lineNumber: 857,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-sm text-red-600",
                                            children: "Failed"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                            lineNumber: 860,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0))
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                    lineNumber: 856,
                                    columnNumber: 15
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-center p-4 bg-yellow-50 rounded-lg",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-2xl font-bold text-yellow-600",
                                            children: results.filter((r)=>r.status === 'pending').length
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                            lineNumber: 864,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-sm text-yellow-600",
                                            children: "Pending"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                            lineNumber: 867,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0))
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                    lineNumber: 863,
                                    columnNumber: 15
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-center p-4 bg-gray-50 rounded-lg",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-2xl font-bold text-gray-600",
                                            children: results.filter((r)=>r.status === 'skipped').length
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                            lineNumber: 871,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-sm text-gray-600",
                                            children: "Skipped"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                            lineNumber: 874,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0))
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                    lineNumber: 870,
                                    columnNumber: 15
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-center p-4 bg-blue-50 rounded-lg",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-2xl font-bold text-blue-600",
                                            children: results.length
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                            lineNumber: 878,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-sm text-blue-600",
                                            children: "Total"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                            lineNumber: 881,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0))
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                    lineNumber: 877,
                                    columnNumber: 15
                                }, ("TURBOPACK compile-time value", void 0))
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/comprehensive-test/page.tsx",
                            lineNumber: 848,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0)),
                        results.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "mt-4 text-center",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-sm text-gray-600",
                                    children: [
                                        "Success Rate: ",
                                        Math.round(results.filter((r)=>r.status === 'success').length / results.length * 100),
                                        "%"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                    lineNumber: 887,
                                    columnNumber: 17
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-xs text-gray-500 mt-1",
                                    children: [
                                        "Average Response Time: ",
                                        Math.round(results.reduce((acc, r)=>acc + (r.duration || 0), 0) / results.length),
                                        "ms"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                                    lineNumber: 890,
                                    columnNumber: 17
                                }, ("TURBOPACK compile-time value", void 0))
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/comprehensive-test/page.tsx",
                            lineNumber: 886,
                            columnNumber: 15
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/comprehensive-test/page.tsx",
                    lineNumber: 845,
                    columnNumber: 11
                }, ("TURBOPACK compile-time value", void 0))
            ]
        }, void 0, true, {
            fileName: "[project]/src/app/comprehensive-test/page.tsx",
            lineNumber: 597,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/app/comprehensive-test/page.tsx",
        lineNumber: 596,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_s(ComprehensiveTestPage, "TkCJ6JfzNHfJ5TT9CUxdYWWRhQ0=");
_c = ComprehensiveTestPage;
const __TURBOPACK__default__export__ = ComprehensiveTestPage;
var _c;
__turbopack_context__.k.register(_c, "ComprehensiveTestPage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_app_comprehensive-test_page_tsx_ec235fa2._.js.map