'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useQuery } from '@tanstack/react-query';
import { 
  MapPin, 
  Navigation,
  Package,
  Clock,
  Phone,
  Route,
  Locate,
  RefreshCw
} from 'lucide-react';
import DeliverySidebar from '@/components/DeliverySidebar';
import { deliveryAPI } from '@/lib/api';

const DeliveryMapPage = () => {
  const [currentLocation, setCurrentLocation] = useState<{lat: number, lng: number} | null>(null);
  const [selectedOrder, setSelectedOrder] = useState<any>(null);

  // Fetch active orders
  const { data: ordersData, isLoading } = useQuery({
    queryKey: ['delivery-map-orders'],
    queryFn: () => deliveryAPI.getOrders({ assigned: 'true' }),
    refetchInterval: 30000,
  });

  const orders = ordersData?.data || [];

  // Mock locations for demonstration
  const mockOrders = [
    {
      id: 'ORD001',
      orderNumber: '#12345',
      restaurant: {
        name: 'Spice Garden',
        address: '123 Restaurant St, Mumbai',
        lat: 19.0760,
        lng: 72.8777
      },
      customer: {
        name: '<PERSON>e',
        address: '456 Customer Ave, Mumbai',
        lat: 19.0896,
        lng: 72.8656,
        phone: '+91 9876543210'
      },
      status: 'ready_for_pickup',
      amount: 450,
      estimatedTime: '15 mins'
    },
    {
      id: 'ORD002',
      orderNumber: '#12346',
      restaurant: {
        name: 'Pizza Palace',
        address: '789 Pizza Rd, Mumbai',
        lat: 19.0728,
        lng: 72.8826
      },
      customer: {
        name: 'Sarah Smith',
        address: '321 Home St, Mumbai',
        lat: 19.0825,
        lng: 72.8811,
        phone: '+91 9876543211'
      },
      status: 'picked_up',
      amount: 680,
      estimatedTime: '8 mins'
    }
  ];

  const displayOrders = orders.length > 0 ? orders : mockOrders;

  // Get current location
  useEffect(() => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          setCurrentLocation({
            lat: position.coords.latitude,
            lng: position.coords.longitude
          });
        },
        (error) => {
          console.error('Error getting location:', error);
          // Default to Mumbai coordinates
          setCurrentLocation({
            lat: 19.0760,
            lng: 72.8777
          });
        }
      );
    }
  }, []);

  const calculateDistance = (lat1: number, lng1: number, lat2: number, lng2: number) => {
    const R = 6371; // Earth's radius in km
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLng = (lng2 - lng1) * Math.PI / 180;
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
              Math.sin(dLng/2) * Math.sin(dLng/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ready_for_pickup':
        return 'bg-orange-500';
      case 'picked_up':
        return 'bg-blue-500';
      case 'delivered':
        return 'bg-green-500';
      default:
        return 'bg-gray-500';
    }
  };

  const handleOrderSelect = (order: any) => {
    setSelectedOrder(order);
  };

  const handleGetDirections = (order: any) => {
    const destination = order.status === 'ready_for_pickup' 
      ? `${order.restaurant.lat},${order.restaurant.lng}`
      : `${order.customer.lat},${order.customer.lng}`;
    
    const url = `https://www.google.com/maps/dir/?api=1&destination=${destination}`;
    window.open(url, '_blank');
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex">
        <DeliverySidebar />
        <div className="flex-1 p-8">
          <div className="max-w-6xl mx-auto">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
              <div className="h-96 bg-gray-200 rounded-lg"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex">
      <DeliverySidebar />
      
      <div className="flex-1 p-8">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <MapPin className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Delivery Map</h1>
                <p className="text-gray-600">View all delivery locations and routes</p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <button className="flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                <Locate className="w-4 h-4" />
                My Location
              </button>
              <button className="flex items-center gap-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                <RefreshCw className="w-4 h-4" />
                Refresh
              </button>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Map Placeholder */}
            <div className="lg:col-span-2">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-white rounded-2xl p-6 shadow-md h-96"
              >
                <div className="w-full h-full bg-gray-100 rounded-lg flex items-center justify-center relative overflow-hidden">
                  {/* Map Background Pattern */}
                  <div className="absolute inset-0 opacity-10">
                    <div className="grid grid-cols-8 grid-rows-6 h-full w-full">
                      {[...Array(48)].map((_, i) => (
                        <div key={i} className="border border-gray-300"></div>
                      ))}
                    </div>
                  </div>
                  
                  {/* Location Markers */}
                  {displayOrders.map((order, index) => (
                    <div key={order.id}>
                      {/* Restaurant Marker */}
                      <div 
                        className={`absolute w-4 h-4 rounded-full ${getStatusColor(order.status)} cursor-pointer transform -translate-x-1/2 -translate-y-1/2`}
                        style={{
                          left: `${20 + index * 15}%`,
                          top: `${30 + index * 10}%`
                        }}
                        onClick={() => handleOrderSelect(order)}
                      />
                      
                      {/* Customer Marker */}
                      <div 
                        className="absolute w-4 h-4 rounded-full bg-red-500 cursor-pointer transform -translate-x-1/2 -translate-y-1/2"
                        style={{
                          left: `${40 + index * 15}%`,
                          top: `${50 + index * 10}%`
                        }}
                        onClick={() => handleOrderSelect(order)}
                      />
                      
                      {/* Route Line */}
                      <svg className="absolute inset-0 w-full h-full pointer-events-none">
                        <line
                          x1={`${20 + index * 15}%`}
                          y1={`${30 + index * 10}%`}
                          x2={`${40 + index * 15}%`}
                          y2={`${50 + index * 10}%`}
                          stroke="#3B82F6"
                          strokeWidth="2"
                          strokeDasharray="5,5"
                        />
                      </svg>
                    </div>
                  ))}
                  
                  {/* Current Location */}
                  {currentLocation && (
                    <div className="absolute w-6 h-6 bg-green-500 rounded-full border-2 border-white shadow-lg transform -translate-x-1/2 -translate-y-1/2"
                         style={{ left: '50%', top: '40%' }}>
                      <div className="w-2 h-2 bg-white rounded-full absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"></div>
                    </div>
                  )}
                  
                  <div className="text-center">
                    <MapPin className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-700 mb-2">Interactive Map</h3>
                    <p className="text-gray-500">Map integration with Google Maps/Mapbox would be implemented here</p>
                  </div>
                </div>
              </motion.div>
            </div>

            {/* Orders List */}
            <div className="lg:col-span-1">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="bg-white rounded-2xl p-6 shadow-md"
              >
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Active Orders</h2>
                
                <div className="space-y-4 max-h-80 overflow-y-auto">
                  {displayOrders.map((order, index) => (
                    <motion.div
                      key={order.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className={`p-4 border rounded-lg cursor-pointer transition-all duration-200 ${
                        selectedOrder?.id === order.id 
                          ? 'border-blue-300 bg-blue-50' 
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => handleOrderSelect(order)}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-medium text-gray-900">{order.orderNumber}</span>
                        <span className={`w-3 h-3 rounded-full ${getStatusColor(order.status)}`}></span>
                      </div>
                      
                      <div className="space-y-2 text-sm">
                        <div className="flex items-center gap-2">
                          <Package className="w-4 h-4 text-gray-400" />
                          <span className="text-gray-600">{order.restaurant.name}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <MapPin className="w-4 h-4 text-gray-400" />
                          <span className="text-gray-600">{order.customer.name}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Clock className="w-4 h-4 text-gray-400" />
                          <span className="text-gray-600">ETA: {order.estimatedTime}</span>
                        </div>
                      </div>
                      
                      <div className="flex items-center justify-between mt-3">
                        <span className="font-semibold text-gray-900">₹{order.amount}</span>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleGetDirections(order);
                          }}
                          className="flex items-center gap-1 px-3 py-1 bg-blue-500 text-white text-xs rounded-lg hover:bg-blue-600 transition-colors"
                        >
                          <Navigation className="w-3 h-3" />
                          Directions
                        </button>
                      </div>
                    </motion.div>
                  ))}
                </div>

                {displayOrders.length === 0 && (
                  <div className="text-center py-8">
                    <Package className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">No Active Orders</h3>
                    <p className="text-gray-600 text-sm">No delivery assignments at the moment.</p>
                  </div>
                )}
              </motion.div>
            </div>
          </div>

          {/* Legend */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="mt-8 bg-white rounded-2xl p-6 shadow-md"
          >
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Map Legend</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 bg-green-500 rounded-full"></div>
                <span className="text-sm text-gray-600">Your Location</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 bg-orange-500 rounded-full"></div>
                <span className="text-sm text-gray-600">Pickup Location</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 bg-blue-500 rounded-full"></div>
                <span className="text-sm text-gray-600">In Transit</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 bg-red-500 rounded-full"></div>
                <span className="text-sm text-gray-600">Delivery Location</span>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default DeliveryMapPage;
