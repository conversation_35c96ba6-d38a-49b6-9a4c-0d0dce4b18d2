{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/project%20food-order%206k/food-ordering-system-modern/frontend/src/app/delivery/layout.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport useAuthStore from '@/store/useAuthStore';\n\nexport default function DeliveryLayout({\n  children,\n}: {\n  children: React.ReactNode;\n}) {\n  const router = useRouter();\n  const { user, isAuthenticated, isLoading } = useAuthStore();\n\n  useEffect(() => {\n    if (!isLoading) {\n      if (!isAuthenticated) {\n        router.push('/auth/login');\n        return;\n      }\n      \n      if (user?.role !== 'delivery') {\n        router.push('/');\n        return;\n      }\n    }\n  }, [isAuthenticated, user, isLoading, router]);\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500\"></div>\n      </div>\n    );\n  }\n\n  if (!isAuthenticated || user?.role !== 'delivery') {\n    return null;\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {children}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS,eAAe,KAItC;QAJsC,EACrC,QAAQ,EAGT,GAJsC;;IAKrC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAY,AAAD;IAExD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,CAAC,WAAW;gBACd,IAAI,CAAC,iBAAiB;oBACpB,OAAO,IAAI,CAAC;oBACZ;gBACF;gBAEA,IAAI,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK,YAAY;oBAC7B,OAAO,IAAI,CAAC;oBACZ;gBACF;YACF;QACF;mCAAG;QAAC;QAAiB;QAAM;QAAW;KAAO;IAE7C,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,mBAAmB,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK,YAAY;QACjD,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACZ;;;;;;AAGP;GAvCwB;;QAKP,qIAAA,CAAA,YAAS;QACqB,+HAAA,CAAA,UAAY;;;KANnC", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/project%20food-order%206k/food-ordering-system-modern/frontend/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}