'use client';

import { motion } from 'framer-motion';
import { usePathname } from 'next/navigation';
import { 
  LayoutDashboard,
  Package,
  MapPin,
  Clock,
  DollarSign,
  Star,
  Settings,
  LogOut,
  Truck
} from 'lucide-react';
import Link from 'next/link';
import useAuthStore from '@/store/useAuthStore';

const DeliverySidebar = () => {
  const pathname = usePathname();
  const { logout } = useAuthStore();

  const menuItems = [
    {
      name: 'Dashboard',
      href: '/delivery/dashboard',
      icon: LayoutDashboard,
    },
    {
      name: 'Active Orders',
      href: '/delivery/orders',
      icon: Package,
    },
    {
      name: 'Map View',
      href: '/delivery/map',
      icon: MapPin,
    },
    {
      name: 'Delivery History',
      href: '/delivery/history',
      icon: Clock,
    },
    {
      name: 'Earnings',
      href: '/delivery/earnings',
      icon: DollarSign,
    },
    {
      name: 'Ratings',
      href: '/delivery/ratings',
      icon: Star,
    },
    {
      name: 'Setting<PERSON>',
      href: '/delivery/settings',
      icon: Settings,
    },
  ];

  const handleLogout = async () => {
    await logout();
    window.location.href = '/';
  };

  return (
    <div className="w-64 bg-white shadow-lg h-screen flex flex-col">
      {/* Logo */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
            <Truck className="w-6 h-6 text-white" />
          </div>
          <div>
            <h2 className="text-lg font-bold text-gray-900">Delivery Portal</h2>
            <p className="text-sm text-gray-600">Partner Dashboard</p>
          </div>
        </div>
      </div>

      {/* Navigation Menu */}
      <nav className="flex-1 p-4 space-y-2">
        {menuItems.map((item) => {
          const isActive = pathname === item.href;
          
          return (
            <Link key={item.name} href={item.href}>
              <motion.div
                className={`flex items-center gap-3 px-4 py-3 rounded-lg transition-all duration-200 ${
                  isActive
                    ? 'bg-blue-50 text-blue-600 border border-blue-200'
                    : 'text-gray-700 hover:bg-gray-50 hover:text-blue-600'
                }`}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <item.icon className={`w-5 h-5 ${
                  isActive ? 'text-blue-600' : 'text-gray-500'
                }`} />
                <span className="font-medium">{item.name}</span>
              </motion.div>
            </Link>
          );
        })}
      </nav>

      {/* User Profile & Logout */}
      <div className="p-4 border-t border-gray-200">
        <div className="flex items-center gap-3 mb-4">
          <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
            <span className="text-gray-600 font-semibold">DP</span>
          </div>
          <div>
            <p className="font-medium text-gray-900">Delivery Partner</p>
            <p className="text-sm text-gray-600">Online</p>
          </div>
        </div>
        
        <motion.button
          onClick={handleLogout}
          className="w-full flex items-center gap-3 px-4 py-3 text-red-600 hover:bg-red-50 rounded-lg transition-all duration-200"
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <LogOut className="w-5 h-5" />
          <span className="font-medium">Logout</span>
        </motion.button>
      </div>
    </div>
  );
};

export default DeliverySidebar;
