import axios from 'axios';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

// Create axios instance for uploads
const uploadAPI = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000, // 30 seconds for file uploads
  withCredentials: true,
});

// Add auth token to requests
uploadAPI.interceptors.request.use((config) => {
  if (typeof window !== 'undefined') {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
  }
  return config;
});

// Upload service functions
export const uploadService = {
  // Upload single image
  uploadImage: async (file, type = 'general') => {
    const formData = new FormData();
    formData.append('image', file);
    formData.append('type', type);

    const response = await uploadAPI.post('/upload/image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data;
  },

  // Upload multiple images
  uploadImages: async (files, type = 'general') => {
    const formData = new FormData();
    
    Array.from(files).forEach((file) => {
      formData.append('images', file);
    });
    formData.append('type', type);

    const response = await uploadAPI.post('/upload/images', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data;
  },

  // Upload restaurant logo
  uploadRestaurantLogo: async (file, restaurantId) => {
    const formData = new FormData();
    formData.append('logo', file);
    formData.append('restaurantId', restaurantId);

    const response = await uploadAPI.post('/upload/restaurant/logo', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data;
  },

  // Upload restaurant banner
  uploadRestaurantBanner: async (file, restaurantId) => {
    const formData = new FormData();
    formData.append('banner', file);
    formData.append('restaurantId', restaurantId);

    const response = await uploadAPI.post('/upload/restaurant/banner', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data;
  },

  // Upload restaurant gallery images
  uploadRestaurantGallery: async (files, restaurantId) => {
    const formData = new FormData();
    
    Array.from(files).forEach((file) => {
      formData.append('gallery', file);
    });
    formData.append('restaurantId', restaurantId);

    const response = await uploadAPI.post('/upload/restaurant/gallery', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data;
  },

  // Upload food item images
  uploadFoodImages: async (files, foodId = null) => {
    const formData = new FormData();
    
    Array.from(files).forEach((file) => {
      formData.append('foodImages', file);
    });
    
    if (foodId) {
      formData.append('foodId', foodId);
    }

    const response = await uploadAPI.post('/upload/food/images', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data;
  },

  // Delete image
  deleteImage: async (imageUrl) => {
    const response = await uploadAPI.delete('/upload/image', {
      data: { imageUrl }
    });

    return response.data;
  },

  // Get upload progress (for large files)
  uploadWithProgress: async (file, type, onProgress) => {
    const formData = new FormData();
    formData.append('image', file);
    formData.append('type', type);

    const response = await uploadAPI.post('/upload/image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        const percentCompleted = Math.round(
          (progressEvent.loaded * 100) / progressEvent.total
        );
        onProgress(percentCompleted);
      },
    });

    return response.data;
  },

  // Validate file before upload
  validateFile: (file, maxSize = 5 * 1024 * 1024) => { // 5MB default
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    
    if (!allowedTypes.includes(file.type)) {
      throw new Error('Invalid file type. Only JPEG, PNG, and WebP images are allowed.');
    }

    if (file.size > maxSize) {
      throw new Error(`File size too large. Maximum size is ${maxSize / (1024 * 1024)}MB.`);
    }

    return true;
  },

  // Create preview URL for local display
  createPreviewUrl: (file) => {
    return URL.createObjectURL(file);
  },

  // Cleanup preview URL
  revokePreviewUrl: (url) => {
    URL.revokeObjectURL(url);
  },

  // Compress image before upload (optional)
  compressImage: async (file, quality = 0.8) => {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        // Calculate new dimensions (max 1920x1080)
        let { width, height } = img;
        const maxWidth = 1920;
        const maxHeight = 1080;

        if (width > maxWidth || height > maxHeight) {
          const ratio = Math.min(maxWidth / width, maxHeight / height);
          width *= ratio;
          height *= ratio;
        }

        canvas.width = width;
        canvas.height = height;

        // Draw and compress
        ctx.drawImage(img, 0, 0, width, height);
        
        canvas.toBlob(resolve, file.type, quality);
      };

      img.src = URL.createObjectURL(file);
    });
  }
};

export default uploadService;
