const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { auth } = require('../middleware/auth');
const Restaurant = require('../models/Restaurant');
const Food = require('../models/Food');

const router = express.Router();

// Create uploads directory if it doesn't exist
const uploadsDir = path.join(__dirname, '../uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Create subdirectories
const subdirs = ['restaurants', 'foods', 'general', 'logos', 'banners', 'gallery'];
subdirs.forEach(dir => {
  const dirPath = path.join(uploadsDir, dir);
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
});

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    let uploadPath = uploadsDir;
    
    // Determine upload path based on file type
    if (req.body.type === 'restaurant-logo' || file.fieldname === 'logo') {
      uploadPath = path.join(uploadsDir, 'logos');
    } else if (req.body.type === 'restaurant-banner' || file.fieldname === 'banner') {
      uploadPath = path.join(uploadsDir, 'banners');
    } else if (req.body.type === 'restaurant-gallery' || file.fieldname === 'gallery') {
      uploadPath = path.join(uploadsDir, 'gallery');
    } else if (req.body.type === 'food' || file.fieldname === 'foodImages') {
      uploadPath = path.join(uploadsDir, 'foods');
    } else {
      uploadPath = path.join(uploadsDir, 'general');
    }
    
    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    // Generate unique filename
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, file.fieldname + '-' + uniqueSuffix + ext);
  }
});

// File filter for images only
const fileFilter = (req, file, cb) => {
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
  
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Invalid file type. Only JPEG, PNG, and WebP images are allowed.'), false);
  }
};

// Configure multer
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  }
});

// Helper function to get file URL
const getFileUrl = (req, filename, subfolder = '') => {
  const baseUrl = `${req.protocol}://${req.get('host')}`;
  return `${baseUrl}/uploads/${subfolder}${subfolder ? '/' : ''}${filename}`;
};

// Upload single image
router.post('/image', auth, upload.single('image'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No file uploaded'
      });
    }

    const fileUrl = getFileUrl(req, req.file.filename, req.body.type || 'general');

    res.json({
      success: true,
      data: {
        url: fileUrl,
        filename: req.file.filename,
        originalName: req.file.originalname,
        size: req.file.size
      }
    });
  } catch (error) {
    console.error('Upload error:', error);
    res.status(500).json({
      success: false,
      message: 'Upload failed',
      error: error.message
    });
  }
});

// Upload multiple images
router.post('/images', auth, upload.array('images', 10), async (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No files uploaded'
      });
    }

    const uploadedFiles = req.files.map(file => ({
      url: getFileUrl(req, file.filename, req.body.type || 'general'),
      filename: file.filename,
      originalName: file.originalname,
      size: file.size
    }));

    res.json({
      success: true,
      data: uploadedFiles
    });
  } catch (error) {
    console.error('Upload error:', error);
    res.status(500).json({
      success: false,
      message: 'Upload failed',
      error: error.message
    });
  }
});

// Upload restaurant logo
router.post('/restaurant/logo', auth, upload.single('logo'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No logo file uploaded'
      });
    }

    const fileUrl = getFileUrl(req, req.file.filename, 'logos');
    const { restaurantId } = req.body;

    // Update restaurant with logo URL
    if (restaurantId) {
      await Restaurant.findByIdAndUpdate(restaurantId, {
        'images.logo': fileUrl
      });
    }

    res.json({
      success: true,
      data: {
        url: fileUrl,
        filename: req.file.filename
      }
    });
  } catch (error) {
    console.error('Logo upload error:', error);
    res.status(500).json({
      success: false,
      message: 'Logo upload failed',
      error: error.message
    });
  }
});

// Upload restaurant banner
router.post('/restaurant/banner', auth, upload.single('banner'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No banner file uploaded'
      });
    }

    const fileUrl = getFileUrl(req, req.file.filename, 'banners');
    const { restaurantId } = req.body;

    // Update restaurant with banner URL
    if (restaurantId) {
      await Restaurant.findByIdAndUpdate(restaurantId, {
        'images.banner': fileUrl
      });
    }

    res.json({
      success: true,
      data: {
        url: fileUrl,
        filename: req.file.filename
      }
    });
  } catch (error) {
    console.error('Banner upload error:', error);
    res.status(500).json({
      success: false,
      message: 'Banner upload failed',
      error: error.message
    });
  }
});

// Upload restaurant gallery
router.post('/restaurant/gallery', auth, upload.array('gallery', 10), async (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No gallery files uploaded'
      });
    }

    const galleryUrls = req.files.map(file => 
      getFileUrl(req, file.filename, 'gallery')
    );

    const { restaurantId } = req.body;

    // Update restaurant with gallery URLs
    if (restaurantId) {
      await Restaurant.findByIdAndUpdate(restaurantId, {
        $push: { 'images.gallery': { $each: galleryUrls } }
      });
    }

    res.json({
      success: true,
      data: galleryUrls
    });
  } catch (error) {
    console.error('Gallery upload error:', error);
    res.status(500).json({
      success: false,
      message: 'Gallery upload failed',
      error: error.message
    });
  }
});

// Upload food images
router.post('/food/images', auth, upload.array('foodImages', 5), async (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No food images uploaded'
      });
    }

    const imageUrls = req.files.map(file => 
      getFileUrl(req, file.filename, 'foods')
    );

    const { foodId } = req.body;

    // Update food item with image URLs if foodId provided
    if (foodId) {
      await Food.findByIdAndUpdate(foodId, {
        images: imageUrls
      });
    }

    res.json({
      success: true,
      data: imageUrls
    });
  } catch (error) {
    console.error('Food images upload error:', error);
    res.status(500).json({
      success: false,
      message: 'Food images upload failed',
      error: error.message
    });
  }
});

// Delete image
router.delete('/image', auth, async (req, res) => {
  try {
    const { imageUrl } = req.body;
    
    if (!imageUrl) {
      return res.status(400).json({
        success: false,
        message: 'Image URL is required'
      });
    }

    // Extract filename from URL
    const filename = path.basename(imageUrl);
    const filePath = path.join(uploadsDir, filename);

    // Delete file if it exists
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }

    res.json({
      success: true,
      message: 'Image deleted successfully'
    });
  } catch (error) {
    console.error('Delete image error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete image',
      error: error.message
    });
  }
});

module.exports = router;
