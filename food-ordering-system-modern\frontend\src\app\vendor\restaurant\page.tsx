'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Store, 
  Edit, 
  Save, 
  Upload, 
  MapPin, 
  Clock, 
  Phone, 
  Mail,
  Star,
  DollarSign,
  Users,
  TrendingUp
} from 'lucide-react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import Image from 'next/image';
import VendorSidebar from '@/components/VendorSidebar';
import { vendorAPI } from '@/lib/api';
import { formatCurrency } from '@/lib/utils';
import { toast } from 'react-hot-toast';

const VendorRestaurantPage = () => {
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    phone: '',
    email: '',
    cuisine: [],
    location: {
      address: {
        street: '',
        city: '',
        state: '',
        zipCode: ''
      },
      coordinates: {
        latitude: 19.0760, // Default to Mumbai coordinates
        longitude: 72.8777
      }
    },
    operatingHours: [],
    pricing: {
      deliveryFee: 0,
      minimumOrder: 0,
      packagingFee: 0
    }
  });

  const queryClient = useQueryClient();

  // Fetch restaurant data
  const { data: restaurantData, isLoading } = useQuery({
    queryKey: ['vendor-restaurant'],
    queryFn: () => vendorAPI.getRestaurant(),
  });

  const restaurant = restaurantData?.data?.data;

  // Create restaurant mutation
  const createRestaurantMutation = useMutation({
    mutationFn: (data: any) => vendorAPI.createRestaurant(data),
    onSuccess: () => {
      toast.success('Restaurant created successfully!');
      setIsEditing(false);
      queryClient.invalidateQueries({ queryKey: ['vendor-restaurant'] });
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to create restaurant');
    },
  });

  // Update restaurant mutation
  const updateRestaurantMutation = useMutation({
    mutationFn: (data: any) => vendorAPI.updateRestaurant(data),
    onSuccess: () => {
      toast.success('Restaurant updated successfully!');
      setIsEditing(false);
      queryClient.invalidateQueries({ queryKey: ['vendor-restaurant'] });
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to update restaurant');
    },
  });

  useEffect(() => {
    if (restaurant) {
      setFormData(restaurant);
      setIsEditing(false);
    } else {
      // If no restaurant exists, enable editing mode for creation
      setIsEditing(true);
    }
  }, [restaurant]);

  const validateForm = () => {
    const errors = [];

    if (!formData.name?.trim()) errors.push('Restaurant name is required');
    if (!formData.description?.trim()) errors.push('Description is required');
    if (!formData.phone?.trim()) errors.push('Phone number is required');
    if (formData.phone && !/^\d{10}$/.test(formData.phone)) errors.push('Phone number must be 10 digits');
    if (!formData.email?.trim()) errors.push('Email is required');
    if (formData.email && !/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/.test(formData.email)) errors.push('Please enter a valid email');
    if (!formData.location?.address?.street?.trim()) errors.push('Street address is required');
    if (!formData.location?.address?.city?.trim()) errors.push('City is required');
    if (!formData.location?.address?.state?.trim()) errors.push('State is required');
    if (!formData.location?.address?.zipCode?.trim()) errors.push('ZIP code is required');

    if (errors.length > 0) {
      toast.error(errors[0]); // Show first error
      return false;
    }
    return true;
  };

  const handleSave = () => {
    if (!validateForm()) return;

    // Ensure coordinates are properly set
    const dataToSend = {
      ...formData,
      location: {
        ...formData.location,
        coordinates: {
          latitude: formData.location?.coordinates?.latitude || 19.0760,
          longitude: formData.location?.coordinates?.longitude || 72.8777
        }
      }
    };

    console.log('Sending restaurant data:', dataToSend);

    if (restaurant) {
      updateRestaurantMutation.mutate(dataToSend);
    } else {
      createRestaurantMutation.mutate(dataToSend);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleNestedInputChange = (parent: string, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [parent]: {
        ...prev[parent as keyof typeof prev],
        [field]: value
      }
    }));
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex">
        <VendorSidebar />
        <div className="flex-1 p-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
            <div className="bg-white rounded-2xl shadow-md p-6">
              <div className="space-y-4">
                {[...Array(6)].map((_, i) => (
                  <div key={i} className="h-4 bg-gray-200 rounded"></div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex">
      <VendorSidebar />
      
      <div className="flex-1 p-8">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                <Store className="w-5 h-5 text-orange-600" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  {restaurant ? 'Restaurant Profile' : 'Set Up Your Restaurant'}
                </h1>
                <p className="text-gray-600">
                  {restaurant ? 'Manage your restaurant information' : 'Create your restaurant profile to start receiving orders'}
                </p>
              </div>
            </div>

            <div className="flex gap-3">
              {isEditing ? (
                <>
                  {restaurant && (
                    <button
                      onClick={() => setIsEditing(false)}
                      className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors duration-200"
                    >
                      Cancel
                    </button>
                  )}
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={handleSave}
                    disabled={updateRestaurantMutation.isPending || createRestaurantMutation.isPending}
                    className="flex items-center gap-2 px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors duration-200 disabled:opacity-50"
                  >
                    <Save className="w-4 h-4" />
                    {(updateRestaurantMutation.isPending || createRestaurantMutation.isPending)
                      ? 'Saving...'
                      : restaurant ? 'Save Changes' : 'Create Restaurant'
                    }
                  </motion.button>
                </>
              ) : (
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => setIsEditing(true)}
                  className="flex items-center gap-2 px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors duration-200"
                >
                  <Edit className="w-4 h-4" />
                  Edit Profile
                </motion.button>
              )}
            </div>
          </div>

          {restaurant ? (
            <div className="space-y-6">
              {/* Stats Cards */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="bg-white rounded-xl shadow-md p-6">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                      <Star className="w-5 h-5 text-green-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Rating</p>
                      <p className="text-xl font-bold text-gray-900">
                        {restaurant.ratings?.average?.toFixed(1) || '0.0'}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-white rounded-xl shadow-md p-6">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                      <Users className="w-5 h-5 text-blue-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Total Orders</p>
                      <p className="text-xl font-bold text-gray-900">{restaurant.totalOrders || 0}</p>
                    </div>
                  </div>
                </div>

                <div className="bg-white rounded-xl shadow-md p-6">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                      <DollarSign className="w-5 h-5 text-purple-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Revenue</p>
                      <p className="text-xl font-bold text-gray-900">
                        {formatCurrency(restaurant.totalRevenue || 0)}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-white rounded-xl shadow-md p-6">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                      <TrendingUp className="w-5 h-5 text-orange-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Status</p>
                      <p className={`text-sm font-medium ${
                        restaurant.status === 'approved' ? 'text-green-600' : 'text-yellow-600'
                      }`}>
                        {restaurant.status?.charAt(0).toUpperCase() + restaurant.status?.slice(1)}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Restaurant Information */}
              <div className="bg-white rounded-2xl shadow-md p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">Restaurant Information</h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Restaurant Name
                    </label>
                    {isEditing ? (
                      <input
                        type="text"
                        value={formData.name}
                        onChange={(e) => handleInputChange('name', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                      />
                    ) : (
                      <p className="text-gray-900">{restaurant.name}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Phone Number
                    </label>
                    {isEditing ? (
                      <input
                        type="tel"
                        value={formData.phone}
                        onChange={(e) => handleInputChange('phone', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                      />
                    ) : (
                      <p className="text-gray-900">{restaurant.phone}</p>
                    )}
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Description
                    </label>
                    {isEditing ? (
                      <textarea
                        value={formData.description}
                        onChange={(e) => handleInputChange('description', e.target.value)}
                        rows={3}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                      />
                    ) : (
                      <p className="text-gray-900">{restaurant.description}</p>
                    )}
                  </div>
                </div>
              </div>

              {/* Location Information */}
              <div className="bg-white rounded-2xl shadow-md p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">Location</h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Street Address
                    </label>
                    {isEditing ? (
                      <input
                        type="text"
                        value={formData.location?.address?.street || ''}
                        onChange={(e) => handleNestedInputChange('location', 'address', {
                          ...formData.location?.address,
                          street: e.target.value
                        })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                      />
                    ) : (
                      <p className="text-gray-900">{restaurant.location?.address?.street}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      City
                    </label>
                    {isEditing ? (
                      <input
                        type="text"
                        value={formData.location?.address?.city || ''}
                        onChange={(e) => handleNestedInputChange('location', 'address', {
                          ...formData.location?.address,
                          city: e.target.value
                        })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                      />
                    ) : (
                      <p className="text-gray-900">{restaurant.location?.address?.city}</p>
                    )}
                  </div>
                </div>
              </div>

              {/* Pricing Information */}
              <div className="bg-white rounded-2xl shadow-md p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">Pricing</h2>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Delivery Fee
                    </label>
                    {isEditing ? (
                      <input
                        type="number"
                        value={formData.pricing?.deliveryFee || 0}
                        onChange={(e) => handleNestedInputChange('pricing', 'deliveryFee', Number(e.target.value))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                      />
                    ) : (
                      <p className="text-gray-900">{formatCurrency(restaurant.pricing?.deliveryFee || 0)}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Minimum Order
                    </label>
                    {isEditing ? (
                      <input
                        type="number"
                        value={formData.pricing?.minimumOrder || 0}
                        onChange={(e) => handleNestedInputChange('pricing', 'minimumOrder', Number(e.target.value))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                      />
                    ) : (
                      <p className="text-gray-900">{formatCurrency(restaurant.pricing?.minimumOrder || 0)}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Packaging Fee
                    </label>
                    {isEditing ? (
                      <input
                        type="number"
                        value={formData.pricing?.packagingFee || 0}
                        onChange={(e) => handleNestedInputChange('pricing', 'packagingFee', Number(e.target.value))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                      />
                    ) : (
                      <p className="text-gray-900">{formatCurrency(restaurant.pricing?.packagingFee || 0)}</p>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="bg-white rounded-2xl shadow-md p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">Restaurant Information</h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Restaurant Name *
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder="Enter restaurant name"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Phone Number *
                  </label>
                  <input
                    type="tel"
                    value={formData.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    placeholder="Enter phone number"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    required
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Description *
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    placeholder="Describe your restaurant"
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Email
                  </label>
                  <input
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    placeholder="Enter email address"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Cuisine Types
                  </label>
                  <input
                    type="text"
                    value={Array.isArray(formData.cuisine) ? formData.cuisine.join(', ') : ''}
                    onChange={(e) => handleInputChange('cuisine', e.target.value.split(',').map(c => c.trim()).filter(c => c))}
                    placeholder="e.g., Italian, Chinese, Indian"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Address *
                  </label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <input
                      type="text"
                      value={formData.location?.address?.street || ''}
                      onChange={(e) => {
                        const newLocation = {
                          ...formData.location,
                          address: {
                            ...formData.location?.address,
                            street: e.target.value
                          }
                        };
                        handleInputChange('location', newLocation);
                      }}
                      placeholder="Street address"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                      required
                    />
                    <input
                      type="text"
                      value={formData.location?.address?.city || ''}
                      onChange={(e) => {
                        const newLocation = {
                          ...formData.location,
                          address: {
                            ...formData.location?.address,
                            city: e.target.value
                          }
                        };
                        handleInputChange('location', newLocation);
                      }}
                      placeholder="City"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                      required
                    />
                    <input
                      type="text"
                      value={formData.location?.address?.state || ''}
                      onChange={(e) => {
                        const newLocation = {
                          ...formData.location,
                          address: {
                            ...formData.location?.address,
                            state: e.target.value
                          }
                        };
                        handleInputChange('location', newLocation);
                      }}
                      placeholder="State"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                      required
                    />
                    <input
                      type="text"
                      value={formData.location?.address?.zipCode || ''}
                      onChange={(e) => {
                        const newLocation = {
                          ...formData.location,
                          address: {
                            ...formData.location?.address,
                            zipCode: e.target.value
                          }
                        };
                        handleInputChange('location', newLocation);
                      }}
                      placeholder="ZIP Code"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                      required
                    />
                  </div>
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Location Coordinates (Optional)
                  </label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <input
                      type="number"
                      value={formData.location?.coordinates?.latitude || 19.0760}
                      onChange={(e) => {
                        const newLocation = {
                          ...formData.location,
                          coordinates: {
                            ...formData.location?.coordinates,
                            latitude: Number(e.target.value)
                          }
                        };
                        handleInputChange('location', newLocation);
                      }}
                      placeholder="Latitude (e.g., 19.0760)"
                      step="any"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    />
                    <input
                      type="number"
                      value={formData.location?.coordinates?.longitude || 72.8777}
                      onChange={(e) => {
                        const newLocation = {
                          ...formData.location,
                          coordinates: {
                            ...formData.location?.coordinates,
                            longitude: Number(e.target.value)
                          }
                        };
                        handleInputChange('location', newLocation);
                      }}
                      placeholder="Longitude (e.g., 72.8777)"
                      step="any"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    />
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    Default coordinates are set to Mumbai. You can update them for accurate location.
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Delivery Fee
                  </label>
                  <input
                    type="number"
                    value={formData.pricing?.deliveryFee || 0}
                    onChange={(e) => handleNestedInputChange('pricing', 'deliveryFee', Number(e.target.value))}
                    placeholder="0.00"
                    step="0.01"
                    min="0"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Minimum Order
                  </label>
                  <input
                    type="number"
                    value={formData.pricing?.minimumOrder || 0}
                    onChange={(e) => handleNestedInputChange('pricing', 'minimumOrder', Number(e.target.value))}
                    placeholder="0.00"
                    step="0.01"
                    min="0"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  />
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default VendorRestaurantPage;
