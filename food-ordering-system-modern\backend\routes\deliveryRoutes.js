const express = require('express');
const { protect, authorize } = require('../middleware/auth');
const Order = require('../models/Order');
const User = require('../models/User');

const router = express.Router();

// @desc    Get delivery dashboard
// @route   GET /api/delivery/dashboard
// @access  Private (Delivery)
router.get('/dashboard', protect, authorize('delivery'), async (req, res, next) => {
  try {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    // Get today's deliveries
    const todayDeliveries = await Order.countDocuments({
      deliveryPartner: req.user._id,
      createdAt: { $gte: today, $lt: tomorrow },
      status: 'delivered'
    });

    // Get today's earnings
    const todayEarningsResult = await Order.aggregate([
      {
        $match: {
          deliveryPartner: req.user._id,
          createdAt: { $gte: today, $lt: tomorrow },
          status: 'delivered'
        }
      },
      {
        $group: {
          _id: null,
          total: { $sum: '$deliveryFee' }
        }
      }
    ]);

    const todayEarnings = todayEarningsResult.length > 0 ? todayEarningsResult[0].total : 0;

    // Get average rating (mock for now)
    const averageRating = 4.8;

    // Get completion rate
    const totalAssigned = await Order.countDocuments({
      deliveryPartner: req.user._id,
      createdAt: { $gte: today, $lt: tomorrow }
    });

    const completionRate = totalAssigned > 0 ? (todayDeliveries / totalAssigned) * 100 : 100;

    res.json({
      success: true,
      data: {
        todayDeliveries,
        todayEarnings,
        averageRating,
        completionRate: Math.round(completionRate * 10) / 10
      }
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get delivery orders
// @route   GET /api/delivery/orders
// @access  Private (Delivery)
router.get('/orders', protect, authorize('delivery'), async (req, res, next) => {
  try {
    const { status, page = 1, limit = 10 } = req.query;
    
    let query = {};
    
    if (status && status !== 'all') {
      query.status = status;
    } else {
      // Show orders that can be assigned to delivery partner
      query.status = { $in: ['confirmed', 'preparing', 'ready', 'assigned', 'picked_up'] };
    }

    // If delivery partner is assigned, show only their orders
    if (req.query.assigned === 'true') {
      query.deliveryPartner = req.user._id;
    }

    const orders = await Order.find(query)
      .populate('restaurant', 'name address phone')
      .populate('customer', 'name phone')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await Order.countDocuments(query);

    res.json({
      success: true,
      data: orders,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Accept delivery order
// @route   PATCH /api/delivery/orders/:id/accept
// @access  Private (Delivery)
router.patch('/orders/:id/accept', protect, authorize('delivery'), async (req, res, next) => {
  try {
    const order = await Order.findById(req.params.id);

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    if (order.deliveryPartner) {
      return res.status(400).json({
        success: false,
        message: 'Order already assigned to another delivery partner'
      });
    }

    order.deliveryPartner = req.user._id;
    order.status = 'assigned';
    await order.save();

    res.json({
      success: true,
      data: order,
      message: 'Order accepted successfully'
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Mark order as picked up
// @route   PATCH /api/delivery/orders/:id/pickup
// @access  Private (Delivery)
router.patch('/orders/:id/pickup', protect, authorize('delivery'), async (req, res, next) => {
  try {
    const order = await Order.findOne({
      _id: req.params.id,
      deliveryPartner: req.user._id
    });

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found or not assigned to you'
      });
    }

    order.status = 'picked_up';
    order.pickedUpAt = new Date();
    await order.save();

    res.json({
      success: true,
      data: order,
      message: 'Order marked as picked up'
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Mark order as delivered
// @route   PATCH /api/delivery/orders/:id/deliver
// @access  Private (Delivery)
router.patch('/orders/:id/deliver', protect, authorize('delivery'), async (req, res, next) => {
  try {
    const order = await Order.findOne({
      _id: req.params.id,
      deliveryPartner: req.user._id
    });

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found or not assigned to you'
      });
    }

    order.status = 'delivered';
    order.deliveredAt = new Date();
    await order.save();

    res.json({
      success: true,
      data: order,
      message: 'Order marked as delivered'
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get delivery earnings
// @route   GET /api/delivery/earnings
// @access  Private (Delivery)
router.get('/earnings', protect, authorize('delivery'), async (req, res, next) => {
  try {
    const { period = '7d' } = req.query;
    let days = 7;
    
    if (period === '30d') days = 30;
    else if (period === '90d') days = 90;
    
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const earnings = await Order.aggregate([
      {
        $match: {
          deliveryPartner: req.user._id,
          status: 'delivered',
          createdAt: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: {
            $dateToString: { format: '%Y-%m-%d', date: '$createdAt' }
          },
          dailyEarnings: { $sum: '$deliveryFee' },
          deliveries: { $sum: 1 }
        }
      },
      {
        $sort: { _id: 1 }
      }
    ]);

    const totalEarnings = earnings.reduce((sum, day) => sum + day.dailyEarnings, 0);
    const totalDeliveries = earnings.reduce((sum, day) => sum + day.deliveries, 0);

    res.json({
      success: true,
      data: {
        earnings,
        totalEarnings,
        totalDeliveries,
        period: `${days}d`
      }
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get delivery history
// @route   GET /api/delivery/history
// @access  Private (Delivery)
router.get('/history', protect, authorize('delivery'), async (req, res, next) => {
  try {
    const { page = 1, limit = 10 } = req.query;

    const orders = await Order.find({
      deliveryPartner: req.user._id,
      status: 'delivered'
    })
      .populate('restaurant', 'name address')
      .populate('customer', 'name')
      .sort({ deliveredAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await Order.countDocuments({
      deliveryPartner: req.user._id,
      status: 'delivered'
    });

    res.json({
      success: true,
      data: orders,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Update delivery partner location
// @route   PATCH /api/delivery/location
// @access  Private (Delivery)
router.patch('/location', protect, authorize('delivery'), async (req, res, next) => {
  try {
    const { latitude, longitude } = req.body;

    if (!latitude || !longitude) {
      return res.status(400).json({
        success: false,
        message: 'Latitude and longitude are required'
      });
    }

    const user = await User.findByIdAndUpdate(
      req.user._id,
      {
        location: {
          type: 'Point',
          coordinates: [longitude, latitude]
        },
        lastLocationUpdate: new Date()
      },
      { new: true }
    );

    res.json({
      success: true,
      data: user.location,
      message: 'Location updated successfully'
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Update delivery partner status
// @route   PATCH /api/delivery/status
// @access  Private (Delivery)
router.patch('/status', protect, authorize('delivery'), async (req, res, next) => {
  try {
    const { status } = req.body;

    if (!['available', 'busy', 'offline'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid status. Must be available, busy, or offline'
      });
    }

    const user = await User.findByIdAndUpdate(
      req.user._id,
      { deliveryStatus: status },
      { new: true }
    );

    res.json({
      success: true,
      data: { status: user.deliveryStatus },
      message: 'Status updated successfully'
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
