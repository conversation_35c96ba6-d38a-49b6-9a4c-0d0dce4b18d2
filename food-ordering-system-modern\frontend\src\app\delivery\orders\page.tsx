'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { useQuery } from '@tanstack/react-query';
import { 
  Package,
  MapPin,
  Clock,
  Phone,
  Navigation,
  CheckCircle,
  AlertCircle,
  Filter,
  Search
} from 'lucide-react';
import DeliverySidebar from '@/components/DeliverySidebar';
import { deliveryAPI } from '@/lib/api';

const DeliveryOrders = () => {
  const [statusFilter, setStatusFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  // Fetch delivery orders
  const { data: ordersData, isLoading } = useQuery({
    queryKey: ['delivery-orders', statusFilter],
    queryFn: () => deliveryAPI.getOrders({ status: statusFilter }),
    refetchInterval: 30000, // Refresh every 30 seconds
  });

  // Mock orders data
  const mockOrders = [
    {
      id: 'ORD001',
      orderNumber: '#12345',
      restaurant: {
        name: 'Spice Garden',
        address: '123 Restaurant St, Mumbai',
        phone: '+91 9876543210'
      },
      customer: {
        name: '<PERSON>',
        address: '456 Customer Ave, Mumbai',
        phone: '+91 9876543211'
      },
      items: [
        { name: 'Chicken Biryani', quantity: 2 },
        { name: 'Raita', quantity: 1 }
      ],
      amount: 450,
      distance: '2.5 km',
      status: 'ready_for_pickup',
      estimatedTime: '15 mins',
      createdAt: new Date().toISOString(),
      priority: 'normal'
    },
    {
      id: 'ORD002',
      orderNumber: '#12346',
      restaurant: {
        name: 'Pizza Palace',
        address: '789 Pizza Rd, Mumbai',
        phone: '+91 9876543212'
      },
      customer: {
        name: 'Sarah Smith',
        address: '321 Home St, Mumbai',
        phone: '+91 9876543213'
      },
      items: [
        { name: 'Margherita Pizza', quantity: 1 },
        { name: 'Garlic Bread', quantity: 2 }
      ],
      amount: 680,
      distance: '1.8 km',
      status: 'picked_up',
      estimatedTime: '8 mins',
      createdAt: new Date(Date.now() - 1800000).toISOString(),
      priority: 'high'
    },
    {
      id: 'ORD003',
      orderNumber: '#12347',
      restaurant: {
        name: 'Burger Hub',
        address: '555 Burger Blvd, Mumbai',
        phone: '+91 9876543214'
      },
      customer: {
        name: 'Mike Johnson',
        address: '999 Delivery Dr, Mumbai',
        phone: '+91 9876543215'
      },
      items: [
        { name: 'Cheese Burger', quantity: 1 },
        { name: 'Fries', quantity: 1 }
      ],
      amount: 320,
      distance: '3.2 km',
      status: 'assigned',
      estimatedTime: '20 mins',
      createdAt: new Date(Date.now() - 600000).toISOString(),
      priority: 'normal'
    }
  ];

  const statusOptions = [
    { value: 'all', label: 'All Orders' },
    { value: 'assigned', label: 'Assigned' },
    { value: 'ready_for_pickup', label: 'Ready for Pickup' },
    { value: 'picked_up', label: 'Picked Up' },
    { value: 'delivered', label: 'Delivered' },
  ];

  const orders = ordersData?.data || mockOrders;

  const filteredOrders = orders.filter(order => {
    const matchesStatus = statusFilter === 'all' || order.status === statusFilter;
    const matchesSearch = order.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.restaurant.name.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesStatus && matchesSearch;
  });

  const getStatusColor = (status) => {
    switch (status) {
      case 'assigned':
        return 'bg-blue-100 text-blue-800';
      case 'ready_for_pickup':
        return 'bg-orange-100 text-orange-800';
      case 'picked_up':
        return 'bg-purple-100 text-purple-800';
      case 'delivered':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800';
      case 'normal':
        return 'bg-gray-100 text-gray-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleAcceptOrder = (orderId) => {
    console.log('Accept order:', orderId);
    // Implement accept order functionality
  };

  const handlePickupOrder = (orderId) => {
    console.log('Pickup order:', orderId);
    // Implement pickup order functionality
  };

  const handleDeliverOrder = (orderId) => {
    console.log('Deliver order:', orderId);
    // Implement deliver order functionality
  };

  const formatTime = (dateString) => {
    return new Date(dateString).toLocaleTimeString('en-IN', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex">
        <DeliverySidebar />
        <div className="flex-1 p-8">
          <div className="max-w-6xl mx-auto">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
              <div className="space-y-4">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="h-48 bg-gray-200 rounded-lg"></div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex">
      <DeliverySidebar />
      
      <div className="flex-1 p-8">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <Package className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Active Orders</h1>
                <p className="text-gray-600">Manage your delivery assignments</p>
              </div>
            </div>
          </div>

          {/* Filters */}
          <div className="bg-white rounded-2xl p-6 shadow-md mb-6">
            <div className="flex flex-col md:flex-row gap-4">
              {/* Search */}
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    type="text"
                    placeholder="Search by order number, customer, or restaurant..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              {/* Status Filter */}
              <div className="flex items-center gap-2">
                <Filter className="w-5 h-5 text-gray-400" />
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {statusOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Orders List */}
          <div className="space-y-6">
            {filteredOrders.map((order, index) => (
              <motion.div
                key={order.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-white rounded-2xl p-6 shadow-md hover:shadow-lg transition-shadow duration-200"
              >
                {/* Order Header */}
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                      <Package className="w-6 h-6 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">{order.orderNumber}</h3>
                      <p className="text-sm text-gray-600">Ordered at {formatTime(order.createdAt)}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${getPriorityColor(order.priority)}`}>
                      {order.priority.toUpperCase()}
                    </span>
                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                      {order.status.replace('_', ' ').toUpperCase()}
                    </span>
                  </div>
                </div>

                {/* Order Details */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-4">
                  {/* Restaurant Info */}
                  <div className="space-y-3">
                    <h4 className="font-medium text-gray-900">Pickup from:</h4>
                    <div className="space-y-2">
                      <p className="font-medium text-gray-900">{order.restaurant.name}</p>
                      <div className="flex items-center gap-2">
                        <MapPin className="w-4 h-4 text-gray-400" />
                        <span className="text-sm text-gray-600">{order.restaurant.address}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Phone className="w-4 h-4 text-gray-400" />
                        <span className="text-sm text-gray-600">{order.restaurant.phone}</span>
                      </div>
                    </div>
                  </div>

                  {/* Customer Info */}
                  <div className="space-y-3">
                    <h4 className="font-medium text-gray-900">Deliver to:</h4>
                    <div className="space-y-2">
                      <p className="font-medium text-gray-900">{order.customer.name}</p>
                      <div className="flex items-center gap-2">
                        <MapPin className="w-4 h-4 text-gray-400" />
                        <span className="text-sm text-gray-600">{order.customer.address}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Phone className="w-4 h-4 text-gray-400" />
                        <span className="text-sm text-gray-600">{order.customer.phone}</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Order Items */}
                <div className="mb-4">
                  <h4 className="font-medium text-gray-900 mb-2">Items:</h4>
                  <div className="flex flex-wrap gap-2">
                    {order.items.map((item, idx) => (
                      <span key={idx} className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm">
                        {item.quantity}x {item.name}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Order Footer */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-6">
                    <div className="flex items-center gap-2">
                      <Navigation className="w-4 h-4 text-gray-400" />
                      <span className="text-sm text-gray-600">{order.distance}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="w-4 h-4 text-gray-400" />
                      <span className="text-sm text-gray-600">ETA: {order.estimatedTime}</span>
                    </div>
                    <div className="font-semibold text-gray-900">₹{order.amount}</div>
                  </div>

                  <div className="flex items-center gap-3">
                    {order.status === 'assigned' && (
                      <button
                        onClick={() => handleAcceptOrder(order.id)}
                        className="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors duration-200"
                      >
                        Accept Order
                      </button>
                    )}
                    {order.status === 'ready_for_pickup' && (
                      <button
                        onClick={() => handlePickupOrder(order.id)}
                        className="bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors duration-200"
                      >
                        Mark as Picked Up
                      </button>
                    )}
                    {order.status === 'picked_up' && (
                      <button
                        onClick={() => handleDeliverOrder(order.id)}
                        className="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors duration-200"
                      >
                        Mark as Delivered
                      </button>
                    )}
                    <button className="border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                      View on Map
                    </button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {filteredOrders.length === 0 && (
            <div className="text-center py-12">
              <Package className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No orders found</h3>
              <p className="text-gray-600">Try adjusting your search or filter criteria.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DeliveryOrders;
