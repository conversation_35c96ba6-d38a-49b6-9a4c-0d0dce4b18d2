'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Plus,
  Search,
  Trash2,
  Eye,
  EyeOff,
  ChefHat,
  DollarSign,
  Clock,
  Star
} from 'lucide-react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import Image from 'next/image';
import VendorLayout from '@/components/VendorLayout';
import AddFoodModal from '@/components/AddFoodModal';
import { vendorAPI } from '@/lib/api';
import { formatCurrency, getOptimizedImageUrl } from '@/lib/utils';
import { toast } from 'react-hot-toast';

const VendorMenuPage = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [showAddModal, setShowAddModal] = useState(false);

  const queryClient = useQueryClient();

  // Fetch menu items
  const { data: foodsData, isLoading, error } = useQuery({
    queryKey: ['vendor-foods'],
    queryFn: () => vendorAPI.getFoods(),
  });

  const foods = Array.isArray(foodsData?.data) ? foodsData.data : [];

  // Filter foods
  const filteredFoods = foods.filter((food: any) => {
    const matchesCategory = selectedCategory === 'all' || food.category === selectedCategory;
    const matchesSearch = !searchQuery ||
      food.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      food.description.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  // Get unique categories
  const categories = ['all', ...new Set(foods.map((food: any) => food.category))];

  // Toggle food availability
  const toggleAvailabilityMutation = useMutation({
    mutationFn: ({ foodId, data }: { foodId: string; data: any }) => vendorAPI.updateFood(foodId, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['vendor-foods'] });
      toast.success('Food availability updated!');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to update availability');
    },
  });

  // Delete food
  const deleteFoodMutation = useMutation({
    mutationFn: (foodId: string) => vendorAPI.deleteFood(foodId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['vendor-foods'] });
      toast.success('Food item deleted!');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to delete food item');
    },
  });

  const handleToggleAvailability = (food: any) => {
    const updatedData = { isAvailable: !food.isAvailable };
    toggleAvailabilityMutation.mutate({ foodId: food._id, data: updatedData });
  };

  const handleDeleteFood = (foodId: string) => {
    if (window.confirm('Are you sure you want to delete this food item?')) {
      deleteFoodMutation.mutate(foodId);
    }
  };

  if (isLoading) {
    return (
      <VendorLayout title="Menu Management" subtitle="Manage your food items and menu">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="bg-white rounded-2xl shadow-md p-6">
                <div className="h-32 bg-gray-200 rounded-lg mb-4"></div>
                <div className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded"></div>
                  <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </VendorLayout>
    );
  }

  if (error) {
    return (
      <VendorLayout title="Menu Management" subtitle="Manage your food items and menu">
        <div className="text-center py-12">
          <ChefHat className="w-16 h-16 text-red-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Error Loading Menu</h3>
          <p className="text-gray-600 mb-4">
            There was an error loading your menu items. Please try again.
          </p>
          <button
            onClick={() => window.location.reload()}
            className="bg-orange-500 text-white px-6 py-2 rounded-lg hover:bg-orange-600 transition-colors"
          >
            Retry
          </button>
        </div>
      </VendorLayout>
    );
  }

  const headerActions = (
    <motion.button
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      onClick={() => setShowAddModal(true)}
      className="flex items-center gap-2 px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors duration-200"
    >
      <Plus className="w-4 h-4" />
      Add Food Item
    </motion.button>
  );

  return (
    <VendorLayout
      title="Menu Management"
      subtitle="Manage your food items and menu"
      headerActions={headerActions}
    >

          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className="bg-white rounded-xl shadow-md p-6">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <ChefHat className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Total Items</p>
                  <p className="text-xl font-bold text-gray-900">{foods.length}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-md p-6">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                  <Eye className="w-5 h-5 text-green-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Available</p>
                  <p className="text-xl font-bold text-gray-900">
                    {foods.filter((food: any) => food.isAvailable).length}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-md p-6">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                  <EyeOff className="w-5 h-5 text-red-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Unavailable</p>
                  <p className="text-xl font-bold text-gray-900">
                    {foods.filter((food: any) => !food.isAvailable).length}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-md p-6">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                  <DollarSign className="w-5 h-5 text-purple-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Avg. Price</p>
                  <p className="text-xl font-bold text-gray-900">
                    {foods.length > 0 
                      ? formatCurrency(foods.reduce((sum: number, food: any) => sum + food.price, 0) / foods.length)
                      : formatCurrency(0)
                    }
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Filters */}
          <div className="bg-white rounded-2xl shadow-md p-6 mb-8">
            <div className="flex flex-col md:flex-row gap-4">
              {/* Search */}
              <div className="flex-1">
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Search className="h-4 w-4 text-gray-400" />
                  </div>
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    placeholder="Search food items..."
                  />
                </div>
              </div>

              {/* Category Filter */}
              <div className="md:w-48">
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                >
                  {categories.map((category) => (
                    <option key={category} value={category}>
                      {category === 'all' ? 'All Categories' : category.replace('-', ' ').toUpperCase()}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Food Items Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <AnimatePresence>
              {filteredFoods.map((food: any) => (
                <motion.div
                  key={food._id}
                  layout
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.9 }}
                  transition={{ duration: 0.3 }}
                  className="bg-white rounded-2xl shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300"
                >
                  {/* Food Image */}
                  <div className="relative h-48">
                    <Image
                      src={getOptimizedImageUrl(food.images?.[0] || '/images/food-placeholder.jpg')}
                      alt={food.name}
                      fill
                      className="object-cover"
                    />
                    
                    {/* Availability Badge */}
                    <div className="absolute top-3 left-3">
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                        food.isAvailable 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {food.isAvailable ? 'Available' : 'Unavailable'}
                      </span>
                    </div>

                    {/* Actions */}
                    <div className="absolute top-3 right-3 flex gap-2">
                      <button
                        onClick={() => handleToggleAvailability(food)}
                        className="w-8 h-8 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center shadow-md hover:bg-white transition-colors duration-200"
                      >
                        {food.isAvailable ? (
                          <EyeOff className="w-4 h-4 text-gray-600" />
                        ) : (
                          <Eye className="w-4 h-4 text-gray-600" />
                        )}
                      </button>

                      <button
                        onClick={() => handleDeleteFood(food._id)}
                        className="w-8 h-8 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center shadow-md hover:bg-white transition-colors duration-200"
                      >
                        <Trash2 className="w-4 h-4 text-red-600" />
                      </button>
                    </div>
                  </div>

                  {/* Food Info */}
                  <div className="p-4">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">{food.name}</h3>
                    <p className="text-gray-600 text-sm mb-3 line-clamp-2">{food.description}</p>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <span className="text-lg font-bold text-orange-600">
                          {formatCurrency(food.price)}
                        </span>
                        <div className="flex items-center gap-1 text-sm text-gray-500">
                          <Clock className="w-3 h-3" />
                          <span>{food.preparationTime || 15}m</span>
                        </div>
                      </div>
                      
                      {food.ratings?.average > 0 && (
                        <div className="flex items-center gap-1">
                          <Star className="w-4 h-4 text-yellow-400 fill-current" />
                          <span className="text-sm font-medium text-gray-700">
                            {food.ratings.average.toFixed(1)}
                          </span>
                        </div>
                      )}
                    </div>

                    <div className="mt-3">
                      <span className="px-2 py-1 bg-gray-100 text-gray-700 text-xs font-medium rounded-full capitalize">
                        {food.category?.replace('-', ' ')}
                      </span>
                    </div>
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>

          {filteredFoods.length === 0 && (
            <div className="text-center py-12">
              <ChefHat className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No food items found</h3>
              <p className="text-gray-600 mb-6">
                {searchQuery || selectedCategory !== 'all' 
                  ? 'Try adjusting your search or filters'
                  : 'Start by adding your first food item'
                }
              </p>
              {!searchQuery && selectedCategory === 'all' && (
                <button
                  onClick={() => setShowAddModal(true)}
                  className="px-6 py-3 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors duration-200"
                >
                  Add Food Item
                </button>
              )}
            </div>
          )}

          {/* Add Food Modal */}
          <AddFoodModal
            isOpen={showAddModal}
            onClose={() => setShowAddModal(false)}
          />
        </VendorLayout>
  );
};

export default VendorMenuPage;
