'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { useQuery } from '@tanstack/react-query';
import { 
  DollarSign,
  TrendingUp,
  Calendar,
  Package,
  Clock,
  Download,
  CreditCard,
  BarChart3
} from 'lucide-react';
import DeliverySidebar from '@/components/DeliverySidebar';
import { deliveryAPI } from '@/lib/api';

const DeliveryEarningsPage = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('7d');

  // Fetch earnings data
  const { data: earningsData, isLoading } = useQuery({
    queryKey: ['delivery-earnings', selectedPeriod],
    queryFn: () => deliveryAPI.getEarnings(selectedPeriod),
  });

  // Mock earnings data
  const mockEarnings = {
    totalEarnings: 2450,
    totalDeliveries: 28,
    period: '7d',
    earnings: [
      { _id: '2024-01-25', dailyEarnings: 320, deliveries: 4 },
      { _id: '2024-01-26', dailyEarnings: 280, deliveries: 3 },
      { _id: '2024-01-27', dailyEarnings: 450, deliveries: 5 },
      { _id: '2024-01-28', dailyEarnings: 380, deliveries: 4 },
      { _id: '2024-01-29', dailyEarnings: 520, deliveries: 6 },
      { _id: '2024-01-30', dailyEarnings: 350, deliveries: 4 },
      { _id: '2024-01-31', dailyEarnings: 150, deliveries: 2 }
    ]
  };

  const earnings = earningsData?.data || mockEarnings;
  const averagePerDelivery = earnings.totalEarnings / earnings.totalDeliveries || 0;
  const averagePerDay = earnings.totalEarnings / 7 || 0;

  const periodOptions = [
    { value: '7d', label: 'Last 7 Days' },
    { value: '30d', label: 'Last 30 Days' },
    { value: '90d', label: 'Last 90 Days' }
  ];

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      month: 'short',
      day: 'numeric'
    });
  };

  const getMaxEarnings = () => {
    return Math.max(...earnings.earnings.map((e: any) => e.dailyEarnings));
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex">
        <DeliverySidebar />
        <div className="flex-1 p-8">
          <div className="max-w-6xl mx-auto">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="h-32 bg-gray-200 rounded-lg"></div>
                ))}
              </div>
              <div className="h-64 bg-gray-200 rounded-lg"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex">
      <DeliverySidebar />
      
      <div className="flex-1 p-8">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                <DollarSign className="w-5 h-5 text-green-600" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Earnings</h1>
                <p className="text-gray-600">Track your delivery earnings and performance</p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <select
                value={selectedPeriod}
                onChange={(e) => setSelectedPeriod(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              >
                {periodOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              <button className="flex items-center gap-2 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors">
                <Download className="w-4 h-4" />
                Export
              </button>
            </div>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white rounded-2xl p-6 shadow-md"
            >
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <DollarSign className="w-6 h-6 text-green-600" />
                </div>
                <TrendingUp className="w-5 h-5 text-green-500" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-1">₹{earnings.totalEarnings}</h3>
              <p className="text-gray-600 text-sm">Total Earnings</p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="bg-white rounded-2xl p-6 shadow-md"
            >
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Package className="w-6 h-6 text-blue-600" />
                </div>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-1">{earnings.totalDeliveries}</h3>
              <p className="text-gray-600 text-sm">Total Deliveries</p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-white rounded-2xl p-6 shadow-md"
            >
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                  <BarChart3 className="w-6 h-6 text-purple-600" />
                </div>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-1">₹{averagePerDelivery.toFixed(0)}</h3>
              <p className="text-gray-600 text-sm">Avg per Delivery</p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="bg-white rounded-2xl p-6 shadow-md"
            >
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                  <Calendar className="w-6 h-6 text-orange-600" />
                </div>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-1">₹{averagePerDay.toFixed(0)}</h3>
              <p className="text-gray-600 text-sm">Avg per Day</p>
            </motion.div>
          </div>

          {/* Earnings Chart */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="bg-white rounded-2xl p-6 shadow-md mb-8"
          >
            <h2 className="text-lg font-semibold text-gray-900 mb-6">Daily Earnings</h2>
            
            <div className="h-64 flex items-end justify-between gap-2">
              {earnings.earnings.map((day: any, index: number) => {
                const height = (day.dailyEarnings / getMaxEarnings()) * 100;
                return (
                  <div key={day._id} className="flex-1 flex flex-col items-center">
                    <div className="w-full flex flex-col items-center">
                      <motion.div
                        initial={{ height: 0 }}
                        animate={{ height: `${height}%` }}
                        transition={{ duration: 0.8, delay: index * 0.1 }}
                        className="w-full bg-gradient-to-t from-green-500 to-green-400 rounded-t-lg min-h-[20px] relative group cursor-pointer"
                      >
                        {/* Tooltip */}
                        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
                          <div className="text-center">
                            <div className="font-semibold">₹{day.dailyEarnings}</div>
                            <div className="text-xs">{day.deliveries} deliveries</div>
                          </div>
                          <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900"></div>
                        </div>
                      </motion.div>
                      <div className="mt-2 text-xs text-gray-600 text-center">
                        {formatDate(day._id)}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </motion.div>

          {/* Detailed Breakdown */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Daily Breakdown */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
              className="bg-white rounded-2xl p-6 shadow-md"
            >
              <h2 className="text-lg font-semibold text-gray-900 mb-6">Daily Breakdown</h2>
              
              <div className="space-y-4">
                {earnings.earnings.map((day: any, index: number) => (
                  <motion.div
                    key={day._id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.6 + index * 0.1 }}
                    className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-green-300 transition-colors"
                  >
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                        <Calendar className="w-5 h-5 text-green-600" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">{formatDate(day._id)}</p>
                        <p className="text-sm text-gray-600">{day.deliveries} deliveries</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-gray-900">₹{day.dailyEarnings}</p>
                      <p className="text-sm text-gray-600">₹{(day.dailyEarnings / day.deliveries).toFixed(0)}/delivery</p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            {/* Payment Summary */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
              className="bg-white rounded-2xl p-6 shadow-md"
            >
              <h2 className="text-lg font-semibold text-gray-900 mb-6">Payment Summary</h2>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 bg-green-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                      <CreditCard className="w-5 h-5 text-green-600" />
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">Total Earnings</p>
                      <p className="text-sm text-gray-600">Before deductions</p>
                    </div>
                  </div>
                  <p className="font-semibold text-gray-900">₹{earnings.totalEarnings}</p>
                </div>

                <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                      <DollarSign className="w-5 h-5 text-red-600" />
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">Platform Fee</p>
                      <p className="text-sm text-gray-600">5% commission</p>
                    </div>
                  </div>
                  <p className="font-semibold text-red-600">-₹{(earnings.totalEarnings * 0.05).toFixed(0)}</p>
                </div>

                <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                      <Package className="w-5 h-5 text-blue-600" />
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">Fuel Allowance</p>
                      <p className="text-sm text-gray-600">₹10 per delivery</p>
                    </div>
                  </div>
                  <p className="font-semibold text-blue-600">+₹{earnings.totalDeliveries * 10}</p>
                </div>

                <div className="border-t pt-4">
                  <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div>
                      <p className="font-semibold text-gray-900">Net Earnings</p>
                      <p className="text-sm text-gray-600">Amount to be paid</p>
                    </div>
                    <p className="text-xl font-bold text-green-600">
                      ₹{(earnings.totalEarnings * 0.95 + earnings.totalDeliveries * 10).toFixed(0)}
                    </p>
                  </div>
                </div>
              </div>

              <div className="mt-6">
                <button className="w-full bg-green-500 text-white py-3 px-4 rounded-lg hover:bg-green-600 transition-colors font-medium">
                  Request Payout
                </button>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DeliveryEarningsPage;
