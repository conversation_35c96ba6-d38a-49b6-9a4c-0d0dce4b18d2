const express = require('express');
const { protect } = require('../middleware/auth');

const router = express.Router();

// Mock offers data (in real app, this would come from database)
const mockOffers = [
  {
    id: 'WELCOME50',
    title: 'Welcome Offer',
    description: 'Get 50% off on your first order',
    code: 'WELCOME50',
    type: 'percentage',
    value: 50,
    minOrderAmount: 200,
    maxDiscount: 150,
    validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
    isActive: true,
    category: 'new_user',
    usageLimit: 1,
    restaurants: [], // Empty means valid for all restaurants
    image: '/offers/welcome.jpg'
  },
  {
    id: 'SAVE20',
    title: 'Save Big',
    description: 'Flat ₹20 off on orders above ₹300',
    code: 'SAVE20',
    type: 'fixed',
    value: 20,
    minOrderAmount: 300,
    maxDiscount: 20,
    validUntil: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000), // 15 days from now
    isActive: true,
    category: 'general',
    usageLimit: 5,
    restaurants: [],
    image: '/offers/save20.jpg'
  },
  {
    id: 'WEEKEND30',
    title: 'Weekend Special',
    description: '30% off on weekend orders',
    code: 'WEEKEND30',
    type: 'percentage',
    value: 30,
    minOrderAmount: 250,
    maxDiscount: 100,
    validUntil: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
    isActive: true,
    category: 'weekend',
    usageLimit: 2,
    restaurants: [],
    image: '/offers/weekend.jpg'
  },
  {
    id: 'PIZZA25',
    title: 'Pizza Lovers',
    description: '25% off on all pizza orders',
    code: 'PIZZA25',
    type: 'percentage',
    value: 25,
    minOrderAmount: 150,
    maxDiscount: 75,
    validUntil: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000), // 10 days from now
    isActive: true,
    category: 'cuisine',
    usageLimit: 3,
    restaurants: [],
    image: '/offers/pizza.jpg'
  },
  {
    id: 'FREEDELIV',
    title: 'Free Delivery',
    description: 'Free delivery on orders above ₹199',
    code: 'FREEDELIV',
    type: 'free_delivery',
    value: 0,
    minOrderAmount: 199,
    maxDiscount: 40,
    validUntil: new Date(Date.now() + 20 * 24 * 60 * 60 * 1000), // 20 days from now
    isActive: true,
    category: 'delivery',
    usageLimit: 10,
    restaurants: [],
    image: '/offers/free-delivery.jpg'
  }
];

// @desc    Get all offers
// @route   GET /api/offers
// @access  Public
router.get('/', async (req, res, next) => {
  try {
    const { category, active = 'true' } = req.query;
    
    let offers = [...mockOffers];
    
    // Filter by active status
    if (active === 'true') {
      offers = offers.filter(offer => offer.isActive && new Date(offer.validUntil) > new Date());
    }
    
    // Filter by category
    if (category && category !== 'all') {
      offers = offers.filter(offer => offer.category === category);
    }
    
    // Sort by value (highest first)
    offers.sort((a, b) => b.value - a.value);
    
    res.json({
      success: true,
      data: offers
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get offer by ID
// @route   GET /api/offers/:id
// @access  Public
router.get('/:id', async (req, res, next) => {
  try {
    const offer = mockOffers.find(o => o.id === req.params.id);
    
    if (!offer) {
      return res.status(404).json({
        success: false,
        message: 'Offer not found'
      });
    }
    
    if (!offer.isActive || new Date(offer.validUntil) <= new Date()) {
      return res.status(400).json({
        success: false,
        message: 'Offer is no longer valid'
      });
    }
    
    res.json({
      success: true,
      data: offer
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Validate offer code
// @route   POST /api/offers/validate
// @access  Private
router.post('/validate', protect, async (req, res, next) => {
  try {
    const { code } = req.body;
    
    if (!code) {
      return res.status(400).json({
        success: false,
        message: 'Offer code is required'
      });
    }
    
    const offer = mockOffers.find(o => o.code.toLowerCase() === code.toLowerCase());
    
    if (!offer) {
      return res.status(404).json({
        success: false,
        message: 'Invalid offer code'
      });
    }
    
    if (!offer.isActive) {
      return res.status(400).json({
        success: false,
        message: 'Offer is no longer active'
      });
    }
    
    if (new Date(offer.validUntil) <= new Date()) {
      return res.status(400).json({
        success: false,
        message: 'Offer has expired'
      });
    }
    
    // In real app, check user usage limit here
    
    res.json({
      success: true,
      data: offer,
      message: 'Offer code is valid'
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Apply offer to order
// @route   POST /api/offers/apply
// @access  Private
router.post('/apply', protect, async (req, res, next) => {
  try {
    const { code, orderAmount, deliveryFee = 0, restaurantId } = req.body;
    
    if (!code || !orderAmount) {
      return res.status(400).json({
        success: false,
        message: 'Offer code and order amount are required'
      });
    }
    
    const offer = mockOffers.find(o => o.code.toLowerCase() === code.toLowerCase());
    
    if (!offer) {
      return res.status(404).json({
        success: false,
        message: 'Invalid offer code'
      });
    }
    
    if (!offer.isActive || new Date(offer.validUntil) <= new Date()) {
      return res.status(400).json({
        success: false,
        message: 'Offer is no longer valid'
      });
    }
    
    if (orderAmount < offer.minOrderAmount) {
      return res.status(400).json({
        success: false,
        message: `Minimum order amount is ₹${offer.minOrderAmount}`
      });
    }
    
    // Check if offer is valid for the restaurant
    if (offer.restaurants.length > 0 && !offer.restaurants.includes(restaurantId)) {
      return res.status(400).json({
        success: false,
        message: 'Offer is not valid for this restaurant'
      });
    }
    
    let discount = 0;
    let freeDelivery = false;
    
    switch (offer.type) {
      case 'percentage':
        discount = Math.min((orderAmount * offer.value) / 100, offer.maxDiscount);
        break;
      case 'fixed':
        discount = Math.min(offer.value, offer.maxDiscount);
        break;
      case 'free_delivery':
        freeDelivery = true;
        discount = deliveryFee;
        break;
      default:
        discount = 0;
    }
    
    const finalAmount = orderAmount - discount + (freeDelivery ? 0 : deliveryFee);
    
    res.json({
      success: true,
      data: {
        offer,
        discount,
        freeDelivery,
        originalAmount: orderAmount,
        finalAmount,
        savings: discount + (freeDelivery ? deliveryFee : 0)
      },
      message: 'Offer applied successfully'
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get user's available offers
// @route   GET /api/offers/user/available
// @access  Private
router.get('/user/available', protect, async (req, res, next) => {
  try {
    // In real app, filter based on user's order history, location, etc.
    let availableOffers = mockOffers.filter(offer => 
      offer.isActive && new Date(offer.validUntil) > new Date()
    );
    
    // For new users, show welcome offers
    // For existing users, show general offers
    // This logic would be more sophisticated in a real app
    
    res.json({
      success: true,
      data: availableOffers
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
