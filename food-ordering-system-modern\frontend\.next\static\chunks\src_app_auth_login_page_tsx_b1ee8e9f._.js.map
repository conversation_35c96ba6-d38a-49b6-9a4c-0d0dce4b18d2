{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/project%20food-order%206k/food-ordering-system-modern/frontend/src/app/auth/login/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { useRouter, useSearchParams } from 'next/navigation';\nimport { useForm } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport { z } from 'zod';\nimport { Eye, EyeOff, Mail, Lock, ChefHat, ArrowLeft } from 'lucide-react';\nimport Link from 'next/link';\nimport useAuthStore from '@/store/useAuthStore';\nimport { toast } from 'react-hot-toast';\n\nconst loginSchema = z.object({\n  email: z.string().email('Please enter a valid email address'),\n  password: z.string().min(6, 'Password must be at least 6 characters'),\n});\n\ntype LoginFormData = z.infer<typeof loginSchema>;\n\nconst LoginPage = () => {\n  const [showPassword, setShowPassword] = useState(false);\n  const [isLoading, setIsLoading] = useState(false);\n  const router = useRouter();\n  const searchParams = useSearchParams();\n  const { login } = useAuthStore();\n\n  const role = searchParams.get('role') || 'customer';\n\n  const getRoleConfig = () => {\n    switch (role) {\n      case 'admin':\n        return {\n          title: 'Admin Login',\n          description: 'Access the admin dashboard to manage the platform',\n          icon: '👨‍💼'\n        };\n      case 'delivery':\n        return {\n          title: 'Delivery Partner Login',\n          description: 'Sign in to start delivering orders and earning',\n          icon: '🚚'\n        };\n      case 'vendor':\n        return {\n          title: 'Restaurant Partner Login',\n          description: 'Manage your restaurant and track orders',\n          icon: '🏪'\n        };\n      default:\n        return {\n          title: 'Welcome Back!',\n          description: 'Sign in to your account to continue ordering',\n          icon: '👨‍🍳'\n        };\n    }\n  };\n\n  const roleConfig = getRoleConfig();\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors },\n  } = useForm<LoginFormData>({\n    resolver: zodResolver(loginSchema),\n  });\n\n  const onSubmit = async (data: LoginFormData) => {\n    setIsLoading(true);\n    try {\n      const result = await login(data);\n      if (result.success && result.user) {\n        // Role-based redirection\n        switch (result.user.role) {\n          case 'admin':\n            router.push('/admin/dashboard');\n            break;\n          case 'vendor':\n            router.push('/vendor/dashboard');\n            break;\n          case 'delivery':\n            router.push('/delivery/dashboard');\n            break;\n          case 'customer':\n          default:\n            router.push('/restaurants');\n            break;\n        }\n      } else {\n        toast.error(result.error || 'Login failed');\n      }\n    } catch (error) {\n      toast.error('Something went wrong. Please try again.');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-orange-50 via-white to-red-50 flex items-center justify-center p-4\">\n      <div className=\"w-full max-w-md\">\n        {/* Back Button */}\n        <motion.div\n          initial={{ opacity: 0, x: -20 }}\n          animate={{ opacity: 1, x: 0 }}\n          transition={{ duration: 0.5 }}\n          className=\"mb-8\"\n        >\n          <Link\n            href=\"/\"\n            className=\"inline-flex items-center gap-2 text-gray-600 hover:text-orange-500 transition-colors duration-200\"\n          >\n            <ArrowLeft className=\"w-5 h-5\" />\n            Back to Home\n          </Link>\n        </motion.div>\n\n        {/* Login Form */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          className=\"bg-white rounded-2xl shadow-xl p-8\"\n        >\n          {/* Header */}\n          <div className=\"text-center mb-8\">\n            <motion.div\n              initial={{ scale: 0 }}\n              animate={{ scale: 1 }}\n              transition={{ duration: 0.5, delay: 0.2 }}\n              className=\"w-16 h-16 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center mx-auto mb-4\"\n            >\n              <ChefHat className=\"w-8 h-8 text-white\" />\n            </motion.div>\n            <h1 className=\"text-2xl font-bold text-gray-900 mb-2\">\n              {roleConfig.title}\n            </h1>\n            <p className=\"text-gray-600\">\n              {roleConfig.description}\n            </p>\n          </div>\n\n          {/* Form */}\n          <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\n            {/* Email Field */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Email Address\n              </label>\n              <div className=\"relative\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                  <Mail className=\"h-5 w-5 text-gray-400\" />\n                </div>\n                <input\n                  {...register('email')}\n                  type=\"email\"\n                  className=\"block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200\"\n                  placeholder=\"Enter your email\"\n                />\n              </div>\n              {errors.email && (\n                <p className=\"mt-1 text-sm text-red-600\">{errors.email.message}</p>\n              )}\n            </div>\n\n            {/* Password Field */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Password\n              </label>\n              <div className=\"relative\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                  <Lock className=\"h-5 w-5 text-gray-400\" />\n                </div>\n                <input\n                  {...register('password')}\n                  type={showPassword ? 'text' : 'password'}\n                  className=\"block w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200\"\n                  placeholder=\"Enter your password\"\n                />\n                <button\n                  type=\"button\"\n                  onClick={() => setShowPassword(!showPassword)}\n                  className=\"absolute inset-y-0 right-0 pr-3 flex items-center\"\n                >\n                  {showPassword ? (\n                    <EyeOff className=\"h-5 w-5 text-gray-400 hover:text-gray-600\" />\n                  ) : (\n                    <Eye className=\"h-5 w-5 text-gray-400 hover:text-gray-600\" />\n                  )}\n                </button>\n              </div>\n              {errors.password && (\n                <p className=\"mt-1 text-sm text-red-600\">{errors.password.message}</p>\n              )}\n            </div>\n\n            {/* Forgot Password */}\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center\">\n                <input\n                  id=\"remember-me\"\n                  name=\"remember-me\"\n                  type=\"checkbox\"\n                  className=\"h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded\"\n                />\n                <label htmlFor=\"remember-me\" className=\"ml-2 block text-sm text-gray-700\">\n                  Remember me\n                </label>\n              </div>\n              <Link\n                href=\"/auth/forgot-password\"\n                className=\"text-sm text-orange-600 hover:text-orange-500 font-medium\"\n              >\n                Forgot password?\n              </Link>\n            </div>\n\n            {/* Submit Button */}\n            <motion.button\n              whileHover={{ scale: 1.02 }}\n              whileTap={{ scale: 0.98 }}\n              type=\"submit\"\n              disabled={isLoading}\n              className=\"w-full bg-gradient-to-r from-orange-500 to-red-500 text-white py-3 px-4 rounded-lg font-medium hover:shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {isLoading ? (\n                <div className=\"flex items-center justify-center gap-2\">\n                  <div className=\"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin\" />\n                  Signing In...\n                </div>\n              ) : (\n                'Sign In'\n              )}\n            </motion.button>\n          </form>\n\n          {/* Divider */}\n          <div className=\"mt-6\">\n            <div className=\"relative\">\n              <div className=\"absolute inset-0 flex items-center\">\n                <div className=\"w-full border-t border-gray-300\" />\n              </div>\n              <div className=\"relative flex justify-center text-sm\">\n                <span className=\"px-2 bg-white text-gray-500\">Or continue with</span>\n              </div>\n            </div>\n          </div>\n\n          {/* Social Login */}\n          <div className=\"mt-6 grid grid-cols-2 gap-3\">\n            <button className=\"w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-lg shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 transition-colors duration-200\">\n              <span>Google</span>\n            </button>\n            <button className=\"w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-lg shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 transition-colors duration-200\">\n              <span>Facebook</span>\n            </button>\n          </div>\n\n          {/* Sign Up Link */}\n          <p className=\"mt-6 text-center text-sm text-gray-600\">\n            Don't have an account?{' '}\n            <Link\n              href=\"/auth/register\"\n              className=\"font-medium text-orange-600 hover:text-orange-500\"\n            >\n              Sign up for free\n            </Link>\n          </p>\n        </motion.div>\n      </div>\n    </div>\n  );\n};\n\nexport default LoginPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AAXA;;;;;;;;;;;AAaA,MAAM,cAAc,gLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC3B,OAAO,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC9B;AAIA,MAAM,YAAY;;IAChB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAY,AAAD;IAE7B,MAAM,OAAO,aAAa,GAAG,CAAC,WAAW;IAEzC,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,OAAO;oBACP,aAAa;oBACb,MAAM;gBACR;YACF,KAAK;gBACH,OAAO;oBACL,OAAO;oBACP,aAAa;oBACb,MAAM;gBACR;YACF,KAAK;gBACH,OAAO;oBACL,OAAO;oBACP,aAAa;oBACb,MAAM;gBACR;YACF;gBACE,OAAO;oBACL,OAAO;oBACP,aAAa;oBACb,MAAM;gBACR;QACJ;IACF;IAEA,MAAM,aAAa;IAEnB,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACtB,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAiB;QACzB,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,MAAM,WAAW,OAAO;QACtB,aAAa;QACb,IAAI;YACF,MAAM,SAAS,MAAM,MAAM;YAC3B,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;gBACjC,yBAAyB;gBACzB,OAAQ,OAAO,IAAI,CAAC,IAAI;oBACtB,KAAK;wBACH,OAAO,IAAI,CAAC;wBACZ;oBACF,KAAK;wBACH,OAAO,IAAI,CAAC;wBACZ;oBACF,KAAK;wBACH,OAAO,IAAI,CAAC;wBACZ;oBACF,KAAK;oBACL;wBACE,OAAO,IAAI,CAAC;wBACZ;gBACJ;YACF,OAAO;gBACL,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,KAAK,IAAI;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC9B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;8BAEV,cAAA,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;;0CAEV,6LAAC,mNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;8BAMrC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAGV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,OAAO;oCAAE;oCACpB,SAAS;wCAAE,OAAO;oCAAE;oCACpB,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;8CAEV,cAAA,6LAAC,+MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;;;;;;8CAErB,6LAAC;oCAAG,WAAU;8CACX,WAAW,KAAK;;;;;;8CAEnB,6LAAC;oCAAE,WAAU;8CACV,WAAW,WAAW;;;;;;;;;;;;sCAK3B,6LAAC;4BAAK,UAAU,aAAa;4BAAW,WAAU;;8CAEhD,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAElB,6LAAC;oDACE,GAAG,SAAS,QAAQ;oDACrB,MAAK;oDACL,WAAU;oDACV,aAAY;;;;;;;;;;;;wCAGf,OAAO,KAAK,kBACX,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;8CAKlE,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAElB,6LAAC;oDACE,GAAG,SAAS,WAAW;oDACxB,MAAM,eAAe,SAAS;oDAC9B,WAAU;oDACV,aAAY;;;;;;8DAEd,6LAAC;oDACC,MAAK;oDACL,SAAS,IAAM,gBAAgB,CAAC;oDAChC,WAAU;8DAET,6BACC,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;iHAElB,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;;;;;;;;;;;wCAIpB,OAAO,QAAQ,kBACd,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;8CAKrE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAK;oDACL,WAAU;;;;;;8DAEZ,6LAAC;oDAAM,SAAQ;oDAAc,WAAU;8DAAmC;;;;;;;;;;;;sDAI5E,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;8CAMH,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,MAAK;oCACL,UAAU;oCACV,WAAU;8CAET,0BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;4CAAiF;;;;;;mFAIlG;;;;;;;;;;;;sCAMN,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;;;;;;;;;;kDAEjB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDAA8B;;;;;;;;;;;;;;;;;;;;;;sCAMpD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAO,WAAU;8CAChB,cAAA,6LAAC;kDAAK;;;;;;;;;;;8CAER,6LAAC;oCAAO,WAAU;8CAChB,cAAA,6LAAC;kDAAK;;;;;;;;;;;;;;;;;sCAKV,6LAAC;4BAAE,WAAU;;gCAAyC;gCAC7B;8CACvB,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GA9PM;;QAGW,qIAAA,CAAA,YAAS;QACH,qIAAA,CAAA,kBAAe;QAClB,+HAAA,CAAA,UAAY;QAuC1B,iKAAA,CAAA,UAAO;;;KA5CP;uCAgQS", "debugId": null}}]}