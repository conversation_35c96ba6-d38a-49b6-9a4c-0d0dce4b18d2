'use client';

import React, { useState, useEffect } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import useAuthStore from '@/store/useAuthStore';
import useSocket from '@/hooks/useSocket';

export function Providers({ children }: { children: React.ReactNode }) {
  const [queryClient] = useState(() => new QueryClient({
    defaultOptions: {
      queries: {
        staleTime: 60 * 1000, // 1 minute
        retry: 1,
        refetchOnWindowFocus: false,
      },
    },
  }));

  const initializeAuth = useAuthStore((state) => state.initializeAuth);

  // Initialize auth state on app load
  useEffect(() => {
    // Make auth store globally accessible for token refresh
    if (typeof window !== 'undefined') {
      window.useAuthStore = useAuthStore;
    }
    initializeAuth();
  }, [initializeAuth]);

  return (
    <QueryClientProvider client={queryClient}>
      <SocketProvider>
        {children}
      </SocketProvider>
    </QueryClientProvider>
  );
}

// Socket provider component
function SocketProvider({ children }: { children: React.ReactNode }) {
  // Initialize socket connection
  useSocket();
  
  return <>{children}</>;
}
