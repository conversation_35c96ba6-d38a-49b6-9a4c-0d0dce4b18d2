'use client';

import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { 
  CheckCircle, 
  XCircle, 
  Loader, 
  User, 
  Store, 
  Truck, 
  Shield,
  ShoppingCart,
  Star
} from 'lucide-react';
import { authAPI, restaurantAPI, vendorAPI, adminAPI, deliveryAPI } from '@/lib/api';
import useAuthStore from '@/store/useAuthStore';

const SystemTestPage = () => {
  const { user, isAuthenticated } = useAuthStore();
  const [testResults, setTestResults] = useState<any>({});

  // Test API endpoints
  const tests = [
    {
      name: 'Public Restaurants',
      key: 'restaurants',
      test: () => restaurantAPI.getAll({ page: 1, limit: 5 }),
      icon: Store,
      color: 'blue'
    },
    {
      name: 'Authentication Status',
      key: 'auth',
      test: () => Promise.resolve({ data: { isAuthenticated, user } }),
      icon: User,
      color: 'green'
    }
  ];

  // Add role-specific tests
  if (user?.role === 'vendor') {
    tests.push(
      {
        name: 'Vendor Restaurant',
        key: 'vendor-restaurant',
        test: () => vendorAPI.getRestaurant(),
        icon: Store,
        color: 'orange'
      },
      {
        name: 'Vendor Foods',
        key: 'vendor-foods',
        test: () => vendorAPI.getFoods(),
        icon: ShoppingCart,
        color: 'purple'
      },
      {
        name: 'Vendor Orders',
        key: 'vendor-orders',
        test: () => vendorAPI.getOrders(),
        icon: Star,
        color: 'yellow'
      }
    );
  }

  if (user?.role === 'admin') {
    tests.push(
      {
        name: 'Admin Stats',
        key: 'admin-stats',
        test: () => adminAPI.getStats(),
        icon: Shield,
        color: 'red'
      },
      {
        name: 'Admin Restaurants',
        key: 'admin-restaurants',
        test: () => adminAPI.getRestaurants(),
        icon: Store,
        color: 'indigo'
      }
    );
  }

  if (user?.role === 'delivery') {
    tests.push(
      {
        name: 'Delivery Dashboard',
        key: 'delivery-dashboard',
        test: () => deliveryAPI.getDashboard(),
        icon: Truck,
        color: 'teal'
      }
    );
  }

  const runTest = async (test: any) => {
    setTestResults(prev => ({ ...prev, [test.key]: { status: 'loading' } }));
    
    try {
      const result = await test.test();
      setTestResults(prev => ({ 
        ...prev, 
        [test.key]: { 
          status: 'success', 
          data: result.data,
          message: 'Test passed successfully'
        } 
      }));
    } catch (error: any) {
      setTestResults(prev => ({ 
        ...prev, 
        [test.key]: { 
          status: 'error', 
          error: error.response?.data?.message || error.message,
          message: 'Test failed'
        } 
      }));
    }
  };

  const runAllTests = async () => {
    for (const test of tests) {
      await runTest(test);
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'loading':
        return <Loader className="w-5 h-5 animate-spin text-blue-500" />;
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'error':
        return <XCircle className="w-5 h-5 text-red-500" />;
      default:
        return <div className="w-5 h-5 bg-gray-300 rounded-full" />;
    }
  };

  const getColorClasses = (color: string) => {
    const colors = {
      blue: 'bg-blue-100 text-blue-600',
      green: 'bg-green-100 text-green-600',
      orange: 'bg-orange-100 text-orange-600',
      purple: 'bg-purple-100 text-purple-600',
      yellow: 'bg-yellow-100 text-yellow-600',
      red: 'bg-red-100 text-red-600',
      indigo: 'bg-indigo-100 text-indigo-600',
      teal: 'bg-teal-100 text-teal-600'
    };
    return colors[color] || 'bg-gray-100 text-gray-600';
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-6xl mx-auto">
        <div className="bg-white rounded-2xl shadow-lg p-8">
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">System Test Dashboard</h1>
              <p className="text-gray-600">Test all system components and API endpoints</p>
            </div>
            <button
              onClick={runAllTests}
              className="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
            >
              Run All Tests
            </button>
          </div>

          {/* User Info */}
          <div className="bg-gray-50 rounded-lg p-6 mb-8">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Current User</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <p className="text-sm text-gray-600">Authentication</p>
                <p className="font-medium">{isAuthenticated ? 'Authenticated' : 'Not Authenticated'}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Role</p>
                <p className="font-medium">{user?.role || 'None'}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Name</p>
                <p className="font-medium">{user?.name || 'N/A'}</p>
              </div>
            </div>
          </div>

          {/* Test Results */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {tests.map((test) => {
              const result = testResults[test.key];
              const IconComponent = test.icon;
              
              return (
                <div key={test.key} className="bg-white border rounded-xl p-6 hover:shadow-md transition-shadow">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${getColorClasses(test.color)}`}>
                        <IconComponent className="w-5 h-5" />
                      </div>
                      <h3 className="font-semibold text-gray-900">{test.name}</h3>
                    </div>
                    {getStatusIcon(result?.status)}
                  </div>
                  
                  <div className="space-y-2">
                    <button
                      onClick={() => runTest(test)}
                      className="w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors text-sm"
                    >
                      Run Test
                    </button>
                    
                    {result && (
                      <div className="text-sm">
                        <p className={`font-medium ${
                          result.status === 'success' ? 'text-green-600' : 
                          result.status === 'error' ? 'text-red-600' : 'text-blue-600'
                        }`}>
                          {result.message}
                        </p>
                        {result.error && (
                          <p className="text-red-500 mt-1">{result.error}</p>
                        )}
                        {result.data && (
                          <details className="mt-2">
                            <summary className="cursor-pointer text-gray-600 hover:text-gray-800">
                              View Response
                            </summary>
                            <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-auto max-h-32">
                              {JSON.stringify(result.data, null, 2)}
                            </pre>
                          </details>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>

          {/* Quick Links */}
          <div className="mt-8 pt-8 border-t">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Quick Navigation</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <a href="/" className="p-4 bg-blue-50 rounded-lg text-center hover:bg-blue-100 transition-colors">
                <p className="font-medium text-blue-900">Home</p>
              </a>
              <a href="/restaurants" className="p-4 bg-green-50 rounded-lg text-center hover:bg-green-100 transition-colors">
                <p className="font-medium text-green-900">Restaurants</p>
              </a>
              {user?.role === 'vendor' && (
                <a href="/vendor/dashboard" className="p-4 bg-orange-50 rounded-lg text-center hover:bg-orange-100 transition-colors">
                  <p className="font-medium text-orange-900">Vendor Dashboard</p>
                </a>
              )}
              {user?.role === 'admin' && (
                <a href="/admin/dashboard" className="p-4 bg-red-50 rounded-lg text-center hover:bg-red-100 transition-colors">
                  <p className="font-medium text-red-900">Admin Dashboard</p>
                </a>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SystemTestPage;
