{"version": 3, "sources": [], "sections": [{"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/project%20food-order%206k/food-ordering-system-modern/frontend/src/store/useCartStore.js"], "sourcesContent": ["import { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport { toast } from 'react-hot-toast';\n\nconst useCartStore = create(\n  persist(\n    (set, get) => ({\n      items: [],\n      restaurant: null,\n      isOpen: false,\n\n      // Add item to cart\n      addItem: (food, quantity = 1, variant = null, addons = [], customizations = []) => {\n        const state = get();\n        \n        // Check if item is from different restaurant\n        if (state.restaurant && state.restaurant._id !== food.restaurant._id) {\n          toast.error('You can only order from one restaurant at a time');\n          return false;\n        }\n\n        // Create item object\n        const newItem = {\n          id: `${food._id}-${variant?.name || 'default'}-${Date.now()}`,\n          food,\n          quantity,\n          variant,\n          addons: addons || [],\n          customizations: customizations || [],\n          price: variant?.price || food.price,\n          itemTotal: calculateItemTotal(food, quantity, variant, addons)\n        };\n\n        set({\n          items: [...state.items, newItem],\n          restaurant: food.restaurant,\n        });\n\n        toast.success(`${food.name} added to cart`);\n        return true;\n      },\n\n      // Update item quantity\n      updateQuantity: (itemId, quantity) => {\n        if (quantity <= 0) {\n          get().removeItem(itemId);\n          return;\n        }\n\n        set((state) => ({\n          items: state.items.map(item => \n            item.id === itemId \n              ? { \n                  ...item, \n                  quantity,\n                  itemTotal: calculateItemTotal(item.food, quantity, item.variant, item.addons)\n                }\n              : item\n          )\n        }));\n      },\n\n      // Remove item from cart\n      removeItem: (itemId) => {\n        set((state) => {\n          const newItems = state.items.filter(item => item.id !== itemId);\n          return {\n            items: newItems,\n            restaurant: newItems.length === 0 ? null : state.restaurant\n          };\n        });\n        toast.success('Item removed from cart');\n      },\n\n      // Clear entire cart\n      clearCart: () => {\n        set({ items: [], restaurant: null });\n        toast.success('Cart cleared');\n      },\n\n      // Toggle cart visibility\n      toggleCart: () => {\n        set((state) => ({ isOpen: !state.isOpen }));\n      },\n\n      // Open cart\n      openCart: () => {\n        set({ isOpen: true });\n      },\n\n      // Close cart\n      closeCart: () => {\n        set({ isOpen: false });\n      },\n\n      // Get cart totals\n      getTotals: () => {\n        const state = get();\n        const subtotal = state.items.reduce((sum, item) => sum + item.itemTotal, 0);\n        const deliveryFee = state.restaurant?.pricing?.deliveryFee || 0;\n        const packagingFee = state.restaurant?.pricing?.packagingFee || 0;\n        const taxes = subtotal * 0.18; // 18% GST\n        const total = subtotal + deliveryFee + packagingFee + taxes;\n\n        return {\n          subtotal,\n          deliveryFee,\n          packagingFee,\n          taxes,\n          total,\n          itemCount: state.items.reduce((sum, item) => sum + item.quantity, 0)\n        };\n      },\n\n      // Check if item exists in cart\n      isItemInCart: (foodId, variant = null) => {\n        const state = get();\n        return state.items.some(item => \n          item.food._id === foodId && \n          (variant ? item.variant?.name === variant.name : !item.variant)\n        );\n      },\n\n      // Get item from cart\n      getCartItem: (foodId, variant = null) => {\n        const state = get();\n        return state.items.find(item => \n          item.food._id === foodId && \n          (variant ? item.variant?.name === variant.name : !item.variant)\n        );\n      },\n    }),\n    {\n      name: 'cart-storage',\n      partialize: (state) => ({ \n        items: state.items, \n        restaurant: state.restaurant \n      }),\n    }\n  )\n);\n\n// Helper function to calculate item total\nconst calculateItemTotal = (food, quantity, variant, addons) => {\n  let basePrice = variant?.price || food.price;\n  \n  // Add addon prices\n  const addonTotal = addons?.reduce((sum, addon) => {\n    return sum + (addon.price * (addon.quantity || 1));\n  }, 0) || 0;\n\n  return (basePrice + addonTotal) * quantity;\n};\n\nexport default useCartStore;\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,MAAM,eAAe,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,EACxB,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,OAAO,EAAE;QACT,YAAY;QACZ,QAAQ;QAER,mBAAmB;QACnB,SAAS,CAAC,MAAM,WAAW,CAAC,EAAE,UAAU,IAAI,EAAE,SAAS,EAAE,EAAE,iBAAiB,EAAE;YAC5E,MAAM,QAAQ;YAEd,6CAA6C;YAC7C,IAAI,MAAM,UAAU,IAAI,MAAM,UAAU,CAAC,GAAG,KAAK,KAAK,UAAU,CAAC,GAAG,EAAE;gBACpE,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,OAAO;YACT;YAEA,qBAAqB;YACrB,MAAM,UAAU;gBACd,IAAI,GAAG,KAAK,GAAG,CAAC,CAAC,EAAE,SAAS,QAAQ,UAAU,CAAC,EAAE,KAAK,GAAG,IAAI;gBAC7D;gBACA;gBACA;gBACA,QAAQ,UAAU,EAAE;gBACpB,gBAAgB,kBAAkB,EAAE;gBACpC,OAAO,SAAS,SAAS,KAAK,KAAK;gBACnC,WAAW,mBAAmB,MAAM,UAAU,SAAS;YACzD;YAEA,IAAI;gBACF,OAAO;uBAAI,MAAM,KAAK;oBAAE;iBAAQ;gBAChC,YAAY,KAAK,UAAU;YAC7B;YAEA,uJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,GAAG,KAAK,IAAI,CAAC,cAAc,CAAC;YAC1C,OAAO;QACT;QAEA,uBAAuB;QACvB,gBAAgB,CAAC,QAAQ;YACvB,IAAI,YAAY,GAAG;gBACjB,MAAM,UAAU,CAAC;gBACjB;YACF;YAEA,IAAI,CAAC,QAAU,CAAC;oBACd,OAAO,MAAM,KAAK,CAAC,GAAG,CAAC,CAAA,OACrB,KAAK,EAAE,KAAK,SACR;4BACE,GAAG,IAAI;4BACP;4BACA,WAAW,mBAAmB,KAAK,IAAI,EAAE,UAAU,KAAK,OAAO,EAAE,KAAK,MAAM;wBAC9E,IACA;gBAER,CAAC;QACH;QAEA,wBAAwB;QACxB,YAAY,CAAC;YACX,IAAI,CAAC;gBACH,MAAM,WAAW,MAAM,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;gBACxD,OAAO;oBACL,OAAO;oBACP,YAAY,SAAS,MAAM,KAAK,IAAI,OAAO,MAAM,UAAU;gBAC7D;YACF;YACA,uJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB;QAEA,oBAAoB;QACpB,WAAW;YACT,IAAI;gBAAE,OAAO,EAAE;gBAAE,YAAY;YAAK;YAClC,uJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB;QAEA,yBAAyB;QACzB,YAAY;YACV,IAAI,CAAC,QAAU,CAAC;oBAAE,QAAQ,CAAC,MAAM,MAAM;gBAAC,CAAC;QAC3C;QAEA,YAAY;QACZ,UAAU;YACR,IAAI;gBAAE,QAAQ;YAAK;QACrB;QAEA,aAAa;QACb,WAAW;YACT,IAAI;gBAAE,QAAQ;YAAM;QACtB;QAEA,kBAAkB;QAClB,WAAW;YACT,MAAM,QAAQ;YACd,MAAM,WAAW,MAAM,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,SAAS,EAAE;YACzE,MAAM,cAAc,MAAM,UAAU,EAAE,SAAS,eAAe;YAC9D,MAAM,eAAe,MAAM,UAAU,EAAE,SAAS,gBAAgB;YAChE,MAAM,QAAQ,WAAW,MAAM,UAAU;YACzC,MAAM,QAAQ,WAAW,cAAc,eAAe;YAEtD,OAAO;gBACL;gBACA;gBACA;gBACA;gBACA;gBACA,WAAW,MAAM,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,QAAQ,EAAE;YACpE;QACF;QAEA,+BAA+B;QAC/B,cAAc,CAAC,QAAQ,UAAU,IAAI;YACnC,MAAM,QAAQ;YACd,OAAO,MAAM,KAAK,CAAC,IAAI,CAAC,CAAA,OACtB,KAAK,IAAI,CAAC,GAAG,KAAK,UAClB,CAAC,UAAU,KAAK,OAAO,EAAE,SAAS,QAAQ,IAAI,GAAG,CAAC,KAAK,OAAO;QAElE;QAEA,qBAAqB;QACrB,aAAa,CAAC,QAAQ,UAAU,IAAI;YAClC,MAAM,QAAQ;YACd,OAAO,MAAM,KAAK,CAAC,IAAI,CAAC,CAAA,OACtB,KAAK,IAAI,CAAC,GAAG,KAAK,UAClB,CAAC,UAAU,KAAK,OAAO,EAAE,SAAS,QAAQ,IAAI,GAAG,CAAC,KAAK,OAAO;QAElE;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,OAAO,MAAM,KAAK;YAClB,YAAY,MAAM,UAAU;QAC9B,CAAC;AACH;AAIJ,0CAA0C;AAC1C,MAAM,qBAAqB,CAAC,MAAM,UAAU,SAAS;IACnD,IAAI,YAAY,SAAS,SAAS,KAAK,KAAK;IAE5C,mBAAmB;IACnB,MAAM,aAAa,QAAQ,OAAO,CAAC,KAAK;QACtC,OAAO,MAAO,MAAM,KAAK,GAAG,CAAC,MAAM,QAAQ,IAAI,CAAC;IAClD,GAAG,MAAM;IAET,OAAO,CAAC,YAAY,UAAU,IAAI;AACpC;uCAEe", "debugId": null}}, {"offset": {"line": 170, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/project%20food-order%206k/food-ordering-system-modern/frontend/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  Menu,\n  X,\n  ShoppingCart,\n  User,\n  MapPin,\n  Search,\n  ChefHat,\n  LogOut,\n  Settings,\n  Heart,\n  Clock\n} from 'lucide-react';\nimport Link from 'next/link';\nimport { useRouter, usePathname } from 'next/navigation';\nimport useAuthStore from '@/store/useAuthStore';\nimport useCartStore from '@/store/useCartStore';\n\nconst Header = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);\n  const [isScrolled, setIsScrolled] = useState(false);\n\n  const router = useRouter();\n  const pathname = usePathname();\n  const { user, isAuthenticated, logout } = useAuthStore();\n  const { items, getTotals, toggleCart } = useCartStore();\n  const { itemCount } = getTotals();\n\n  // Hide header for vendor pages\n  if (pathname?.startsWith('/vendor')) {\n    return null;\n  }\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 10);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const handleLogout = async () => {\n    await logout();\n    setIsUserMenuOpen(false);\n    router.push('/');\n  };\n\n  const navItems = [\n    { name: 'Restaurants', href: '/restaurants' },\n    { name: 'Cuisines', href: '/cuisines' },\n    { name: 'Offers', href: '/offers' },\n    { name: 'About', href: '/about' },\n  ];\n\n  return (\n    <motion.header\n      initial={{ y: -100 }}\n      animate={{ y: 0 }}\n      transition={{ duration: 0.6, ease: \"easeOut\" }}\n      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n        isScrolled \n          ? 'bg-white/95 backdrop-blur-md shadow-lg' \n          : 'bg-transparent'\n      }`}\n    >\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16 lg:h-20\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center gap-2\">\n            <motion.div\n              whileHover={{ rotate: 360 }}\n              transition={{ duration: 0.6 }}\n              className=\"w-10 h-10 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center\"\n            >\n              <ChefHat className=\"w-6 h-6 text-white\" />\n            </motion.div>\n            <span className=\"text-2xl font-bold text-gray-900\">\n              FoodieExpress\n            </span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden lg:flex items-center space-x-8\">\n            {navItems.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className=\"text-gray-700 hover:text-orange-500 font-medium transition-colors duration-200 relative group\"\n              >\n                {item.name}\n                <span className=\"absolute -bottom-1 left-0 w-0 h-0.5 bg-orange-500 transition-all duration-300 group-hover:w-full\" />\n              </Link>\n            ))}\n          </nav>\n\n          {/* Right Side Actions */}\n          <div className=\"flex items-center gap-4\">\n            {/* Location */}\n            <motion.button\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              className=\"hidden sm:flex items-center gap-2 text-gray-700 hover:text-orange-500 transition-colors duration-200\"\n            >\n              <MapPin className=\"w-5 h-5\" />\n              <span className=\"text-sm font-medium\">Location</span>\n            </motion.button>\n\n            {/* Search */}\n            <motion.button\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              className=\"p-2 text-gray-700 hover:text-orange-500 transition-colors duration-200\"\n            >\n              <Search className=\"w-5 h-5\" />\n            </motion.button>\n\n            {/* Cart */}\n            <motion.button\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              onClick={toggleCart}\n              className=\"relative p-2 text-gray-700 hover:text-orange-500 transition-colors duration-200\"\n            >\n              <ShoppingCart className=\"w-5 h-5\" />\n              {itemCount > 0 && (\n                <motion.span\n                  initial={{ scale: 0 }}\n                  animate={{ scale: 1 }}\n                  className=\"absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-medium\"\n                >\n                  {itemCount}\n                </motion.span>\n              )}\n            </motion.button>\n\n            {/* User Menu */}\n            {isAuthenticated ? (\n              <div className=\"relative\">\n                <motion.button\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                  onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}\n                  className=\"flex items-center gap-2 p-2 rounded-full hover:bg-gray-100 transition-colors duration-200\"\n                >\n                  <div className=\"w-8 h-8 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center\">\n                    <span className=\"text-white text-sm font-medium\">\n                      {user?.name?.charAt(0).toUpperCase()}\n                    </span>\n                  </div>\n                </motion.button>\n\n                <AnimatePresence>\n                  {isUserMenuOpen && (\n                    <motion.div\n                      initial={{ opacity: 0, y: 10, scale: 0.95 }}\n                      animate={{ opacity: 1, y: 0, scale: 1 }}\n                      exit={{ opacity: 0, y: 10, scale: 0.95 }}\n                      transition={{ duration: 0.2 }}\n                      className=\"absolute right-0 mt-2 w-64 bg-white rounded-xl shadow-lg border border-gray-200 py-2\"\n                    >\n                      <div className=\"px-4 py-3 border-b border-gray-100\">\n                        <p className=\"text-sm font-medium text-gray-900\">\n                          {user?.name}\n                        </p>\n                        <p className=\"text-sm text-gray-500\">\n                          {user?.email}\n                        </p>\n                      </div>\n\n                      <div className=\"py-2\">\n                        <Link\n                          href=\"/profile\"\n                          className=\"flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50\"\n                          onClick={() => setIsUserMenuOpen(false)}\n                        >\n                          <User className=\"w-4 h-4\" />\n                          Profile\n                        </Link>\n                        <Link\n                          href=\"/orders\"\n                          className=\"flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50\"\n                          onClick={() => setIsUserMenuOpen(false)}\n                        >\n                          <Clock className=\"w-4 h-4\" />\n                          Orders\n                        </Link>\n                        <Link\n                          href=\"/favorites\"\n                          className=\"flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50\"\n                          onClick={() => setIsUserMenuOpen(false)}\n                        >\n                          <Heart className=\"w-4 h-4\" />\n                          Favorites\n                        </Link>\n                        <Link\n                          href=\"/settings\"\n                          className=\"flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50\"\n                          onClick={() => setIsUserMenuOpen(false)}\n                        >\n                          <Settings className=\"w-4 h-4\" />\n                          Settings\n                        </Link>\n                      </div>\n\n                      <div className=\"border-t border-gray-100 pt-2\">\n                        <button\n                          onClick={handleLogout}\n                          className=\"flex items-center gap-3 px-4 py-2 text-sm text-red-600 hover:bg-red-50 w-full text-left\"\n                        >\n                          <LogOut className=\"w-4 h-4\" />\n                          Sign Out\n                        </button>\n                      </div>\n                    </motion.div>\n                  )}\n                </AnimatePresence>\n              </div>\n            ) : (\n              <div className=\"flex items-center gap-2\">\n                <Link href=\"/auth/login\">\n                  <motion.button\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    className=\"px-4 py-2 text-gray-700 hover:text-orange-500 font-medium transition-colors duration-200\"\n                  >\n                    Sign In\n                  </motion.button>\n                </Link>\n                <Link href=\"/auth/register\">\n                  <motion.button\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    className=\"px-4 py-2 bg-gradient-to-r from-orange-500 to-red-500 text-white rounded-full font-medium shadow-md hover:shadow-lg transition-all duration-200\"\n                  >\n                    Sign Up\n                  </motion.button>\n                </Link>\n              </div>\n            )}\n\n            {/* Mobile Menu Button */}\n            <motion.button\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"lg:hidden p-2 text-gray-700 hover:text-orange-500 transition-colors duration-200\"\n            >\n              {isMenuOpen ? (\n                <X className=\"w-6 h-6\" />\n              ) : (\n                <Menu className=\"w-6 h-6\" />\n              )}\n            </motion.button>\n          </div>\n        </div>\n\n        {/* Mobile Menu */}\n        <AnimatePresence>\n          {isMenuOpen && (\n            <motion.div\n              initial={{ opacity: 0, height: 0 }}\n              animate={{ opacity: 1, height: 'auto' }}\n              exit={{ opacity: 0, height: 0 }}\n              transition={{ duration: 0.3 }}\n              className=\"lg:hidden border-t border-gray-200 bg-white\"\n            >\n              <div className=\"py-4 space-y-2\">\n                {navItems.map((item) => (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className=\"block px-4 py-2 text-gray-700 hover:text-orange-500 hover:bg-gray-50 transition-colors duration-200\"\n                    onClick={() => setIsMenuOpen(false)}\n                  >\n                    {item.name}\n                  </Link>\n                ))}\n              </div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </div>\n    </motion.header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AACA;AACA;AACA;AApBA;;;;;;;;;AAsBA,MAAM,SAAS;IACb,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,UAAY,AAAD;IACrD,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,UAAY,AAAD;IACpD,MAAM,EAAE,SAAS,EAAE,GAAG;IAEtB,+BAA+B;IAC/B,IAAI,UAAU,WAAW,YAAY;QACnC,OAAO;IACT;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,cAAc,OAAO,OAAO,GAAG;QACjC;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,eAAe;QACnB,MAAM;QACN,kBAAkB;QAClB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,WAAW;QACf;YAAE,MAAM;YAAe,MAAM;QAAe;QAC5C;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAU,MAAM;QAAU;QAClC;YAAE,MAAM;YAAS,MAAM;QAAS;KACjC;IAED,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,SAAS;YAAE,GAAG,CAAC;QAAI;QACnB,SAAS;YAAE,GAAG;QAAE;QAChB,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;QAC7C,WAAW,CAAC,4DAA4D,EACtE,aACI,2CACA,kBACJ;kBAEF,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,YAAY;wCAAE,QAAQ;oCAAI;oCAC1B,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,WAAU;8CAEV,cAAA,8OAAC,4MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;;;;;;8CAErB,8OAAC;oCAAK,WAAU;8CAAmC;;;;;;;;;;;;sCAMrD,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;;wCAET,KAAK,IAAI;sDACV,8OAAC;4CAAK,WAAU;;;;;;;mCALX,KAAK,IAAI;;;;;;;;;;sCAWpB,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAU;;sDAEV,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;4CAAK,WAAU;sDAAsB;;;;;;;;;;;;8CAIxC,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAU;8CAEV,cAAA,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;8CAIpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,SAAS;oCACT,WAAU;;sDAEV,8OAAC,sNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;wCACvB,YAAY,mBACX,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;4CACV,SAAS;gDAAE,OAAO;4CAAE;4CACpB,SAAS;gDAAE,OAAO;4CAAE;4CACpB,WAAU;sDAET;;;;;;;;;;;;gCAMN,gCACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4CACZ,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,UAAU;gDAAE,OAAO;4CAAK;4CACxB,SAAS,IAAM,kBAAkB,CAAC;4CAClC,WAAU;sDAEV,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DACb,MAAM,MAAM,OAAO,GAAG;;;;;;;;;;;;;;;;sDAK7B,8OAAC,yLAAA,CAAA,kBAAe;sDACb,gCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,GAAG;oDAAI,OAAO;gDAAK;gDAC1C,SAAS;oDAAE,SAAS;oDAAG,GAAG;oDAAG,OAAO;gDAAE;gDACtC,MAAM;oDAAE,SAAS;oDAAG,GAAG;oDAAI,OAAO;gDAAK;gDACvC,YAAY;oDAAE,UAAU;gDAAI;gDAC5B,WAAU;;kEAEV,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EACV,MAAM;;;;;;0EAET,8OAAC;gEAAE,WAAU;0EACV,MAAM;;;;;;;;;;;;kEAIX,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,4JAAA,CAAA,UAAI;gEACH,MAAK;gEACL,WAAU;gEACV,SAAS,IAAM,kBAAkB;;kFAEjC,8OAAC,kMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAY;;;;;;;0EAG9B,8OAAC,4JAAA,CAAA,UAAI;gEACH,MAAK;gEACL,WAAU;gEACV,SAAS,IAAM,kBAAkB;;kFAEjC,8OAAC,oMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;oEAAY;;;;;;;0EAG/B,8OAAC,4JAAA,CAAA,UAAI;gEACH,MAAK;gEACL,WAAU;gEACV,SAAS,IAAM,kBAAkB;;kFAEjC,8OAAC,oMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;oEAAY;;;;;;;0EAG/B,8OAAC,4JAAA,CAAA,UAAI;gEACH,MAAK;gEACL,WAAU;gEACV,SAAS,IAAM,kBAAkB;;kFAEjC,8OAAC,0MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;oEAAY;;;;;;;;;;;;;kEAKpC,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DACC,SAAS;4DACT,WAAU;;8EAEV,8OAAC,0MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;6FAS1C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gDACZ,YAAY;oDAAE,OAAO;gDAAK;gDAC1B,UAAU;oDAAE,OAAO;gDAAK;gDACxB,WAAU;0DACX;;;;;;;;;;;sDAIH,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gDACZ,YAAY;oDAAE,OAAO;gDAAK;gDAC1B,UAAU;oDAAE,OAAO;gDAAK;gDACxB,WAAU;0DACX;;;;;;;;;;;;;;;;;8CAQP,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,SAAS,IAAM,cAAc,CAAC;oCAC9B,WAAU;8CAET,2BACC,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;iGAEb,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8BAOxB,8OAAC,yLAAA,CAAA,kBAAe;8BACb,4BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,QAAQ;wBAAE;wBACjC,SAAS;4BAAE,SAAS;4BAAG,QAAQ;wBAAO;wBACtC,MAAM;4BAAE,SAAS;4BAAG,QAAQ;wBAAE;wBAC9B,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,SAAS,IAAM,cAAc;8CAE5B,KAAK,IAAI;mCALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAelC;uCAEe", "debugId": null}}, {"offset": {"line": 772, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/project%20food-order%206k/food-ordering-system-modern/frontend/src/components/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { \n  ChefHat, \n  Mail, \n  Phone, \n  MapPin, \n  Facebook, \n  Twitter, \n  Instagram, \n  Youtube,\n  ArrowRight\n} from 'lucide-react';\nimport Link from 'next/link';\n\nconst Footer = () => {\n  const footerLinks = {\n    company: [\n      { name: 'About Us', href: '/about' },\n      { name: 'Careers', href: '/careers' },\n      { name: 'Press', href: '/press' },\n      { name: 'Blog', href: '/blog' },\n    ],\n    support: [\n      { name: 'Help Center', href: '/help' },\n      { name: 'Contact Us', href: '/contact' },\n      { name: 'Safety', href: '/safety' },\n      { name: 'Terms of Service', href: '/terms' },\n    ],\n    partner: [\n      { name: 'Become a Partner', href: '/partner' },\n      { name: 'Restaurant Dashboard', href: '/restaurant-dashboard' },\n      { name: 'Delivery Partner', href: '/delivery-partner' },\n      { name: 'API Documentation', href: '/api-docs' },\n    ],\n    admin: [\n      { name: 'Admin Login', href: '/auth/login?role=admin' },\n      { name: 'Delivery Partner Login', href: '/auth/login?role=delivery' },\n    ],\n  };\n\n  const socialLinks = [\n    { icon: Facebook, href: '#', label: 'Facebook' },\n    { icon: Twitter, href: '#', label: 'Twitter' },\n    { icon: Instagram, href: '#', label: 'Instagram' },\n    { icon: Youtube, href: '#', label: 'YouTube' },\n  ];\n\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      {/* Newsletter Section */}\n      <div className=\"border-b border-gray-800\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"text-center\"\n          >\n            <h3 className=\"text-2xl font-bold mb-4\">\n              Stay Updated with FoodieExpress\n            </h3>\n            <p className=\"text-gray-400 mb-8 max-w-2xl mx-auto\">\n              Get the latest updates on new restaurants, exclusive offers, and delicious deals delivered straight to your inbox.\n            </p>\n            \n            <div className=\"flex flex-col sm:flex-row gap-4 max-w-md mx-auto\">\n              <input\n                type=\"email\"\n                placeholder=\"Enter your email\"\n                className=\"flex-1 px-4 py-3 rounded-lg bg-gray-800 border border-gray-700 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent\"\n              />\n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"px-6 py-3 bg-gradient-to-r from-orange-500 to-red-500 text-white rounded-lg font-medium hover:shadow-lg transition-all duration-200 flex items-center gap-2 justify-center\"\n              >\n                Subscribe\n                <ArrowRight className=\"w-4 h-4\" />\n              </motion.button>\n            </div>\n          </motion.div>\n        </div>\n      </div>\n\n      {/* Main Footer Content */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8\">\n          {/* Brand Section */}\n          <div className=\"lg:col-span-2\">\n            <Link href=\"/\" className=\"flex items-center gap-2 mb-6\">\n              <div className=\"w-10 h-10 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center\">\n                <ChefHat className=\"w-6 h-6 text-white\" />\n              </div>\n              <span className=\"text-2xl font-bold\">FoodieExpress</span>\n            </Link>\n            \n            <p className=\"text-gray-400 mb-6 max-w-md\">\n              Delivering happiness one meal at a time. Order from your favorite restaurants and enjoy fast, reliable delivery service.\n            </p>\n\n            {/* Contact Info */}\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center gap-3 text-gray-400\">\n                <Phone className=\"w-5 h-5\" />\n                <span>+91 98765 43210</span>\n              </div>\n              <div className=\"flex items-center gap-3 text-gray-400\">\n                <Mail className=\"w-5 h-5\" />\n                <span><EMAIL></span>\n              </div>\n              <div className=\"flex items-center gap-3 text-gray-400\">\n                <MapPin className=\"w-5 h-5\" />\n                <span>Mumbai, Maharashtra, India</span>\n              </div>\n            </div>\n          </div>\n\n          {/* Company Links */}\n          <div>\n            <h4 className=\"text-lg font-semibold mb-6\">Company</h4>\n            <ul className=\"space-y-3\">\n              {footerLinks.company.map((link) => (\n                <li key={link.name}>\n                  <Link\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-white transition-colors duration-200\"\n                  >\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Support Links */}\n          <div>\n            <h4 className=\"text-lg font-semibold mb-6\">Support</h4>\n            <ul className=\"space-y-3\">\n              {footerLinks.support.map((link) => (\n                <li key={link.name}>\n                  <Link\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-white transition-colors duration-200\"\n                  >\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Partner Links */}\n          <div>\n            <h4 className=\"text-lg font-semibold mb-6\">Partner With Us</h4>\n            <ul className=\"space-y-3\">\n              {footerLinks.partner.map((link) => (\n                <li key={link.name}>\n                  <Link\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-white transition-colors duration-200\"\n                  >\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Admin Links */}\n          <div>\n            <h4 className=\"text-lg font-semibold mb-6\">Admin Access</h4>\n            <ul className=\"space-y-3\">\n              {footerLinks.admin.map((link) => (\n                <li key={link.name}>\n                  <Link\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-orange-400 transition-colors duration-200\"\n                  >\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n\n          </div>\n        </div>\n      </div>\n\n      {/* Bottom Section */}\n      <div className=\"border-t border-gray-800\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n          <div className=\"flex flex-col md:flex-row items-center justify-between gap-4\">\n            {/* Copyright */}\n            <p className=\"text-gray-400 text-sm\">\n              © 2024 FoodieExpress. All rights reserved.\n            </p>\n\n            {/* Social Links */}\n            <div className=\"flex items-center gap-4\">\n              {socialLinks.map((social) => (\n                <motion.a\n                  key={social.label}\n                  href={social.href}\n                  whileHover={{ scale: 1.1 }}\n                  whileTap={{ scale: 0.9 }}\n                  className=\"w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center text-gray-400 hover:text-white hover:bg-orange-500 transition-all duration-200\"\n                  aria-label={social.label}\n                >\n                  <social.icon className=\"w-5 h-5\" />\n                </motion.a>\n              ))}\n            </div>\n\n            {/* Legal Links */}\n            <div className=\"flex items-center gap-6 text-sm\">\n              <Link\n                href=\"/privacy\"\n                className=\"text-gray-400 hover:text-white transition-colors duration-200\"\n              >\n                Privacy Policy\n              </Link>\n              <Link\n                href=\"/cookies\"\n                className=\"text-gray-400 hover:text-white transition-colors duration-200\"\n              >\n                Cookie Policy\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AAdA;;;;;AAgBA,MAAM,SAAS;IACb,MAAM,cAAc;QAClB,SAAS;YACP;gBAAE,MAAM;gBAAY,MAAM;YAAS;YACnC;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAS,MAAM;YAAS;YAChC;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;SAC/B;QACD,SAAS;YACP;gBAAE,MAAM;gBAAe,MAAM;YAAQ;YACrC;gBAAE,MAAM;gBAAc,MAAM;YAAW;YACvC;gBAAE,MAAM;gBAAU,MAAM;YAAU;YAClC;gBAAE,MAAM;gBAAoB,MAAM;YAAS;SAC5C;QACD,SAAS;YACP;gBAAE,MAAM;gBAAoB,MAAM;YAAW;YAC7C;gBAAE,MAAM;gBAAwB,MAAM;YAAwB;YAC9D;gBAAE,MAAM;gBAAoB,MAAM;YAAoB;YACtD;gBAAE,MAAM;gBAAqB,MAAM;YAAY;SAChD;QACD,OAAO;YACL;gBAAE,MAAM;gBAAe,MAAM;YAAyB;YACtD;gBAAE,MAAM;gBAA0B,MAAM;YAA4B;SACrE;IACH;IAEA,MAAM,cAAc;QAClB;YAAE,MAAM,0MAAA,CAAA,WAAQ;YAAE,MAAM;YAAK,OAAO;QAAW;QAC/C;YAAE,MAAM,wMAAA,CAAA,UAAO;YAAE,MAAM;YAAK,OAAO;QAAU;QAC7C;YAAE,MAAM,4MAAA,CAAA,YAAS;YAAE,MAAM;YAAK,OAAO;QAAY;QACjD;YAAE,MAAM,wMAAA,CAAA,UAAO;YAAE,MAAM;YAAK,OAAO;QAAU;KAC9C;IAED,qBACE,8OAAC;QAAO,WAAU;;0BAEhB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;0CAA0B;;;;;;0CAGxC,8OAAC;gCAAE,WAAU;0CAAuC;;;;;;0CAIpD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;kDAEZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,WAAU;;4CACX;0DAEC,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;;sDACvB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,4MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;sDAErB,8OAAC;4CAAK,WAAU;sDAAqB;;;;;;;;;;;;8CAGvC,8OAAC;oCAAE,WAAU;8CAA8B;;;;;;8CAK3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;sCAMZ,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;8CACX,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,qBACtB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAgB5B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;0CAKrC,8OAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAC,uBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wCAEP,MAAM,OAAO,IAAI;wCACjB,YAAY;4CAAE,OAAO;wCAAI;wCACzB,UAAU;4CAAE,OAAO;wCAAI;wCACvB,WAAU;wCACV,cAAY,OAAO,KAAK;kDAExB,cAAA,8OAAC,OAAO,IAAI;4CAAC,WAAU;;;;;;uCAPlB,OAAO,KAAK;;;;;;;;;;0CAavB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;uCAEe", "debugId": null}}, {"offset": {"line": 1372, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/project%20food-order%206k/food-ordering-system-modern/frontend/src/lib/utils.js"], "sourcesContent": ["import { clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs) {\n  return twMerge(clsx(inputs));\n}\n\n// Format currency\nexport const formatCurrency = (amount) => {\n  return new Intl.NumberFormat('en-IN', {\n    style: 'currency',\n    currency: 'INR',\n    minimumFractionDigits: 0,\n    maximumFractionDigits: 2,\n  }).format(amount);\n};\n\n// Format date\nexport const formatDate = (date) => {\n  return new Intl.DateTimeFormat('en-IN', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  }).format(new Date(date));\n};\n\n// Format time\nexport const formatTime = (date) => {\n  return new Intl.DateTimeFormat('en-IN', {\n    hour: '2-digit',\n    minute: '2-digit',\n    hour12: true,\n  }).format(new Date(date));\n};\n\n// Format relative time\nexport const formatRelativeTime = (date) => {\n  const now = new Date();\n  const targetDate = new Date(date);\n  const diffInSeconds = Math.floor((now - targetDate) / 1000);\n\n  if (diffInSeconds < 60) {\n    return 'Just now';\n  } else if (diffInSeconds < 3600) {\n    const minutes = Math.floor(diffInSeconds / 60);\n    return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;\n  } else if (diffInSeconds < 86400) {\n    const hours = Math.floor(diffInSeconds / 3600);\n    return `${hours} hour${hours > 1 ? 's' : ''} ago`;\n  } else {\n    const days = Math.floor(diffInSeconds / 86400);\n    return `${days} day${days > 1 ? 's' : ''} ago`;\n  }\n};\n\n// Calculate distance between two coordinates\nexport const calculateDistance = (lat1, lon1, lat2, lon2) => {\n  const R = 6371; // Radius of the Earth in kilometers\n  const dLat = (lat2 - lat1) * Math.PI / 180;\n  const dLon = (lon2 - lon1) * Math.PI / 180;\n  const a = \n    Math.sin(dLat/2) * Math.sin(dLat/2) +\n    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * \n    Math.sin(dLon/2) * Math.sin(dLon/2);\n  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));\n  const distance = R * c;\n  return Math.round(distance * 10) / 10; // Round to 1 decimal place\n};\n\n// Generate order status color\nexport const getOrderStatusColor = (status) => {\n  const colors = {\n    placed: 'bg-blue-100 text-blue-800',\n    confirmed: 'bg-green-100 text-green-800',\n    preparing: 'bg-yellow-100 text-yellow-800',\n    ready: 'bg-purple-100 text-purple-800',\n    'picked-up': 'bg-indigo-100 text-indigo-800',\n    'out-for-delivery': 'bg-orange-100 text-orange-800',\n    delivered: 'bg-green-100 text-green-800',\n    cancelled: 'bg-red-100 text-red-800',\n    refunded: 'bg-gray-100 text-gray-800',\n  };\n  return colors[status] || 'bg-gray-100 text-gray-800';\n};\n\n// Generate random ID\nexport const generateId = () => {\n  return Math.random().toString(36).substr(2, 9);\n};\n\n// Debounce function\nexport const debounce = (func, wait) => {\n  let timeout;\n  return function executedFunction(...args) {\n    const later = () => {\n      clearTimeout(timeout);\n      func(...args);\n    };\n    clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n  };\n};\n\n// Throttle function\nexport const throttle = (func, limit) => {\n  let inThrottle;\n  return function() {\n    const args = arguments;\n    const context = this;\n    if (!inThrottle) {\n      func.apply(context, args);\n      inThrottle = true;\n      setTimeout(() => inThrottle = false, limit);\n    }\n  };\n};\n\n// Validate email\nexport const isValidEmail = (email) => {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n};\n\n// Validate phone number (Indian format)\nexport const isValidPhone = (phone) => {\n  const phoneRegex = /^[6-9]\\d{9}$/;\n  return phoneRegex.test(phone);\n};\n\n// Get user location\nexport const getUserLocation = () => {\n  return new Promise((resolve, reject) => {\n    if (!navigator.geolocation) {\n      reject(new Error('Geolocation is not supported by this browser.'));\n      return;\n    }\n\n    navigator.geolocation.getCurrentPosition(\n      (position) => {\n        resolve({\n          latitude: position.coords.latitude,\n          longitude: position.coords.longitude,\n        });\n      },\n      (error) => {\n        reject(error);\n      },\n      {\n        enableHighAccuracy: true,\n        timeout: 10000,\n        maximumAge: 600000, // 10 minutes\n      }\n    );\n  });\n};\n\n// Local storage helpers\nexport const storage = {\n  get: (key) => {\n    try {\n      const item = localStorage.getItem(key);\n      return item ? JSON.parse(item) : null;\n    } catch (error) {\n      console.error('Error getting from localStorage:', error);\n      return null;\n    }\n  },\n  set: (key, value) => {\n    try {\n      localStorage.setItem(key, JSON.stringify(value));\n    } catch (error) {\n      console.error('Error setting to localStorage:', error);\n    }\n  },\n  remove: (key) => {\n    try {\n      localStorage.removeItem(key);\n    } catch (error) {\n      console.error('Error removing from localStorage:', error);\n    }\n  },\n};\n\n// Image optimization\nexport const getOptimizedImageUrl = (url, width = 400, height = 300) => {\n  if (!url) return '/images/placeholder.jpg';\n  \n  // If it's already an optimized URL, return as is\n  if (url.includes('w_') || url.includes('h_')) return url;\n  \n  // For Cloudinary URLs, add optimization parameters\n  if (url.includes('cloudinary.com')) {\n    return url.replace('/upload/', `/upload/w_${width},h_${height},c_fill,f_auto,q_auto/`);\n  }\n  \n  return url;\n};\n\n// Truncate text\nexport const truncateText = (text, maxLength = 100) => {\n  if (!text) return '';\n  if (text.length <= maxLength) return text;\n  return text.substr(0, maxLength) + '...';\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAM;IAC1B,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,MAAM,iBAAiB,CAAC;IAC7B,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;QACV,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAGO,MAAM,aAAa,CAAC;IACzB,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAGO,MAAM,aAAa,CAAC;IACzB,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,QAAQ;QACR,QAAQ;IACV,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAGO,MAAM,qBAAqB,CAAC;IACjC,MAAM,MAAM,IAAI;IAChB,MAAM,aAAa,IAAI,KAAK;IAC5B,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,MAAM,UAAU,IAAI;IAEtD,IAAI,gBAAgB,IAAI;QACtB,OAAO;IACT,OAAO,IAAI,gBAAgB,MAAM;QAC/B,MAAM,UAAU,KAAK,KAAK,CAAC,gBAAgB;QAC3C,OAAO,GAAG,QAAQ,OAAO,EAAE,UAAU,IAAI,MAAM,GAAG,IAAI,CAAC;IACzD,OAAO,IAAI,gBAAgB,OAAO;QAChC,MAAM,QAAQ,KAAK,KAAK,CAAC,gBAAgB;QACzC,OAAO,GAAG,MAAM,KAAK,EAAE,QAAQ,IAAI,MAAM,GAAG,IAAI,CAAC;IACnD,OAAO;QACL,MAAM,OAAO,KAAK,KAAK,CAAC,gBAAgB;QACxC,OAAO,GAAG,KAAK,IAAI,EAAE,OAAO,IAAI,MAAM,GAAG,IAAI,CAAC;IAChD;AACF;AAGO,MAAM,oBAAoB,CAAC,MAAM,MAAM,MAAM;IAClD,MAAM,IAAI,MAAM,oCAAoC;IACpD,MAAM,OAAO,CAAC,OAAO,IAAI,IAAI,KAAK,EAAE,GAAG;IACvC,MAAM,OAAO,CAAC,OAAO,IAAI,IAAI,KAAK,EAAE,GAAG;IACvC,MAAM,IACJ,KAAK,GAAG,CAAC,OAAK,KAAK,KAAK,GAAG,CAAC,OAAK,KACjC,KAAK,GAAG,CAAC,OAAO,KAAK,EAAE,GAAG,OAAO,KAAK,GAAG,CAAC,OAAO,KAAK,EAAE,GAAG,OAC3D,KAAK,GAAG,CAAC,OAAK,KAAK,KAAK,GAAG,CAAC,OAAK;IACnC,MAAM,IAAI,IAAI,KAAK,KAAK,CAAC,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAE;IACnD,MAAM,WAAW,IAAI;IACrB,OAAO,KAAK,KAAK,CAAC,WAAW,MAAM,IAAI,2BAA2B;AACpE;AAGO,MAAM,sBAAsB,CAAC;IAClC,MAAM,SAAS;QACb,QAAQ;QACR,WAAW;QACX,WAAW;QACX,OAAO;QACP,aAAa;QACb,oBAAoB;QACpB,WAAW;QACX,WAAW;QACX,UAAU;IACZ;IACA,OAAO,MAAM,CAAC,OAAO,IAAI;AAC3B;AAGO,MAAM,aAAa;IACxB,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAGO,MAAM,WAAW,CAAC,MAAM;IAC7B,IAAI;IACJ,OAAO,SAAS,iBAAiB,GAAG,IAAI;QACtC,MAAM,QAAQ;YACZ,aAAa;YACb,QAAQ;QACV;QACA,aAAa;QACb,UAAU,WAAW,OAAO;IAC9B;AACF;AAGO,MAAM,WAAW,CAAC,MAAM;IAC7B,IAAI;IACJ,OAAO;QACL,MAAM,OAAO;QACb,MAAM,UAAU,IAAI;QACpB,IAAI,CAAC,YAAY;YACf,KAAK,KAAK,CAAC,SAAS;YACpB,aAAa;YACb,WAAW,IAAM,aAAa,OAAO;QACvC;IACF;AACF;AAGO,MAAM,eAAe,CAAC;IAC3B,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAGO,MAAM,eAAe,CAAC;IAC3B,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAGO,MAAM,kBAAkB;IAC7B,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,IAAI,CAAC,UAAU,WAAW,EAAE;YAC1B,OAAO,IAAI,MAAM;YACjB;QACF;QAEA,UAAU,WAAW,CAAC,kBAAkB,CACtC,CAAC;YACC,QAAQ;gBACN,UAAU,SAAS,MAAM,CAAC,QAAQ;gBAClC,WAAW,SAAS,MAAM,CAAC,SAAS;YACtC;QACF,GACA,CAAC;YACC,OAAO;QACT,GACA;YACE,oBAAoB;YACpB,SAAS;YACT,YAAY;QACd;IAEJ;AACF;AAGO,MAAM,UAAU;IACrB,KAAK,CAAC;QACJ,IAAI;YACF,MAAM,OAAO,aAAa,OAAO,CAAC;YAClC,OAAO,OAAO,KAAK,KAAK,CAAC,QAAQ;QACnC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO;QACT;IACF;IACA,KAAK,CAAC,KAAK;QACT,IAAI;YACF,aAAa,OAAO,CAAC,KAAK,KAAK,SAAS,CAAC;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD;IACF;IACA,QAAQ,CAAC;QACP,IAAI;YACF,aAAa,UAAU,CAAC;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;QACrD;IACF;AACF;AAGO,MAAM,uBAAuB,CAAC,KAAK,QAAQ,GAAG,EAAE,SAAS,GAAG;IACjE,IAAI,CAAC,KAAK,OAAO;IAEjB,iDAAiD;IACjD,IAAI,IAAI,QAAQ,CAAC,SAAS,IAAI,QAAQ,CAAC,OAAO,OAAO;IAErD,mDAAmD;IACnD,IAAI,IAAI,QAAQ,CAAC,mBAAmB;QAClC,OAAO,IAAI,OAAO,CAAC,YAAY,CAAC,UAAU,EAAE,MAAM,GAAG,EAAE,OAAO,sBAAsB,CAAC;IACvF;IAEA,OAAO;AACT;AAGO,MAAM,eAAe,CAAC,MAAM,YAAY,GAAG;IAChD,IAAI,CAAC,MAAM,OAAO;IAClB,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,MAAM,CAAC,GAAG,aAAa;AACrC", "debugId": null}}, {"offset": {"line": 1557, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/project%20food-order%206k/food-ordering-system-modern/frontend/src/components/FoodCard.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { Star, Plus, Minus, Clock, Leaf, Flame } from 'lucide-react';\nimport Image from 'next/image';\nimport { formatCurrency, getOptimizedImageUrl } from '@/lib/utils';\nimport useCartStore from '@/store/useCartStore';\nimport { toast } from 'react-hot-toast';\n\ninterface FoodCardProps {\n  food: any;\n  restaurant: any;\n}\n\nconst FoodCard = ({ food, restaurant }: FoodCardProps) => {\n  const [selectedVariant, setSelectedVariant] = useState(food.variants?.[0] || null);\n  const [selectedAddons, setSelectedAddons] = useState<any[]>([]);\n  const [quantity, setQuantity] = useState(1);\n  const [showCustomization, setShowCustomization] = useState(false);\n\n  const { addItem, getCartItem } = useCartStore();\n\n  const currentPrice = selectedVariant?.price || food.price;\n  const addonTotal = selectedAddons.reduce((sum, addon) => sum + addon.price, 0);\n  const totalPrice = (currentPrice + addonTotal) * quantity;\n\n  const cartItem = getCartItem(food._id, selectedVariant);\n  const isInCart = !!cartItem;\n\n  const handleAddToCart = () => {\n    const success = addItem(\n      { ...food, restaurant },\n      quantity,\n      selectedVariant,\n      selectedAddons,\n      []\n    );\n\n    if (success) {\n      setQuantity(1);\n      setSelectedAddons([]);\n      setShowCustomization(false);\n    }\n  };\n\n  const handleAddonToggle = (addon: any) => {\n    setSelectedAddons(prev => {\n      const exists = prev.find(a => a.name === addon.name);\n      if (exists) {\n        return prev.filter(a => a.name !== addon.name);\n      } else {\n        return [...prev, addon];\n      }\n    });\n  };\n\n  const getSpiceLevelColor = (level: string) => {\n    switch (level) {\n      case 'mild': return 'text-green-600';\n      case 'medium': return 'text-yellow-600';\n      case 'hot': return 'text-red-600';\n      default: return 'text-gray-600';\n    }\n  };\n\n  const getSpiceLevelIcon = (level: string) => {\n    const count = level === 'mild' ? 1 : level === 'medium' ? 2 : 3;\n    return Array.from({ length: count }, (_, i) => (\n      <Flame key={i} className={`w-3 h-3 ${getSpiceLevelColor(level)} fill-current`} />\n    ));\n  };\n\n  return (\n    <motion.div\n      layout\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      exit={{ opacity: 0, y: -20 }}\n      transition={{ duration: 0.3 }}\n      className=\"bg-white rounded-2xl shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden\"\n    >\n      {/* Food Image */}\n      <div className=\"relative h-48\">\n        <Image\n          src={getOptimizedImageUrl(food.images?.[0] || '/images/food-placeholder.jpg')}\n          alt={food.name}\n          fill\n          className=\"object-cover\"\n        />\n        \n        {/* Badges */}\n        <div className=\"absolute top-3 left-3 flex flex-col gap-2\">\n          {food.dietaryInfo?.isVegetarian && (\n            <div className=\"bg-green-100 text-green-800 px-2 py-1 rounded-full flex items-center gap-1 text-xs font-medium\">\n              <Leaf className=\"w-3 h-3\" />\n              Veg\n            </div>\n          )}\n          {food.dietaryInfo?.isVegan && (\n            <div className=\"bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium\">\n              Vegan\n            </div>\n          )}\n        </div>\n\n        {/* Rating */}\n        <div className=\"absolute top-3 right-3 bg-white/90 backdrop-blur-sm px-2 py-1 rounded-full flex items-center gap-1\">\n          <Star className=\"w-3 h-3 text-yellow-400 fill-current\" />\n          <span className=\"text-xs font-medium text-gray-700\">\n            {food.ratings?.average?.toFixed(1) || '0.0'}\n          </span>\n        </div>\n\n        {/* Preparation Time */}\n        <div className=\"absolute bottom-3 left-3 bg-white/90 backdrop-blur-sm px-2 py-1 rounded-full flex items-center gap-1\">\n          <Clock className=\"w-3 h-3 text-gray-600\" />\n          <span className=\"text-xs font-medium text-gray-700\">\n            {food.preparationTime} mins\n          </span>\n        </div>\n      </div>\n\n      {/* Food Info */}\n      <div className=\"p-4\">\n        <div className=\"flex items-start justify-between mb-2\">\n          <h3 className=\"text-lg font-bold text-gray-900 line-clamp-1\">\n            {food.name}\n          </h3>\n          {food.spiceLevel && (\n            <div className=\"flex items-center gap-1 ml-2\">\n              {getSpiceLevelIcon(food.spiceLevel)}\n            </div>\n          )}\n        </div>\n\n        <p className=\"text-gray-600 text-sm mb-3 line-clamp-2\">\n          {food.description}\n        </p>\n\n        {/* Price */}\n        <div className=\"flex items-center justify-between mb-4\">\n          <div className=\"flex items-center gap-2\">\n            <span className=\"text-xl font-bold text-gray-900\">\n              {formatCurrency(currentPrice)}\n            </span>\n            {food.originalPrice && food.originalPrice > currentPrice && (\n              <span className=\"text-sm text-gray-500 line-through\">\n                {formatCurrency(food.originalPrice)}\n              </span>\n            )}\n          </div>\n          <span className=\"text-sm text-gray-500\">\n            {food.orderCount || 0} orders\n          </span>\n        </div>\n\n        {/* Variants */}\n        {food.variants && food.variants.length > 0 && (\n          <div className=\"mb-4\">\n            <p className=\"text-sm font-medium text-gray-700 mb-2\">Size:</p>\n            <div className=\"flex gap-2\">\n              {food.variants.map((variant: any, index: number) => (\n                <button\n                  key={index}\n                  onClick={() => setSelectedVariant(variant)}\n                  className={`px-3 py-1 text-sm rounded-full border transition-colors duration-200 ${\n                    selectedVariant?.name === variant.name\n                      ? 'border-orange-500 bg-orange-50 text-orange-700'\n                      : 'border-gray-300 text-gray-700 hover:border-orange-300'\n                  }`}\n                >\n                  {variant.name} - {formatCurrency(variant.price)}\n                </button>\n              ))}\n            </div>\n          </div>\n        )}\n\n        {/* Addons */}\n        {food.addons && food.addons.length > 0 && (\n          <div className=\"mb-4\">\n            <button\n              onClick={() => setShowCustomization(!showCustomization)}\n              className=\"text-sm font-medium text-orange-600 hover:text-orange-700 transition-colors duration-200\"\n            >\n              {showCustomization ? 'Hide' : 'Show'} Add-ons ({food.addons.length})\n            </button>\n            \n            {showCustomization && (\n              <motion.div\n                initial={{ opacity: 0, height: 0 }}\n                animate={{ opacity: 1, height: 'auto' }}\n                exit={{ opacity: 0, height: 0 }}\n                className=\"mt-2 space-y-2\"\n              >\n                {food.addons.map((addon: any, index: number) => (\n                  <label\n                    key={index}\n                    className=\"flex items-center justify-between p-2 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50\"\n                  >\n                    <div className=\"flex items-center gap-3\">\n                      <input\n                        type=\"checkbox\"\n                        checked={selectedAddons.some(a => a.name === addon.name)}\n                        onChange={() => handleAddonToggle(addon)}\n                        className=\"h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded\"\n                      />\n                      <span className=\"text-sm text-gray-700\">{addon.name}</span>\n                    </div>\n                    <span className=\"text-sm font-medium text-gray-900\">\n                      +{formatCurrency(addon.price)}\n                    </span>\n                  </label>\n                ))}\n              </motion.div>\n            )}\n          </div>\n        )}\n\n        {/* Quantity and Add to Cart */}\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center gap-3\">\n            <span className=\"text-sm font-medium text-gray-700\">Qty:</span>\n            <div className=\"flex items-center gap-2\">\n              <motion.button\n                whileHover={{ scale: 1.1 }}\n                whileTap={{ scale: 0.9 }}\n                onClick={() => setQuantity(Math.max(1, quantity - 1))}\n                className=\"w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center hover:bg-gray-200 transition-colors duration-200\"\n              >\n                <Minus className=\"w-4 h-4 text-gray-600\" />\n              </motion.button>\n              <span className=\"w-8 text-center font-medium\">{quantity}</span>\n              <motion.button\n                whileHover={{ scale: 1.1 }}\n                whileTap={{ scale: 0.9 }}\n                onClick={() => setQuantity(quantity + 1)}\n                className=\"w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center hover:bg-gray-200 transition-colors duration-200\"\n              >\n                <Plus className=\"w-4 h-4 text-gray-600\" />\n              </motion.button>\n            </div>\n          </div>\n\n          <motion.button\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n            onClick={handleAddToCart}\n            className=\"bg-orange-500 text-white px-4 py-2 rounded-lg font-medium hover:bg-orange-600 transition-colors duration-200 flex items-center gap-2\"\n          >\n            <Plus className=\"w-4 h-4\" />\n            {formatCurrency(totalPrice)}\n          </motion.button>\n        </div>\n\n        {/* Nutritional Info */}\n        {food.nutrition && (\n          <div className=\"mt-4 pt-4 border-t border-gray-100\">\n            <div className=\"grid grid-cols-3 gap-4 text-xs text-gray-600\">\n              <div className=\"text-center\">\n                <div className=\"font-medium text-gray-900\">{food.nutrition.calories}</div>\n                <div>Calories</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"font-medium text-gray-900\">{food.nutrition.protein}g</div>\n                <div>Protein</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"font-medium text-gray-900\">{food.nutrition.carbs}g</div>\n                <div>Carbs</div>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </motion.div>\n  );\n};\n\nexport default FoodCard;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAPA;;;;;;;;AAeA,MAAM,WAAW,CAAC,EAAE,IAAI,EAAE,UAAU,EAAiB;IACnD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,QAAQ,EAAE,CAAC,EAAE,IAAI;IAC7E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAC9D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,UAAY,AAAD;IAE5C,MAAM,eAAe,iBAAiB,SAAS,KAAK,KAAK;IACzD,MAAM,aAAa,eAAe,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,KAAK,EAAE;IAC5E,MAAM,aAAa,CAAC,eAAe,UAAU,IAAI;IAEjD,MAAM,WAAW,YAAY,KAAK,GAAG,EAAE;IACvC,MAAM,WAAW,CAAC,CAAC;IAEnB,MAAM,kBAAkB;QACtB,MAAM,UAAU,QACd;YAAE,GAAG,IAAI;YAAE;QAAW,GACtB,UACA,iBACA,gBACA,EAAE;QAGJ,IAAI,SAAS;YACX,YAAY;YACZ,kBAAkB,EAAE;YACpB,qBAAqB;QACvB;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,kBAAkB,CAAA;YAChB,MAAM,SAAS,KAAK,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,MAAM,IAAI;YACnD,IAAI,QAAQ;gBACV,OAAO,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,MAAM,IAAI;YAC/C,OAAO;gBACL,OAAO;uBAAI;oBAAM;iBAAM;YACzB;QACF;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAO,OAAO;YACnB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,QAAQ,UAAU,SAAS,IAAI,UAAU,WAAW,IAAI;QAC9D,OAAO,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAM,GAAG,CAAC,GAAG,kBACvC,8OAAC,oMAAA,CAAA,QAAK;gBAAS,WAAW,CAAC,QAAQ,EAAE,mBAAmB,OAAO,aAAa,CAAC;eAAjE;;;;;IAEhB;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,MAAM;QACN,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,MAAM;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC3B,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAU;;0BAGV,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,6HAAA,CAAA,UAAK;wBACJ,KAAK,CAAA,GAAA,mHAAA,CAAA,uBAAoB,AAAD,EAAE,KAAK,MAAM,EAAE,CAAC,EAAE,IAAI;wBAC9C,KAAK,KAAK,IAAI;wBACd,IAAI;wBACJ,WAAU;;;;;;kCAIZ,8OAAC;wBAAI,WAAU;;4BACZ,KAAK,WAAW,EAAE,8BACjB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAY;;;;;;;4BAI/B,KAAK,WAAW,EAAE,yBACjB,8OAAC;gCAAI,WAAU;0CAAyE;;;;;;;;;;;;kCAO5F,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,8OAAC;gCAAK,WAAU;0CACb,KAAK,OAAO,EAAE,SAAS,QAAQ,MAAM;;;;;;;;;;;;kCAK1C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;gCAAK,WAAU;;oCACb,KAAK,eAAe;oCAAC;;;;;;;;;;;;;;;;;;;0BAM5B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACX,KAAK,IAAI;;;;;;4BAEX,KAAK,UAAU,kBACd,8OAAC;gCAAI,WAAU;0CACZ,kBAAkB,KAAK,UAAU;;;;;;;;;;;;kCAKxC,8OAAC;wBAAE,WAAU;kCACV,KAAK,WAAW;;;;;;kCAInB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDACb,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;oCAEjB,KAAK,aAAa,IAAI,KAAK,aAAa,GAAG,8BAC1C,8OAAC;wCAAK,WAAU;kDACb,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,aAAa;;;;;;;;;;;;0CAIxC,8OAAC;gCAAK,WAAU;;oCACb,KAAK,UAAU,IAAI;oCAAE;;;;;;;;;;;;;oBAKzB,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG,mBACvC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAyC;;;;;;0CACtD,8OAAC;gCAAI,WAAU;0CACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAc,sBAChC,8OAAC;wCAEC,SAAS,IAAM,mBAAmB;wCAClC,WAAW,CAAC,qEAAqE,EAC/E,iBAAiB,SAAS,QAAQ,IAAI,GAClC,mDACA,yDACJ;;4CAED,QAAQ,IAAI;4CAAC;4CAAI,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,KAAK;;uCARzC;;;;;;;;;;;;;;;;oBAgBd,KAAK,MAAM,IAAI,KAAK,MAAM,CAAC,MAAM,GAAG,mBACnC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,qBAAqB,CAAC;gCACrC,WAAU;;oCAET,oBAAoB,SAAS;oCAAO;oCAAW,KAAK,MAAM,CAAC,MAAM;oCAAC;;;;;;;4BAGpE,mCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,QAAQ;gCAAE;gCACjC,SAAS;oCAAE,SAAS;oCAAG,QAAQ;gCAAO;gCACtC,MAAM;oCAAE,SAAS;oCAAG,QAAQ;gCAAE;gCAC9B,WAAU;0CAET,KAAK,MAAM,CAAC,GAAG,CAAC,CAAC,OAAY,sBAC5B,8OAAC;wCAEC,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAK;wDACL,SAAS,eAAe,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,MAAM,IAAI;wDACvD,UAAU,IAAM,kBAAkB;wDAClC,WAAU;;;;;;kEAEZ,8OAAC;wDAAK,WAAU;kEAAyB,MAAM,IAAI;;;;;;;;;;;;0DAErD,8OAAC;gDAAK,WAAU;;oDAAoC;oDAChD,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,KAAK;;;;;;;;uCAbzB;;;;;;;;;;;;;;;;kCAuBjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAoC;;;;;;kDACpD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gDACZ,YAAY;oDAAE,OAAO;gDAAI;gDACzB,UAAU;oDAAE,OAAO;gDAAI;gDACvB,SAAS,IAAM,YAAY,KAAK,GAAG,CAAC,GAAG,WAAW;gDAClD,WAAU;0DAEV,cAAA,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;0DAEnB,8OAAC;gDAAK,WAAU;0DAA+B;;;;;;0DAC/C,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gDACZ,YAAY;oDAAE,OAAO;gDAAI;gDACzB,UAAU;oDAAE,OAAO;gDAAI;gDACvB,SAAS,IAAM,YAAY,WAAW;gDACtC,WAAU;0DAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0CAKtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,SAAS;gCACT,WAAU;;kDAEV,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCACf,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;;;;;;;;oBAKnB,KAAK,SAAS,kBACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAA6B,KAAK,SAAS,CAAC,QAAQ;;;;;;sDACnE,8OAAC;sDAAI;;;;;;;;;;;;8CAEP,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;gDAA6B,KAAK,SAAS,CAAC,OAAO;gDAAC;;;;;;;sDACnE,8OAAC;sDAAI;;;;;;;;;;;;8CAEP,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;gDAA6B,KAAK,SAAS,CAAC,KAAK;gDAAC;;;;;;;sDACjE,8OAAC;sDAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrB;uCAEe", "debugId": null}}, {"offset": {"line": 2190, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/project%20food-order%206k/food-ordering-system-modern/frontend/src/components/CartSidebar.tsx"], "sourcesContent": ["'use client';\n\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { X, Plus, Minus, ShoppingCart, Trash2, ArrowRight } from 'lucide-react';\nimport Image from 'next/image';\nimport Link from 'next/link';\nimport useCartStore from '@/store/useCartStore';\nimport useAuthStore from '@/store/useAuthStore';\nimport { formatCurrency, getOptimizedImageUrl } from '@/lib/utils';\nimport { toast } from 'react-hot-toast';\n\nconst CartSidebar = () => {\n  const { \n    isOpen, \n    closeCart, \n    items, \n    updateQuantity, \n    removeItem, \n    clearCart, \n    getTotals \n  } = useCartStore();\n  \n  const { isAuthenticated } = useAuthStore();\n  const { subtotal, deliveryFee, packagingFee, total, itemCount } = getTotals();\n\n  const handleCheckout = () => {\n    if (!isAuthenticated) {\n      toast.error('Please login to proceed with checkout');\n      return;\n    }\n    \n    if (items.length === 0) {\n      toast.error('Your cart is empty');\n      return;\n    }\n\n    // Navigate to checkout\n    window.location.href = '/checkout';\n  };\n\n  const sidebarVariants = {\n    hidden: { x: '100%' },\n    visible: { x: 0 },\n    exit: { x: '100%' }\n  };\n\n  const backdropVariants = {\n    hidden: { opacity: 0 },\n    visible: { opacity: 1 },\n    exit: { opacity: 0 }\n  };\n\n  return (\n    <>\n      {/* Desktop Cart Summary */}\n      <div className=\"hidden lg:block\">\n        <div className=\"bg-white rounded-2xl shadow-lg p-6\">\n          <div className=\"flex items-center gap-2 mb-6\">\n            <ShoppingCart className=\"w-5 h-5 text-orange-500\" />\n            <h3 className=\"text-lg font-bold text-gray-900\">Your Order</h3>\n            {itemCount > 0 && (\n              <span className=\"bg-orange-100 text-orange-800 text-sm font-medium px-2 py-1 rounded-full\">\n                {itemCount}\n              </span>\n            )}\n          </div>\n\n          {items.length === 0 ? (\n            <div className=\"text-center py-8\">\n              <ShoppingCart className=\"w-12 h-12 text-gray-300 mx-auto mb-4\" />\n              <p className=\"text-gray-500 mb-2\">Your cart is empty</p>\n              <p className=\"text-sm text-gray-400\">Add items to get started</p>\n            </div>\n          ) : (\n            <>\n              {/* Cart Items */}\n              <div className=\"space-y-4 mb-6 max-h-96 overflow-y-auto\">\n                {items.map((item) => (\n                  <div key={item.id} className=\"flex gap-3 p-3 bg-gray-50 rounded-lg\">\n                    <div className=\"relative w-16 h-16 flex-shrink-0\">\n                      <Image\n                        src={getOptimizedImageUrl(item.food.images?.[0] || '/images/food-placeholder.jpg')}\n                        alt={item.food.name}\n                        fill\n                        className=\"object-cover rounded-lg\"\n                      />\n                    </div>\n                    \n                    <div className=\"flex-1 min-w-0\">\n                      <h4 className=\"font-medium text-gray-900 text-sm line-clamp-1\">\n                        {item.food.name}\n                      </h4>\n                      {item.variant && (\n                        <p className=\"text-xs text-gray-500\">{item.variant.name}</p>\n                      )}\n                      {item.addons.length > 0 && (\n                        <p className=\"text-xs text-gray-500\">\n                          +{item.addons.map(a => a.name).join(', ')}\n                        </p>\n                      )}\n                      \n                      <div className=\"flex items-center justify-between mt-2\">\n                        <div className=\"flex items-center gap-2\">\n                          <button\n                            onClick={() => updateQuantity(item.id, Math.max(0, item.quantity - 1))}\n                            className=\"w-6 h-6 bg-white rounded-full flex items-center justify-center border border-gray-200 hover:border-orange-300 transition-colors duration-200\"\n                          >\n                            <Minus className=\"w-3 h-3 text-gray-600\" />\n                          </button>\n                          <span className=\"text-sm font-medium w-6 text-center\">\n                            {item.quantity}\n                          </span>\n                          <button\n                            onClick={() => updateQuantity(item.id, item.quantity + 1)}\n                            className=\"w-6 h-6 bg-white rounded-full flex items-center justify-center border border-gray-200 hover:border-orange-300 transition-colors duration-200\"\n                          >\n                            <Plus className=\"w-3 h-3 text-gray-600\" />\n                          </button>\n                        </div>\n                        \n                        <div className=\"flex items-center gap-2\">\n                          <span className=\"text-sm font-medium text-gray-900\">\n                            {formatCurrency(item.totalPrice)}\n                          </span>\n                          <button\n                            onClick={() => removeItem(item.id)}\n                            className=\"text-red-500 hover:text-red-700 transition-colors duration-200\"\n                          >\n                            <Trash2 className=\"w-4 h-4\" />\n                          </button>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n\n              {/* Order Summary */}\n              <div className=\"border-t border-gray-200 pt-4 space-y-2\">\n                <div className=\"flex justify-between text-sm\">\n                  <span className=\"text-gray-600\">Subtotal</span>\n                  <span className=\"font-medium\">{formatCurrency(subtotal)}</span>\n                </div>\n                <div className=\"flex justify-between text-sm\">\n                  <span className=\"text-gray-600\">Delivery Fee</span>\n                  <span className=\"font-medium\">{formatCurrency(deliveryFee)}</span>\n                </div>\n                <div className=\"flex justify-between text-sm\">\n                  <span className=\"text-gray-600\">Packaging Fee</span>\n                  <span className=\"font-medium\">{formatCurrency(packagingFee)}</span>\n                </div>\n                <div className=\"flex justify-between text-lg font-bold border-t border-gray-200 pt-2\">\n                  <span>Total</span>\n                  <span>{formatCurrency(total)}</span>\n                </div>\n              </div>\n\n              {/* Checkout Button */}\n              <motion.button\n                whileHover={{ scale: 1.02 }}\n                whileTap={{ scale: 0.98 }}\n                onClick={handleCheckout}\n                className=\"w-full bg-orange-500 text-white py-3 px-4 rounded-lg font-medium hover:bg-orange-600 transition-colors duration-200 flex items-center justify-center gap-2 mt-6\"\n              >\n                Proceed to Checkout\n                <ArrowRight className=\"w-4 h-4\" />\n              </motion.button>\n            </>\n          )}\n        </div>\n      </div>\n\n      {/* Mobile Cart Sidebar */}\n      <AnimatePresence>\n        {isOpen && (\n          <>\n            {/* Backdrop */}\n            <motion.div\n              variants={backdropVariants}\n              initial=\"hidden\"\n              animate=\"visible\"\n              exit=\"exit\"\n              onClick={closeCart}\n              className=\"lg:hidden fixed inset-0 bg-black/50 z-50\"\n            />\n\n            {/* Sidebar */}\n            <motion.div\n              variants={sidebarVariants}\n              initial=\"hidden\"\n              animate=\"visible\"\n              exit=\"exit\"\n              transition={{ type: 'tween', duration: 0.3 }}\n              className=\"lg:hidden fixed top-0 right-0 h-full w-full max-w-md bg-white z-50 flex flex-col\"\n            >\n              {/* Header */}\n              <div className=\"flex items-center justify-between p-6 border-b border-gray-200\">\n                <div className=\"flex items-center gap-2\">\n                  <ShoppingCart className=\"w-5 h-5 text-orange-500\" />\n                  <h3 className=\"text-lg font-bold text-gray-900\">Your Order</h3>\n                  {itemCount > 0 && (\n                    <span className=\"bg-orange-100 text-orange-800 text-sm font-medium px-2 py-1 rounded-full\">\n                      {itemCount}\n                    </span>\n                  )}\n                </div>\n                <button\n                  onClick={closeCart}\n                  className=\"p-2 hover:bg-gray-100 rounded-full transition-colors duration-200\"\n                >\n                  <X className=\"w-5 h-5 text-gray-600\" />\n                </button>\n              </div>\n\n              {/* Content */}\n              <div className=\"flex-1 overflow-hidden flex flex-col\">\n                {items.length === 0 ? (\n                  <div className=\"flex-1 flex items-center justify-center\">\n                    <div className=\"text-center py-8\">\n                      <ShoppingCart className=\"w-16 h-16 text-gray-300 mx-auto mb-4\" />\n                      <p className=\"text-gray-500 mb-2\">Your cart is empty</p>\n                      <p className=\"text-sm text-gray-400\">Add items to get started</p>\n                    </div>\n                  </div>\n                ) : (\n                  <>\n                    {/* Cart Items */}\n                    <div className=\"flex-1 overflow-y-auto p-6\">\n                      <div className=\"space-y-4\">\n                        {items.map((item) => (\n                          <div key={item.id} className=\"flex gap-3 p-3 bg-gray-50 rounded-lg\">\n                            <div className=\"relative w-16 h-16 flex-shrink-0\">\n                              <Image\n                                src={getOptimizedImageUrl(item.food.images?.[0] || '/images/food-placeholder.jpg')}\n                                alt={item.food.name}\n                                fill\n                                className=\"object-cover rounded-lg\"\n                              />\n                            </div>\n                            \n                            <div className=\"flex-1 min-w-0\">\n                              <h4 className=\"font-medium text-gray-900 text-sm line-clamp-1\">\n                                {item.food.name}\n                              </h4>\n                              {item.variant && (\n                                <p className=\"text-xs text-gray-500\">{item.variant.name}</p>\n                              )}\n                              {item.addons.length > 0 && (\n                                <p className=\"text-xs text-gray-500\">\n                                  +{item.addons.map(a => a.name).join(', ')}\n                                </p>\n                              )}\n                              \n                              <div className=\"flex items-center justify-between mt-2\">\n                                <div className=\"flex items-center gap-2\">\n                                  <button\n                                    onClick={() => updateQuantity(item.id, Math.max(0, item.quantity - 1))}\n                                    className=\"w-8 h-8 bg-white rounded-full flex items-center justify-center border border-gray-200 hover:border-orange-300 transition-colors duration-200\"\n                                  >\n                                    <Minus className=\"w-4 h-4 text-gray-600\" />\n                                  </button>\n                                  <span className=\"text-sm font-medium w-8 text-center\">\n                                    {item.quantity}\n                                  </span>\n                                  <button\n                                    onClick={() => updateQuantity(item.id, item.quantity + 1)}\n                                    className=\"w-8 h-8 bg-white rounded-full flex items-center justify-center border border-gray-200 hover:border-orange-300 transition-colors duration-200\"\n                                  >\n                                    <Plus className=\"w-4 h-4 text-gray-600\" />\n                                  </button>\n                                </div>\n                                \n                                <div className=\"flex items-center gap-2\">\n                                  <span className=\"text-sm font-medium text-gray-900\">\n                                    {formatCurrency(item.totalPrice)}\n                                  </span>\n                                  <button\n                                    onClick={() => removeItem(item.id)}\n                                    className=\"text-red-500 hover:text-red-700 transition-colors duration-200\"\n                                  >\n                                    <Trash2 className=\"w-4 h-4\" />\n                                  </button>\n                                </div>\n                              </div>\n                            </div>\n                          </div>\n                        ))}\n                      </div>\n                    </div>\n\n                    {/* Footer with Summary and Checkout */}\n                    <div className=\"border-t border-gray-200 p-6\">\n                      {/* Order Summary */}\n                      <div className=\"space-y-2 mb-6\">\n                        <div className=\"flex justify-between text-sm\">\n                          <span className=\"text-gray-600\">Subtotal</span>\n                          <span className=\"font-medium\">{formatCurrency(subtotal)}</span>\n                        </div>\n                        <div className=\"flex justify-between text-sm\">\n                          <span className=\"text-gray-600\">Delivery Fee</span>\n                          <span className=\"font-medium\">{formatCurrency(deliveryFee)}</span>\n                        </div>\n                        <div className=\"flex justify-between text-sm\">\n                          <span className=\"text-gray-600\">Packaging Fee</span>\n                          <span className=\"font-medium\">{formatCurrency(packagingFee)}</span>\n                        </div>\n                        <div className=\"flex justify-between text-lg font-bold border-t border-gray-200 pt-2\">\n                          <span>Total</span>\n                          <span>{formatCurrency(total)}</span>\n                        </div>\n                      </div>\n\n                      {/* Checkout Button */}\n                      <motion.button\n                        whileHover={{ scale: 1.02 }}\n                        whileTap={{ scale: 0.98 }}\n                        onClick={handleCheckout}\n                        className=\"w-full bg-orange-500 text-white py-3 px-4 rounded-lg font-medium hover:bg-orange-600 transition-colors duration-200 flex items-center justify-center gap-2\"\n                      >\n                        Proceed to Checkout\n                        <ArrowRight className=\"w-4 h-4\" />\n                      </motion.button>\n                    </div>\n                  </>\n                )}\n              </div>\n            </motion.div>\n          </>\n        )}\n      </AnimatePresence>\n    </>\n  );\n};\n\nexport default CartSidebar;\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AACA;AATA;;;;;;;;;AAWA,MAAM,cAAc;IAClB,MAAM,EACJ,MAAM,EACN,SAAS,EACT,KAAK,EACL,cAAc,EACd,UAAU,EACV,SAAS,EACT,SAAS,EACV,GAAG,CAAA,GAAA,4HAAA,CAAA,UAAY,AAAD;IAEf,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,UAAY,AAAD;IACvC,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,YAAY,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG;IAElE,MAAM,iBAAiB;QACrB,IAAI,CAAC,iBAAiB;YACpB,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,MAAM,MAAM,KAAK,GAAG;YACtB,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,uBAAuB;QACvB,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,MAAM,kBAAkB;QACtB,QAAQ;YAAE,GAAG;QAAO;QACpB,SAAS;YAAE,GAAG;QAAE;QAChB,MAAM;YAAE,GAAG;QAAO;IACpB;IAEA,MAAM,mBAAmB;QACvB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YAAE,SAAS;QAAE;QACtB,MAAM;YAAE,SAAS;QAAE;IACrB;IAEA,qBACE;;0BAEE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,sNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;8CACxB,8OAAC;oCAAG,WAAU;8CAAkC;;;;;;gCAC/C,YAAY,mBACX,8OAAC;oCAAK,WAAU;8CACb;;;;;;;;;;;;wBAKN,MAAM,MAAM,KAAK,kBAChB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,sNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;8CACxB,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAClC,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;qFAGvC;;8CAEE,8OAAC;oCAAI,WAAU;8CACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;4CAAkB,WAAU;;8DAC3B,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;wDACJ,KAAK,CAAA,GAAA,mHAAA,CAAA,uBAAoB,AAAD,EAAE,KAAK,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,IAAI;wDACnD,KAAK,KAAK,IAAI,CAAC,IAAI;wDACnB,IAAI;wDACJ,WAAU;;;;;;;;;;;8DAId,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEACX,KAAK,IAAI,CAAC,IAAI;;;;;;wDAEhB,KAAK,OAAO,kBACX,8OAAC;4DAAE,WAAU;sEAAyB,KAAK,OAAO,CAAC,IAAI;;;;;;wDAExD,KAAK,MAAM,CAAC,MAAM,GAAG,mBACpB,8OAAC;4DAAE,WAAU;;gEAAwB;gEACjC,KAAK,MAAM,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,EAAE,IAAI,CAAC;;;;;;;sEAIxC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EACC,SAAS,IAAM,eAAe,KAAK,EAAE,EAAE,KAAK,GAAG,CAAC,GAAG,KAAK,QAAQ,GAAG;4EACnE,WAAU;sFAEV,cAAA,8OAAC,oMAAA,CAAA,QAAK;gFAAC,WAAU;;;;;;;;;;;sFAEnB,8OAAC;4EAAK,WAAU;sFACb,KAAK,QAAQ;;;;;;sFAEhB,8OAAC;4EACC,SAAS,IAAM,eAAe,KAAK,EAAE,EAAE,KAAK,QAAQ,GAAG;4EACvD,WAAU;sFAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;;;;;;;;;;;;8EAIpB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFACb,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,UAAU;;;;;;sFAEjC,8OAAC;4EACC,SAAS,IAAM,WAAW,KAAK,EAAE;4EACjC,WAAU;sFAEV,cAAA,8OAAC,0MAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2CAlDlB,KAAK,EAAE;;;;;;;;;;8CA4DrB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;8DAAe,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;;;;;;;sDAEhD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;8DAAe,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;;;;;;;sDAEhD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;8DAAe,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;;;;;;;sDAEhD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAK;;;;;;8DACN,8OAAC;8DAAM,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;;;;;;;;;;;;;8CAK1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,SAAS;oCACT,WAAU;;wCACX;sDAEC,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhC,8OAAC,yLAAA,CAAA,kBAAe;0BACb,wBACC;;sCAEE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU;4BACV,SAAQ;4BACR,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;;;;;;sCAIZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU;4BACV,SAAQ;4BACR,SAAQ;4BACR,MAAK;4BACL,YAAY;gCAAE,MAAM;gCAAS,UAAU;4BAAI;4BAC3C,WAAU;;8CAGV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,sNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;8DACxB,8OAAC;oDAAG,WAAU;8DAAkC;;;;;;gDAC/C,YAAY,mBACX,8OAAC;oDAAK,WAAU;8DACb;;;;;;;;;;;;sDAIP,8OAAC;4CACC,SAAS;4CACT,WAAU;sDAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAKjB,8OAAC;oCAAI,WAAU;8CACZ,MAAM,MAAM,KAAK,kBAChB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,sNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;8DACxB,8OAAC;oDAAE,WAAU;8DAAqB;;;;;;8DAClC,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;iGAIzC;;0DAEE,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;4DAAkB,WAAU;;8EAC3B,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;wEACJ,KAAK,CAAA,GAAA,mHAAA,CAAA,uBAAoB,AAAD,EAAE,KAAK,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,IAAI;wEACnD,KAAK,KAAK,IAAI,CAAC,IAAI;wEACnB,IAAI;wEACJ,WAAU;;;;;;;;;;;8EAId,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAG,WAAU;sFACX,KAAK,IAAI,CAAC,IAAI;;;;;;wEAEhB,KAAK,OAAO,kBACX,8OAAC;4EAAE,WAAU;sFAAyB,KAAK,OAAO,CAAC,IAAI;;;;;;wEAExD,KAAK,MAAM,CAAC,MAAM,GAAG,mBACpB,8OAAC;4EAAE,WAAU;;gFAAwB;gFACjC,KAAK,MAAM,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,EAAE,IAAI,CAAC;;;;;;;sFAIxC,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAI,WAAU;;sGACb,8OAAC;4FACC,SAAS,IAAM,eAAe,KAAK,EAAE,EAAE,KAAK,GAAG,CAAC,GAAG,KAAK,QAAQ,GAAG;4FACnE,WAAU;sGAEV,cAAA,8OAAC,oMAAA,CAAA,QAAK;gGAAC,WAAU;;;;;;;;;;;sGAEnB,8OAAC;4FAAK,WAAU;sGACb,KAAK,QAAQ;;;;;;sGAEhB,8OAAC;4FACC,SAAS,IAAM,eAAe,KAAK,EAAE,EAAE,KAAK,QAAQ,GAAG;4FACvD,WAAU;sGAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;gGAAC,WAAU;;;;;;;;;;;;;;;;;8FAIpB,8OAAC;oFAAI,WAAU;;sGACb,8OAAC;4FAAK,WAAU;sGACb,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,UAAU;;;;;;sGAEjC,8OAAC;4FACC,SAAS,IAAM,WAAW,KAAK,EAAE;4FACjC,WAAU;sGAEV,cAAA,8OAAC,0MAAA,CAAA,SAAM;gGAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2DAlDlB,KAAK,EAAE;;;;;;;;;;;;;;;0DA6DvB,8OAAC;gDAAI,WAAU;;kEAEb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,8OAAC;wEAAK,WAAU;kFAAe,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;;;;;;;0EAEhD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,8OAAC;wEAAK,WAAU;kFAAe,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;;;;;;;0EAEhD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,8OAAC;wEAAK,WAAU;kFAAe,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;;;;;;;0EAEhD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;kFAAK;;;;;;kFACN,8OAAC;kFAAM,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;;;;;;;;;;;;;kEAK1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wDACZ,YAAY;4DAAE,OAAO;wDAAK;wDAC1B,UAAU;4DAAE,OAAO;wDAAK;wDACxB,SAAS;wDACT,WAAU;;4DACX;0EAEC,8OAAC,kNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY9C;uCAEe", "debugId": null}}, {"offset": {"line": 3083, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/project%20food-order%206k/food-ordering-system-modern/frontend/src/app/restaurants/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useParams } from 'next/navigation';\nimport { useQuery } from '@tanstack/react-query';\nimport { \n  Star, \n  Clock, \n  MapPin, \n  Phone, \n  Heart, \n  Share2,\n  Filter,\n  Search,\n  Plus,\n  Minus,\n  ShoppingCart,\n  ArrowLeft\n} from 'lucide-react';\nimport Image from 'next/image';\nimport Link from 'next/link';\nimport Header from '@/components/Header';\nimport Footer from '@/components/Footer';\nimport FoodCard from '@/components/FoodCard';\nimport CartSidebar from '@/components/CartSidebar';\nimport { restaurantAPI, foodAPI } from '@/lib/api';\nimport { formatCurrency, getOptimizedImageUrl } from '@/lib/utils';\nimport useCartStore from '@/store/useCartStore';\nimport useAuthStore from '@/store/useAuthStore';\nimport { toast } from 'react-hot-toast';\n\nconst RestaurantDetailPage = () => {\n  const params = useParams();\n  const restaurantId = params.id as string;\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [searchQuery, setSearchQuery] = useState('');\n  const [isFavorite, setIsFavorite] = useState(false);\n\n  const { isAuthenticated } = useAuthStore();\n  const { isOpen: isCartOpen, openCart, getTotals } = useCartStore();\n  const { itemCount } = getTotals();\n\n  // Debug logging\n  useEffect(() => {\n    console.log('Restaurant ID from params:', restaurantId);\n    console.log('Params object:', params);\n  }, [restaurantId, params]);\n\n  // Validate restaurant ID format\n  const isValidObjectId = restaurantId && /^[0-9a-fA-F]{24}$/.test(restaurantId);\n\n  // Fetch restaurant details\n  const { data: restaurantData, isLoading: restaurantLoading, error: restaurantError } = useQuery({\n    queryKey: ['restaurant', restaurantId],\n    queryFn: () => restaurantAPI.getById(restaurantId),\n    enabled: Boolean(restaurantId && isValidObjectId),\n  });\n\n  // Fetch restaurant foods\n  const { data: foodsData, isLoading: foodsLoading } = useQuery({\n    queryKey: ['restaurant-foods', restaurantId],\n    queryFn: () => foodAPI.getByRestaurant(restaurantId),\n    enabled: Boolean(restaurantId && isValidObjectId),\n  });\n\n  const restaurant = restaurantData?.data?.data?.restaurant;\n  const foods = foodsData?.data?.data || {};\n\n  // Get categories from foods\n  const categories = Object.keys(foods);\n  const allFoods = Object.values(foods).flat();\n\n  // Filter foods based on search and category\n  const filteredFoods = allFoods.filter((food: any) => {\n    const matchesSearch = food.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n                         food.description.toLowerCase().includes(searchQuery.toLowerCase());\n    const matchesCategory = selectedCategory === 'all' || food.category === selectedCategory;\n    return matchesSearch && matchesCategory;\n  });\n\n  const handleFavoriteToggle = async () => {\n    if (!isAuthenticated) {\n      toast.error('Please login to add favorites');\n      return;\n    }\n    // TODO: Implement favorite toggle\n    setIsFavorite(!isFavorite);\n    toast.success(isFavorite ? 'Removed from favorites' : 'Added to favorites');\n  };\n\n  const handleShare = async () => {\n    if (navigator.share) {\n      try {\n        await navigator.share({\n          title: restaurant?.name,\n          text: restaurant?.description,\n          url: window.location.href,\n        });\n      } catch (error) {\n        console.log('Error sharing:', error);\n      }\n    } else {\n      // Fallback: copy to clipboard\n      navigator.clipboard.writeText(window.location.href);\n      toast.success('Link copied to clipboard!');\n    }\n  };\n\n  // Handle invalid restaurant ID\n  if (!restaurantId || restaurantId === 'undefined' || !isValidObjectId) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <Header />\n        <div className=\"pt-20 pb-16\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n            <div className=\"py-12\">\n              <h1 className=\"text-2xl font-bold text-gray-900 mb-4\">Invalid Restaurant ID</h1>\n              <p className=\"text-gray-600 mb-8\">The restaurant link appears to be invalid.</p>\n              <Link href=\"/restaurants\">\n                <motion.button\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                  className=\"bg-orange-500 text-white px-6 py-3 rounded-lg font-medium hover:bg-orange-600 transition-colors duration-200\"\n                >\n                  Browse Restaurants\n                </motion.button>\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  if (restaurantLoading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <Header />\n        <div className=\"pt-20 pb-16\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"animate-pulse\">\n              <div className=\"h-64 bg-gray-200 rounded-2xl mb-8\"></div>\n              <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-8\">\n                <div className=\"lg:col-span-3\">\n                  <div className=\"h-8 bg-gray-200 rounded w-1/3 mb-4\"></div>\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                    {[...Array(6)].map((_, index) => (\n                      <div key={index} className=\"bg-gray-200 rounded-2xl h-48\"></div>\n                    ))}\n                  </div>\n                </div>\n                <div className=\"h-96 bg-gray-200 rounded-2xl\"></div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  if (restaurantError || !restaurant) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <Header />\n        <div className=\"pt-20 pb-16\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n            <div className=\"py-12\">\n              <h1 className=\"text-2xl font-bold text-gray-900 mb-4\">Restaurant not found</h1>\n              <p className=\"text-gray-600 mb-8\">The restaurant you're looking for doesn't exist.</p>\n              <Link href=\"/restaurants\">\n                <motion.button\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                  className=\"bg-orange-500 text-white px-6 py-3 rounded-lg font-medium hover:bg-orange-600 transition-colors duration-200\"\n                >\n                  Browse Restaurants\n                </motion.button>\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Header />\n      \n      {/* Restaurant Hero Section */}\n      <section className=\"pt-20 pb-8\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          {/* Back Button */}\n          <motion.div\n            initial={{ opacity: 0, x: -20 }}\n            animate={{ opacity: 1, x: 0 }}\n            className=\"mb-6\"\n          >\n            <Link\n              href=\"/restaurants\"\n              className=\"inline-flex items-center gap-2 text-gray-600 hover:text-orange-500 transition-colors duration-200\"\n            >\n              <ArrowLeft className=\"w-5 h-5\" />\n              Back to Restaurants\n            </Link>\n          </motion.div>\n\n          {/* Restaurant Header */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            className=\"bg-white rounded-2xl shadow-lg overflow-hidden\"\n          >\n            {/* Banner Image */}\n            <div className=\"relative h-64 md:h-80\">\n              <Image\n                src={getOptimizedImageUrl(restaurant.images?.banner || '/images/restaurant-placeholder.jpg')}\n                alt={restaurant.name}\n                fill\n                className=\"object-cover\"\n              />\n              <div className=\"absolute inset-0 bg-gradient-to-t from-black/50 to-transparent\" />\n              \n              {/* Action Buttons */}\n              <div className=\"absolute top-4 right-4 flex gap-2\">\n                <motion.button\n                  whileHover={{ scale: 1.1 }}\n                  whileTap={{ scale: 0.9 }}\n                  onClick={handleFavoriteToggle}\n                  className=\"w-10 h-10 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center shadow-md hover:bg-white transition-colors duration-200\"\n                >\n                  <Heart \n                    className={`w-5 h-5 transition-colors duration-200 ${\n                      isFavorite ? 'text-red-500 fill-current' : 'text-gray-600'\n                    }`} \n                  />\n                </motion.button>\n                <motion.button\n                  whileHover={{ scale: 1.1 }}\n                  whileTap={{ scale: 0.9 }}\n                  onClick={handleShare}\n                  className=\"w-10 h-10 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center shadow-md hover:bg-white transition-colors duration-200\"\n                >\n                  <Share2 className=\"w-5 h-5 text-gray-600\" />\n                </motion.button>\n              </div>\n\n              {/* Status Badge */}\n              <div className=\"absolute top-4 left-4\">\n                <span className={`px-3 py-1 text-sm font-medium rounded-full ${\n                  restaurant.isOpen \n                    ? 'bg-green-100 text-green-800' \n                    : 'bg-red-100 text-red-800'\n                }`}>\n                  {restaurant.isOpen ? 'Open Now' : 'Closed'}\n                </span>\n              </div>\n            </div>\n\n            {/* Restaurant Info */}\n            <div className=\"p-6\">\n              <div className=\"flex flex-col lg:flex-row lg:items-start lg:justify-between gap-6\">\n                <div className=\"flex-1\">\n                  <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n                    {restaurant.name}\n                  </h1>\n                  <p className=\"text-gray-600 mb-4\">\n                    {restaurant.description}\n                  </p>\n\n                  {/* Restaurant Stats */}\n                  <div className=\"flex flex-wrap items-center gap-6 text-sm text-gray-600\">\n                    <div className=\"flex items-center gap-1\">\n                      <Star className=\"w-4 h-4 text-yellow-400 fill-current\" />\n                      <span className=\"font-medium text-gray-900\">\n                        {restaurant.ratings?.average?.toFixed(1) || '0.0'}\n                      </span>\n                      <span>({restaurant.ratings?.count || 0} reviews)</span>\n                    </div>\n                    <div className=\"flex items-center gap-1\">\n                      <Clock className=\"w-4 h-4\" />\n                      <span>{restaurant.preparationTime || 30} mins</span>\n                    </div>\n                    <div className=\"flex items-center gap-1\">\n                      <MapPin className=\"w-4 h-4\" />\n                      <span>{restaurant.location?.address?.city}</span>\n                    </div>\n                    <div className=\"flex items-center gap-1\">\n                      <Phone className=\"w-4 h-4\" />\n                      <span>{restaurant.phone}</span>\n                    </div>\n                  </div>\n\n                  {/* Cuisine Tags */}\n                  <div className=\"flex flex-wrap gap-2 mt-4\">\n                    {restaurant.cuisine?.map((cuisine: string, index: number) => (\n                      <span\n                        key={index}\n                        className=\"px-3 py-1 bg-orange-100 text-orange-800 text-sm font-medium rounded-full\"\n                      >\n                        {cuisine}\n                      </span>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Delivery Info */}\n                <div className=\"bg-gray-50 rounded-xl p-4 lg:w-80\">\n                  <h3 className=\"font-semibold text-gray-900 mb-3\">Delivery Info</h3>\n                  <div className=\"space-y-2 text-sm\">\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-gray-600\">Delivery Fee:</span>\n                      <span className=\"font-medium\">{formatCurrency(restaurant.pricing?.deliveryFee || 0)}</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-gray-600\">Minimum Order:</span>\n                      <span className=\"font-medium\">{formatCurrency(restaurant.pricing?.minimumOrder || 0)}</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-gray-600\">Packaging Fee:</span>\n                      <span className=\"font-medium\">{formatCurrency(restaurant.pricing?.packagingFee || 0)}</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        </div>\n      </section>\n\n      {/* Menu Section */}\n      <section className=\"pb-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-8\">\n            {/* Menu Content */}\n            <div className=\"lg:col-span-3\">\n              {/* Search and Filters */}\n              <div className=\"bg-white rounded-xl shadow-sm p-6 mb-8\">\n                <div className=\"flex flex-col sm:flex-row gap-4\">\n                  {/* Search */}\n                  <div className=\"flex-1 relative\">\n                    <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                      <Search className=\"h-5 w-5 text-gray-400\" />\n                    </div>\n                    <input\n                      type=\"text\"\n                      value={searchQuery}\n                      onChange={(e) => setSearchQuery(e.target.value)}\n                      className=\"block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent\"\n                      placeholder=\"Search menu items...\"\n                    />\n                  </div>\n\n                  {/* Category Filter */}\n                  <select\n                    value={selectedCategory}\n                    onChange={(e) => setSelectedCategory(e.target.value)}\n                    className=\"px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent\"\n                  >\n                    <option value=\"all\">All Categories</option>\n                    {categories.map((category) => (\n                      <option key={category} value={category}>\n                        {category.charAt(0).toUpperCase() + category.slice(1).replace('-', ' ')}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n              </div>\n\n              {/* Menu Items */}\n              {foodsLoading ? (\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  {[...Array(6)].map((_, index) => (\n                    <div key={index} className=\"animate-pulse\">\n                      <div className=\"bg-gray-200 rounded-2xl h-48 mb-4\"></div>\n                      <div className=\"space-y-2\">\n                        <div className=\"h-4 bg-gray-200 rounded w-3/4\"></div>\n                        <div className=\"h-3 bg-gray-200 rounded w-1/2\"></div>\n                        <div className=\"h-3 bg-gray-200 rounded w-2/3\"></div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              ) : filteredFoods.length === 0 ? (\n                <div className=\"text-center py-12\">\n                  <div className=\"text-gray-500 mb-4\">\n                    <Search className=\"w-16 h-16 mx-auto mb-4 opacity-50\" />\n                    <p className=\"text-lg\">No menu items found</p>\n                    <p className=\"text-sm\">Try adjusting your search or filters</p>\n                  </div>\n                </div>\n              ) : (\n                <motion.div\n                  layout\n                  className=\"grid grid-cols-1 md:grid-cols-2 gap-6\"\n                >\n                  <AnimatePresence>\n                    {filteredFoods.map((food: any) => (\n                      <FoodCard\n                        key={food._id}\n                        food={food}\n                        restaurant={restaurant}\n                      />\n                    ))}\n                  </AnimatePresence>\n                </motion.div>\n              )}\n            </div>\n\n            {/* Cart Summary - Desktop */}\n            <div className=\"hidden lg:block\">\n              <div className=\"sticky top-24\">\n                <CartSidebar />\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Floating Cart Button - Mobile */}\n      {itemCount > 0 && (\n        <motion.button\n          initial={{ scale: 0 }}\n          animate={{ scale: 1 }}\n          whileHover={{ scale: 1.05 }}\n          whileTap={{ scale: 0.95 }}\n          onClick={openCart}\n          className=\"lg:hidden fixed bottom-6 right-6 bg-orange-500 text-white p-4 rounded-full shadow-lg z-40 flex items-center gap-2\"\n        >\n          <ShoppingCart className=\"w-6 h-6\" />\n          <span className=\"font-medium\">{itemCount}</span>\n        </motion.button>\n      )}\n\n      {/* Cart Sidebar - Mobile */}\n      <CartSidebar />\n\n      <Footer />\n    </div>\n  );\n};\n\nexport default RestaurantDetailPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA9BA;;;;;;;;;;;;;;;;;;AAgCA,MAAM,uBAAuB;IAC3B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,OAAO,EAAE;IAC9B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,UAAY,AAAD;IACvC,MAAM,EAAE,QAAQ,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,UAAY,AAAD;IAC/D,MAAM,EAAE,SAAS,EAAE,GAAG;IAEtB,gBAAgB;IAChB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ,GAAG,CAAC,8BAA8B;QAC1C,QAAQ,GAAG,CAAC,kBAAkB;IAChC,GAAG;QAAC;QAAc;KAAO;IAEzB,gCAAgC;IAChC,MAAM,kBAAkB,gBAAgB,oBAAoB,IAAI,CAAC;IAEjE,2BAA2B;IAC3B,MAAM,EAAE,MAAM,cAAc,EAAE,WAAW,iBAAiB,EAAE,OAAO,eAAe,EAAE,GAAG,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QAC9F,UAAU;YAAC;YAAc;SAAa;QACtC,SAAS,IAAM,iHAAA,CAAA,gBAAa,CAAC,OAAO,CAAC;QACrC,SAAS,QAAQ,gBAAgB;IACnC;IAEA,yBAAyB;IACzB,MAAM,EAAE,MAAM,SAAS,EAAE,WAAW,YAAY,EAAE,GAAG,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QAC5D,UAAU;YAAC;YAAoB;SAAa;QAC5C,SAAS,IAAM,iHAAA,CAAA,UAAO,CAAC,eAAe,CAAC;QACvC,SAAS,QAAQ,gBAAgB;IACnC;IAEA,MAAM,aAAa,gBAAgB,MAAM,MAAM;IAC/C,MAAM,QAAQ,WAAW,MAAM,QAAQ,CAAC;IAExC,4BAA4B;IAC5B,MAAM,aAAa,OAAO,IAAI,CAAC;IAC/B,MAAM,WAAW,OAAO,MAAM,CAAC,OAAO,IAAI;IAE1C,4CAA4C;IAC5C,MAAM,gBAAgB,SAAS,MAAM,CAAC,CAAC;QACrC,MAAM,gBAAgB,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OACzD,KAAK,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;QACpF,MAAM,kBAAkB,qBAAqB,SAAS,KAAK,QAAQ,KAAK;QACxE,OAAO,iBAAiB;IAC1B;IAEA,MAAM,uBAAuB;QAC3B,IAAI,CAAC,iBAAiB;YACpB,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QACA,kCAAkC;QAClC,cAAc,CAAC;QACf,uJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,aAAa,2BAA2B;IACxD;IAEA,MAAM,cAAc;QAClB,IAAI,UAAU,KAAK,EAAE;YACnB,IAAI;gBACF,MAAM,UAAU,KAAK,CAAC;oBACpB,OAAO,YAAY;oBACnB,MAAM,YAAY;oBAClB,KAAK,OAAO,QAAQ,CAAC,IAAI;gBAC3B;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,GAAG,CAAC,kBAAkB;YAChC;QACF,OAAO;YACL,8BAA8B;YAC9B,UAAU,SAAS,CAAC,SAAS,CAAC,OAAO,QAAQ,CAAC,IAAI;YAClD,uJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB;IACF;IAEA,+BAA+B;IAC/B,IAAI,CAAC,gBAAgB,iBAAiB,eAAe,CAAC,iBAAiB;QACrE,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,4HAAA,CAAA,UAAM;;;;;8BACP,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAClC,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IASf;IAEA,IAAI,mBAAmB;QACrB,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,4HAAA,CAAA,UAAM;;;;;8BACP,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;8DACZ;2DAAI,MAAM;qDAAG,CAAC,GAAG,CAAC,CAAC,GAAG,sBACrB,8OAAC;4DAAgB,WAAU;2DAAjB;;;;;;;;;;;;;;;;sDAIhB,8OAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAO7B;IAEA,IAAI,mBAAmB,CAAC,YAAY;QAClC,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,4HAAA,CAAA,UAAM;;;;;8BACP,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAClC,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IASf;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,UAAM;;;;;0BAGP,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,WAAU;sCAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;sCAMrC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;;8CAGV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,6HAAA,CAAA,UAAK;4CACJ,KAAK,CAAA,GAAA,mHAAA,CAAA,uBAAoB,AAAD,EAAE,WAAW,MAAM,EAAE,UAAU;4CACvD,KAAK,WAAW,IAAI;4CACpB,IAAI;4CACJ,WAAU;;;;;;sDAEZ,8OAAC;4CAAI,WAAU;;;;;;sDAGf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oDACZ,YAAY;wDAAE,OAAO;oDAAI;oDACzB,UAAU;wDAAE,OAAO;oDAAI;oDACvB,SAAS;oDACT,WAAU;8DAEV,cAAA,8OAAC,oMAAA,CAAA,QAAK;wDACJ,WAAW,CAAC,uCAAuC,EACjD,aAAa,8BAA8B,iBAC3C;;;;;;;;;;;8DAGN,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oDACZ,YAAY;wDAAE,OAAO;oDAAI;oDACzB,UAAU;wDAAE,OAAO;oDAAI;oDACvB,SAAS;oDACT,WAAU;8DAEV,cAAA,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAKtB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAW,CAAC,2CAA2C,EAC3D,WAAW,MAAM,GACb,gCACA,2BACJ;0DACC,WAAW,MAAM,GAAG,aAAa;;;;;;;;;;;;;;;;;8CAMxC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEACX,WAAW,IAAI;;;;;;kEAElB,8OAAC;wDAAE,WAAU;kEACV,WAAW,WAAW;;;;;;kEAIzB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,kMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;kFAChB,8OAAC;wEAAK,WAAU;kFACb,WAAW,OAAO,EAAE,SAAS,QAAQ,MAAM;;;;;;kFAE9C,8OAAC;;4EAAK;4EAAE,WAAW,OAAO,EAAE,SAAS;4EAAE;;;;;;;;;;;;;0EAEzC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,oMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;kFACjB,8OAAC;;4EAAM,WAAW,eAAe,IAAI;4EAAG;;;;;;;;;;;;;0EAE1C,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,0MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;kFAClB,8OAAC;kFAAM,WAAW,QAAQ,EAAE,SAAS;;;;;;;;;;;;0EAEvC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,oMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;kFACjB,8OAAC;kFAAM,WAAW,KAAK;;;;;;;;;;;;;;;;;;kEAK3B,8OAAC;wDAAI,WAAU;kEACZ,WAAW,OAAO,EAAE,IAAI,CAAC,SAAiB,sBACzC,8OAAC;gEAEC,WAAU;0EAET;+DAHI;;;;;;;;;;;;;;;;0DAUb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAmC;;;;;;kEACjD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,8OAAC;wEAAK,WAAU;kFAAe,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,OAAO,EAAE,eAAe;;;;;;;;;;;;0EAEnF,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,8OAAC;wEAAK,WAAU;kFAAe,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,OAAO,EAAE,gBAAgB;;;;;;;;;;;;0EAEpF,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,8OAAC;wEAAK,WAAU;kFAAe,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,OAAO,EAAE,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWlG,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;;;;;;sEAEpB,8OAAC;4DACC,MAAK;4DACL,OAAO;4DACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4DAC9C,WAAU;4DACV,aAAY;;;;;;;;;;;;8DAKhB,8OAAC;oDACC,OAAO;oDACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;oDACnD,WAAU;;sEAEV,8OAAC;4DAAO,OAAM;sEAAM;;;;;;wDACnB,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC;gEAAsB,OAAO;0EAC3B,SAAS,MAAM,CAAC,GAAG,WAAW,KAAK,SAAS,KAAK,CAAC,GAAG,OAAO,CAAC,KAAK;+DADxD;;;;;;;;;;;;;;;;;;;;;;oCASpB,6BACC,8OAAC;wCAAI,WAAU;kDACZ;+CAAI,MAAM;yCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,sBACrB,8OAAC;gDAAgB,WAAU;;kEACzB,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;;;;;;;;;;;;+CALT;;;;;;;;;mFAUZ,cAAc,MAAM,KAAK,kBAC3B,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;oDAAE,WAAU;8DAAU;;;;;;8DACvB,8OAAC;oDAAE,WAAU;8DAAU;;;;;;;;;;;;;;;;iGAI3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,MAAM;wCACN,WAAU;kDAEV,cAAA,8OAAC,yLAAA,CAAA,kBAAe;sDACb,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC,8HAAA,CAAA,UAAQ;oDAEP,MAAM;oDACN,YAAY;mDAFP,KAAK,GAAG;;;;;;;;;;;;;;;;;;;;;0CAWzB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,iIAAA,CAAA,UAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQrB,YAAY,mBACX,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,SAAS;oBAAE,OAAO;gBAAE;gBACpB,SAAS;oBAAE,OAAO;gBAAE;gBACpB,YAAY;oBAAE,OAAO;gBAAK;gBAC1B,UAAU;oBAAE,OAAO;gBAAK;gBACxB,SAAS;gBACT,WAAU;;kCAEV,8OAAC,sNAAA,CAAA,eAAY;wBAAC,WAAU;;;;;;kCACxB,8OAAC;wBAAK,WAAU;kCAAe;;;;;;;;;;;;0BAKnC,8OAAC,iIAAA,CAAA,UAAW;;;;;0BAEZ,8OAAC,4HAAA,CAAA,UAAM;;;;;;;;;;;AAGb;uCAEe", "debugId": null}}]}