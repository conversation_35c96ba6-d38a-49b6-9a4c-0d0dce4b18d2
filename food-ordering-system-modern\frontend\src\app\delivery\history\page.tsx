'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { useQuery } from '@tanstack/react-query';
import { 
  Clock,
  Package,
  MapPin,
  DollarSign,
  Calendar,
  Filter,
  Search,
  CheckCircle,
  Star,
  Download
} from 'lucide-react';
import DeliverySidebar from '@/components/DeliverySidebar';
import { deliveryAPI } from '@/lib/api';

const DeliveryHistoryPage = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [dateFilter, setDateFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);

  // Fetch delivery history
  const { data: historyData, isLoading } = useQuery({
    queryKey: ['delivery-history', currentPage, dateFilter],
    queryFn: () => deliveryAPI.getHistory({ page: currentPage, limit: 10 }),
  });

  // Mock delivery history data
  const mockHistory = [
    {
      id: 'DEL001',
      orderNumber: '#12345',
      restaurant: {
        name: 'Spice Garden',
        address: '123 Restaurant St, Mumbai'
      },
      customer: {
        name: '<PERSON>',
        address: '456 Customer Ave, Mumbai'
      },
      amount: 450,
      deliveryFee: 40,
      distance: '2.5 km',
      deliveredAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      rating: 5,
      feedback: 'Great service, very fast delivery!',
      duration: '25 mins'
    },
    {
      id: 'DEL002',
      orderNumber: '#12346',
      restaurant: {
        name: 'Pizza Palace',
        address: '789 Pizza Rd, Mumbai'
      },
      customer: {
        name: 'Sarah Smith',
        address: '321 Home St, Mumbai'
      },
      amount: 680,
      deliveryFee: 50,
      distance: '1.8 km',
      deliveredAt: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(),
      rating: 4,
      feedback: 'Good delivery, food was still hot.',
      duration: '18 mins'
    },
    {
      id: 'DEL003',
      orderNumber: '#12347',
      restaurant: {
        name: 'Burger Hub',
        address: '555 Burger Blvd, Mumbai'
      },
      customer: {
        name: 'Mike Johnson',
        address: '999 Delivery Dr, Mumbai'
      },
      amount: 320,
      deliveryFee: 35,
      distance: '3.2 km',
      deliveredAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
      rating: 5,
      feedback: 'Excellent delivery partner!',
      duration: '30 mins'
    },
    {
      id: 'DEL004',
      orderNumber: '#12348',
      restaurant: {
        name: 'Sushi Express',
        address: '777 Sushi St, Mumbai'
      },
      customer: {
        name: 'Emily Davis',
        address: '111 Fresh Ave, Mumbai'
      },
      amount: 890,
      deliveryFee: 60,
      distance: '4.1 km',
      deliveredAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
      rating: 4,
      feedback: 'Professional and courteous.',
      duration: '35 mins'
    }
  ];

  const deliveries = historyData?.data || mockHistory;

  const filteredDeliveries = deliveries.filter(delivery => {
    const matchesSearch = !searchTerm || 
      delivery.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      delivery.customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      delivery.restaurant.name.toLowerCase().includes(searchTerm.toLowerCase());

    const deliveryDate = new Date(delivery.deliveredAt);
    const now = new Date();
    let matchesDate = true;

    if (dateFilter === 'today') {
      matchesDate = deliveryDate.toDateString() === now.toDateString();
    } else if (dateFilter === 'week') {
      const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      matchesDate = deliveryDate >= weekAgo;
    } else if (dateFilter === 'month') {
      const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      matchesDate = deliveryDate >= monthAgo;
    }

    return matchesSearch && matchesDate;
  });

  const totalEarnings = filteredDeliveries.reduce((sum, delivery) => sum + delivery.deliveryFee, 0);
  const averageRating = filteredDeliveries.reduce((sum, delivery) => sum + delivery.rating, 0) / filteredDeliveries.length || 0;

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Less than an hour ago';
    if (diffInHours < 24) return `${diffInHours} hours ago`;
    return `${Math.floor(diffInHours / 24)} days ago`;
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex">
        <DeliverySidebar />
        <div className="flex-1 p-8">
          <div className="max-w-6xl mx-auto">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
              <div className="space-y-4">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="h-32 bg-gray-200 rounded-lg"></div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex">
      <DeliverySidebar />
      
      <div className="flex-1 p-8">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                <Clock className="w-5 h-5 text-green-600" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Delivery History</h1>
                <p className="text-gray-600">View your completed deliveries and earnings</p>
              </div>
            </div>

            <button className="flex items-center gap-2 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors">
              <Download className="w-4 h-4" />
              Export Report
            </button>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white rounded-2xl p-6 shadow-md"
            >
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Package className="w-6 h-6 text-blue-600" />
                </div>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-1">{filteredDeliveries.length}</h3>
              <p className="text-gray-600 text-sm">Total Deliveries</p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="bg-white rounded-2xl p-6 shadow-md"
            >
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <DollarSign className="w-6 h-6 text-green-600" />
                </div>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-1">₹{totalEarnings}</h3>
              <p className="text-gray-600 text-sm">Total Earnings</p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-white rounded-2xl p-6 shadow-md"
            >
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                  <Star className="w-6 h-6 text-yellow-600" />
                </div>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-1">{averageRating.toFixed(1)}</h3>
              <p className="text-gray-600 text-sm">Average Rating</p>
            </motion.div>
          </div>

          {/* Filters */}
          <div className="bg-white rounded-2xl p-6 shadow-md mb-6">
            <div className="flex flex-col md:flex-row gap-4">
              {/* Search */}
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    type="text"
                    placeholder="Search by order number, customer, or restaurant..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  />
                </div>
              </div>

              {/* Date Filter */}
              <div className="flex items-center gap-2">
                <Calendar className="w-5 h-5 text-gray-400" />
                <select
                  value={dateFilter}
                  onChange={(e) => setDateFilter(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                >
                  <option value="all">All Time</option>
                  <option value="today">Today</option>
                  <option value="week">This Week</option>
                  <option value="month">This Month</option>
                </select>
              </div>
            </div>
          </div>

          {/* Delivery History List */}
          <div className="space-y-6">
            {filteredDeliveries.map((delivery, index) => (
              <motion.div
                key={delivery.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-white rounded-2xl p-6 shadow-md hover:shadow-lg transition-shadow duration-200"
              >
                {/* Delivery Header */}
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                      <CheckCircle className="w-6 h-6 text-green-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">{delivery.orderNumber}</h3>
                      <p className="text-sm text-gray-600">{formatTime(delivery.deliveredAt)}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-gray-900">₹{delivery.deliveryFee}</p>
                    <p className="text-sm text-gray-600">Delivery Fee</p>
                  </div>
                </div>

                {/* Delivery Details */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-4">
                  {/* Restaurant Info */}
                  <div className="space-y-3">
                    <h4 className="font-medium text-gray-900">Pickup from:</h4>
                    <div className="space-y-2">
                      <p className="font-medium text-gray-900">{delivery.restaurant.name}</p>
                      <div className="flex items-center gap-2">
                        <MapPin className="w-4 h-4 text-gray-400" />
                        <span className="text-sm text-gray-600">{delivery.restaurant.address}</span>
                      </div>
                    </div>
                  </div>

                  {/* Customer Info */}
                  <div className="space-y-3">
                    <h4 className="font-medium text-gray-900">Delivered to:</h4>
                    <div className="space-y-2">
                      <p className="font-medium text-gray-900">{delivery.customer.name}</p>
                      <div className="flex items-center gap-2">
                        <MapPin className="w-4 h-4 text-gray-400" />
                        <span className="text-sm text-gray-600">{delivery.customer.address}</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Delivery Stats */}
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-6">
                    <div className="flex items-center gap-2">
                      <Clock className="w-4 h-4 text-gray-400" />
                      <span className="text-sm text-gray-600">Duration: {delivery.duration}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <MapPin className="w-4 h-4 text-gray-400" />
                      <span className="text-sm text-gray-600">Distance: {delivery.distance}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <DollarSign className="w-4 h-4 text-gray-400" />
                      <span className="text-sm text-gray-600">Order Value: ₹{delivery.amount}</span>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-gray-600">{formatDate(delivery.deliveredAt)}</p>
                  </div>
                </div>

                {/* Rating and Feedback */}
                {delivery.rating && (
                  <div className="border-t pt-4">
                    <div className="flex items-start justify-between">
                      <div>
                        <h4 className="font-medium text-gray-900 mb-2">Customer Feedback</h4>
                        <p className="text-gray-600 text-sm">{delivery.feedback}</p>
                      </div>
                      <div className="flex items-center gap-1">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`w-4 h-4 ${
                              i < delivery.rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
                            }`}
                          />
                        ))}
                        <span className="ml-2 text-sm font-medium text-gray-900">{delivery.rating}.0</span>
                      </div>
                    </div>
                  </div>
                )}
              </motion.div>
            ))}
          </div>

          {filteredDeliveries.length === 0 && (
            <div className="text-center py-12">
              <Package className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No delivery history found</h3>
              <p className="text-gray-600">Try adjusting your search or filter criteria.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DeliveryHistoryPage;
