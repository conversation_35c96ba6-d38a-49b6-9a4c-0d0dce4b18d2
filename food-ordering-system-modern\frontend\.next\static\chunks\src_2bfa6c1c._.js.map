{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/project%20food-order%206k/food-ordering-system-modern/frontend/src/store/useCartStore.js"], "sourcesContent": ["import { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport { toast } from 'react-hot-toast';\n\nconst useCartStore = create(\n  persist(\n    (set, get) => ({\n      items: [],\n      restaurant: null,\n      isOpen: false,\n\n      // Add item to cart\n      addItem: (food, quantity = 1, variant = null, addons = [], customizations = []) => {\n        const state = get();\n        \n        // Check if item is from different restaurant\n        if (state.restaurant && state.restaurant._id !== food.restaurant._id) {\n          toast.error('You can only order from one restaurant at a time');\n          return false;\n        }\n\n        // Create item object\n        const newItem = {\n          id: `${food._id}-${variant?.name || 'default'}-${Date.now()}`,\n          food,\n          quantity,\n          variant,\n          addons: addons || [],\n          customizations: customizations || [],\n          price: variant?.price || food.price,\n          itemTotal: calculateItemTotal(food, quantity, variant, addons)\n        };\n\n        set({\n          items: [...state.items, newItem],\n          restaurant: food.restaurant,\n        });\n\n        toast.success(`${food.name} added to cart`);\n        return true;\n      },\n\n      // Update item quantity\n      updateQuantity: (itemId, quantity) => {\n        if (quantity <= 0) {\n          get().removeItem(itemId);\n          return;\n        }\n\n        set((state) => ({\n          items: state.items.map(item => \n            item.id === itemId \n              ? { \n                  ...item, \n                  quantity,\n                  itemTotal: calculateItemTotal(item.food, quantity, item.variant, item.addons)\n                }\n              : item\n          )\n        }));\n      },\n\n      // Remove item from cart\n      removeItem: (itemId) => {\n        set((state) => {\n          const newItems = state.items.filter(item => item.id !== itemId);\n          return {\n            items: newItems,\n            restaurant: newItems.length === 0 ? null : state.restaurant\n          };\n        });\n        toast.success('Item removed from cart');\n      },\n\n      // Clear entire cart\n      clearCart: () => {\n        set({ items: [], restaurant: null });\n        toast.success('Cart cleared');\n      },\n\n      // Toggle cart visibility\n      toggleCart: () => {\n        set((state) => ({ isOpen: !state.isOpen }));\n      },\n\n      // Open cart\n      openCart: () => {\n        set({ isOpen: true });\n      },\n\n      // Close cart\n      closeCart: () => {\n        set({ isOpen: false });\n      },\n\n      // Get cart totals\n      getTotals: () => {\n        const state = get();\n        const subtotal = state.items.reduce((sum, item) => sum + item.itemTotal, 0);\n        const deliveryFee = state.restaurant?.pricing?.deliveryFee || 0;\n        const packagingFee = state.restaurant?.pricing?.packagingFee || 0;\n        const taxes = subtotal * 0.18; // 18% GST\n        const total = subtotal + deliveryFee + packagingFee + taxes;\n\n        return {\n          subtotal,\n          deliveryFee,\n          packagingFee,\n          taxes,\n          total,\n          itemCount: state.items.reduce((sum, item) => sum + item.quantity, 0)\n        };\n      },\n\n      // Check if item exists in cart\n      isItemInCart: (foodId, variant = null) => {\n        const state = get();\n        return state.items.some(item => \n          item.food._id === foodId && \n          (variant ? item.variant?.name === variant.name : !item.variant)\n        );\n      },\n\n      // Get item from cart\n      getCartItem: (foodId, variant = null) => {\n        const state = get();\n        return state.items.find(item => \n          item.food._id === foodId && \n          (variant ? item.variant?.name === variant.name : !item.variant)\n        );\n      },\n    }),\n    {\n      name: 'cart-storage',\n      partialize: (state) => ({ \n        items: state.items, \n        restaurant: state.restaurant \n      }),\n    }\n  )\n);\n\n// Helper function to calculate item total\nconst calculateItemTotal = (food, quantity, variant, addons) => {\n  let basePrice = variant?.price || food.price;\n  \n  // Add addon prices\n  const addonTotal = addons?.reduce((sum, addon) => {\n    return sum + (addon.price * (addon.quantity || 1));\n  }, 0) || 0;\n\n  return (basePrice + addonTotal) * quantity;\n};\n\nexport default useCartStore;\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,MAAM,eAAe,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,EACxB,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,OAAO,EAAE;QACT,YAAY;QACZ,QAAQ;QAER,mBAAmB;QACnB,SAAS,SAAC;gBAAM,4EAAW,GAAG,2EAAU,MAAM,0EAAS,EAAE,EAAE,kFAAiB,EAAE;YAC5E,MAAM,QAAQ;YAEd,6CAA6C;YAC7C,IAAI,MAAM,UAAU,IAAI,MAAM,UAAU,CAAC,GAAG,KAAK,KAAK,UAAU,CAAC,GAAG,EAAE;gBACpE,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,OAAO;YACT;YAEA,qBAAqB;YACrB,MAAM,UAAU;gBACd,IAAI,AAAC,GAAc,OAAZ,KAAK,GAAG,EAAC,KAAiC,OAA9B,CAAA,oBAAA,8BAAA,QAAS,IAAI,KAAI,WAAU,KAAc,OAAX,KAAK,GAAG;gBACzD;gBACA;gBACA;gBACA,QAAQ,UAAU,EAAE;gBACpB,gBAAgB,kBAAkB,EAAE;gBACpC,OAAO,CAAA,oBAAA,8BAAA,QAAS,KAAK,KAAI,KAAK,KAAK;gBACnC,WAAW,mBAAmB,MAAM,UAAU,SAAS;YACzD;YAEA,IAAI;gBACF,OAAO;uBAAI,MAAM,KAAK;oBAAE;iBAAQ;gBAChC,YAAY,KAAK,UAAU;YAC7B;YAEA,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC,AAAC,GAAY,OAAV,KAAK,IAAI,EAAC;YAC3B,OAAO;QACT;QAEA,uBAAuB;QACvB,gBAAgB,CAAC,QAAQ;YACvB,IAAI,YAAY,GAAG;gBACjB,MAAM,UAAU,CAAC;gBACjB;YACF;YAEA,IAAI,CAAC,QAAU,CAAC;oBACd,OAAO,MAAM,KAAK,CAAC,GAAG,CAAC,CAAA,OACrB,KAAK,EAAE,KAAK,SACR;4BACE,GAAG,IAAI;4BACP;4BACA,WAAW,mBAAmB,KAAK,IAAI,EAAE,UAAU,KAAK,OAAO,EAAE,KAAK,MAAM;wBAC9E,IACA;gBAER,CAAC;QACH;QAEA,wBAAwB;QACxB,YAAY,CAAC;YACX,IAAI,CAAC;gBACH,MAAM,WAAW,MAAM,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;gBACxD,OAAO;oBACL,OAAO;oBACP,YAAY,SAAS,MAAM,KAAK,IAAI,OAAO,MAAM,UAAU;gBAC7D;YACF;YACA,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB;QAEA,oBAAoB;QACpB,WAAW;YACT,IAAI;gBAAE,OAAO,EAAE;gBAAE,YAAY;YAAK;YAClC,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB;QAEA,yBAAyB;QACzB,YAAY;YACV,IAAI,CAAC,QAAU,CAAC;oBAAE,QAAQ,CAAC,MAAM,MAAM;gBAAC,CAAC;QAC3C;QAEA,YAAY;QACZ,UAAU;YACR,IAAI;gBAAE,QAAQ;YAAK;QACrB;QAEA,aAAa;QACb,WAAW;YACT,IAAI;gBAAE,QAAQ;YAAM;QACtB;QAEA,kBAAkB;QAClB,WAAW;gBAGW,2BAAA,mBACC,4BAAA;YAHrB,MAAM,QAAQ;YACd,MAAM,WAAW,MAAM,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,SAAS,EAAE;YACzE,MAAM,cAAc,EAAA,oBAAA,MAAM,UAAU,cAAhB,yCAAA,4BAAA,kBAAkB,OAAO,cAAzB,gDAAA,0BAA2B,WAAW,KAAI;YAC9D,MAAM,eAAe,EAAA,qBAAA,MAAM,UAAU,cAAhB,0CAAA,6BAAA,mBAAkB,OAAO,cAAzB,iDAAA,2BAA2B,YAAY,KAAI;YAChE,MAAM,QAAQ,WAAW,MAAM,UAAU;YACzC,MAAM,QAAQ,WAAW,cAAc,eAAe;YAEtD,OAAO;gBACL;gBACA;gBACA;gBACA;gBACA;gBACA,WAAW,MAAM,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,QAAQ,EAAE;YACpE;QACF;QAEA,+BAA+B;QAC/B,cAAc,SAAC;gBAAQ,2EAAU;YAC/B,MAAM,QAAQ;YACd,OAAO,MAAM,KAAK,CAAC,IAAI,CAAC,CAAA;oBAEX;uBADX,KAAK,IAAI,CAAC,GAAG,KAAK,UAClB,CAAC,UAAU,EAAA,gBAAA,KAAK,OAAO,cAAZ,oCAAA,cAAc,IAAI,MAAK,QAAQ,IAAI,GAAG,CAAC,KAAK,OAAO;;QAElE;QAEA,qBAAqB;QACrB,aAAa,SAAC;gBAAQ,2EAAU;YAC9B,MAAM,QAAQ;YACd,OAAO,MAAM,KAAK,CAAC,IAAI,CAAC,CAAA;oBAEX;uBADX,KAAK,IAAI,CAAC,GAAG,KAAK,UAClB,CAAC,UAAU,EAAA,gBAAA,KAAK,OAAO,cAAZ,oCAAA,cAAc,IAAI,MAAK,QAAQ,IAAI,GAAG,CAAC,KAAK,OAAO;;QAElE;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,OAAO,MAAM,KAAK;YAClB,YAAY,MAAM,UAAU;QAC9B,CAAC;AACH;AAIJ,0CAA0C;AAC1C,MAAM,qBAAqB,CAAC,MAAM,UAAU,SAAS;IACnD,IAAI,YAAY,CAAA,oBAAA,8BAAA,QAAS,KAAK,KAAI,KAAK,KAAK;IAE5C,mBAAmB;IACnB,MAAM,aAAa,CAAA,mBAAA,6BAAA,OAAQ,MAAM,CAAC,CAAC,KAAK;QACtC,OAAO,MAAO,MAAM,KAAK,GAAG,CAAC,MAAM,QAAQ,IAAI,CAAC;IAClD,GAAG,OAAM;IAET,OAAO,CAAC,YAAY,UAAU,IAAI;AACpC;uCAEe", "debugId": null}}, {"offset": {"line": 163, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/project%20food-order%206k/food-ordering-system-modern/frontend/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  Menu,\n  X,\n  ShoppingCart,\n  User,\n  MapPin,\n  Search,\n  ChefHat,\n  LogOut,\n  Settings,\n  Heart,\n  Clock\n} from 'lucide-react';\nimport Link from 'next/link';\nimport { useRouter, usePathname } from 'next/navigation';\nimport useAuthStore from '@/store/useAuthStore';\nimport useCartStore from '@/store/useCartStore';\n\nconst Header = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);\n  const [isScrolled, setIsScrolled] = useState(false);\n\n  const router = useRouter();\n  const pathname = usePathname();\n  const { user, isAuthenticated, logout } = useAuthStore();\n  const { items, getTotals, toggleCart } = useCartStore();\n  const { itemCount } = getTotals();\n\n  // Hide header for vendor pages\n  if (pathname?.startsWith('/vendor')) {\n    return null;\n  }\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 10);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const handleLogout = async () => {\n    await logout();\n    setIsUserMenuOpen(false);\n    router.push('/');\n  };\n\n  const navItems = [\n    { name: 'Restaurants', href: '/restaurants' },\n    { name: 'Cuisines', href: '/cuisines' },\n    { name: 'Offers', href: '/offers' },\n    { name: 'About', href: '/about' },\n  ];\n\n  return (\n    <motion.header\n      initial={{ y: -100 }}\n      animate={{ y: 0 }}\n      transition={{ duration: 0.6, ease: \"easeOut\" }}\n      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n        isScrolled \n          ? 'bg-white/95 backdrop-blur-md shadow-lg' \n          : 'bg-transparent'\n      }`}\n    >\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16 lg:h-20\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center gap-2\">\n            <motion.div\n              whileHover={{ rotate: 360 }}\n              transition={{ duration: 0.6 }}\n              className=\"w-10 h-10 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center\"\n            >\n              <ChefHat className=\"w-6 h-6 text-white\" />\n            </motion.div>\n            <span className=\"text-2xl font-bold text-gray-900\">\n              FoodieExpress\n            </span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden lg:flex items-center space-x-8\">\n            {navItems.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className=\"text-gray-700 hover:text-orange-500 font-medium transition-colors duration-200 relative group\"\n              >\n                {item.name}\n                <span className=\"absolute -bottom-1 left-0 w-0 h-0.5 bg-orange-500 transition-all duration-300 group-hover:w-full\" />\n              </Link>\n            ))}\n          </nav>\n\n          {/* Right Side Actions */}\n          <div className=\"flex items-center gap-4\">\n            {/* Location */}\n            <motion.button\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              className=\"hidden sm:flex items-center gap-2 text-gray-700 hover:text-orange-500 transition-colors duration-200\"\n            >\n              <MapPin className=\"w-5 h-5\" />\n              <span className=\"text-sm font-medium\">Location</span>\n            </motion.button>\n\n            {/* Search */}\n            <motion.button\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              className=\"p-2 text-gray-700 hover:text-orange-500 transition-colors duration-200\"\n            >\n              <Search className=\"w-5 h-5\" />\n            </motion.button>\n\n            {/* Cart */}\n            <motion.button\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              onClick={toggleCart}\n              className=\"relative p-2 text-gray-700 hover:text-orange-500 transition-colors duration-200\"\n            >\n              <ShoppingCart className=\"w-5 h-5\" />\n              {itemCount > 0 && (\n                <motion.span\n                  initial={{ scale: 0 }}\n                  animate={{ scale: 1 }}\n                  className=\"absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-medium\"\n                >\n                  {itemCount}\n                </motion.span>\n              )}\n            </motion.button>\n\n            {/* User Menu */}\n            {isAuthenticated ? (\n              <div className=\"relative\">\n                <motion.button\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                  onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}\n                  className=\"flex items-center gap-2 p-2 rounded-full hover:bg-gray-100 transition-colors duration-200\"\n                >\n                  <div className=\"w-8 h-8 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center\">\n                    <span className=\"text-white text-sm font-medium\">\n                      {user?.name?.charAt(0).toUpperCase()}\n                    </span>\n                  </div>\n                </motion.button>\n\n                <AnimatePresence>\n                  {isUserMenuOpen && (\n                    <motion.div\n                      initial={{ opacity: 0, y: 10, scale: 0.95 }}\n                      animate={{ opacity: 1, y: 0, scale: 1 }}\n                      exit={{ opacity: 0, y: 10, scale: 0.95 }}\n                      transition={{ duration: 0.2 }}\n                      className=\"absolute right-0 mt-2 w-64 bg-white rounded-xl shadow-lg border border-gray-200 py-2\"\n                    >\n                      <div className=\"px-4 py-3 border-b border-gray-100\">\n                        <p className=\"text-sm font-medium text-gray-900\">\n                          {user?.name}\n                        </p>\n                        <p className=\"text-sm text-gray-500\">\n                          {user?.email}\n                        </p>\n                      </div>\n\n                      <div className=\"py-2\">\n                        <Link\n                          href=\"/profile\"\n                          className=\"flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50\"\n                          onClick={() => setIsUserMenuOpen(false)}\n                        >\n                          <User className=\"w-4 h-4\" />\n                          Profile\n                        </Link>\n                        <Link\n                          href=\"/orders\"\n                          className=\"flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50\"\n                          onClick={() => setIsUserMenuOpen(false)}\n                        >\n                          <Clock className=\"w-4 h-4\" />\n                          Orders\n                        </Link>\n                        <Link\n                          href=\"/favorites\"\n                          className=\"flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50\"\n                          onClick={() => setIsUserMenuOpen(false)}\n                        >\n                          <Heart className=\"w-4 h-4\" />\n                          Favorites\n                        </Link>\n                        <Link\n                          href=\"/settings\"\n                          className=\"flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50\"\n                          onClick={() => setIsUserMenuOpen(false)}\n                        >\n                          <Settings className=\"w-4 h-4\" />\n                          Settings\n                        </Link>\n                      </div>\n\n                      <div className=\"border-t border-gray-100 pt-2\">\n                        <button\n                          onClick={handleLogout}\n                          className=\"flex items-center gap-3 px-4 py-2 text-sm text-red-600 hover:bg-red-50 w-full text-left\"\n                        >\n                          <LogOut className=\"w-4 h-4\" />\n                          Sign Out\n                        </button>\n                      </div>\n                    </motion.div>\n                  )}\n                </AnimatePresence>\n              </div>\n            ) : (\n              <div className=\"flex items-center gap-2\">\n                <Link href=\"/auth/login\">\n                  <motion.button\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    className=\"px-4 py-2 text-gray-700 hover:text-orange-500 font-medium transition-colors duration-200\"\n                  >\n                    Sign In\n                  </motion.button>\n                </Link>\n                <Link href=\"/auth/register\">\n                  <motion.button\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    className=\"px-4 py-2 bg-gradient-to-r from-orange-500 to-red-500 text-white rounded-full font-medium shadow-md hover:shadow-lg transition-all duration-200\"\n                  >\n                    Sign Up\n                  </motion.button>\n                </Link>\n              </div>\n            )}\n\n            {/* Mobile Menu Button */}\n            <motion.button\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"lg:hidden p-2 text-gray-700 hover:text-orange-500 transition-colors duration-200\"\n            >\n              {isMenuOpen ? (\n                <X className=\"w-6 h-6\" />\n              ) : (\n                <Menu className=\"w-6 h-6\" />\n              )}\n            </motion.button>\n          </div>\n        </div>\n\n        {/* Mobile Menu */}\n        <AnimatePresence>\n          {isMenuOpen && (\n            <motion.div\n              initial={{ opacity: 0, height: 0 }}\n              animate={{ opacity: 1, height: 'auto' }}\n              exit={{ opacity: 0, height: 0 }}\n              transition={{ duration: 0.3 }}\n              className=\"lg:hidden border-t border-gray-200 bg-white\"\n            >\n              <div className=\"py-4 space-y-2\">\n                {navItems.map((item) => (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className=\"block px-4 py-2 text-gray-700 hover:text-orange-500 hover:bg-gray-50 transition-colors duration-200\"\n                    onClick={() => setIsMenuOpen(false)}\n                  >\n                    {item.name}\n                  </Link>\n                ))}\n              </div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </div>\n    </motion.header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AACA;AACA;AACA;;;AApBA;;;;;;;;AAsBA,MAAM,SAAS;QAkIQ;;IAjIrB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAY,AAAD;IACrD,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAY,AAAD;IACpD,MAAM,EAAE,SAAS,EAAE,GAAG;IAEtB,+BAA+B;IAC/B,IAAI,qBAAA,+BAAA,SAAU,UAAU,CAAC,YAAY;QACnC,OAAO;IACT;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM;iDAAe;oBACnB,cAAc,OAAO,OAAO,GAAG;gBACjC;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;oCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;2BAAG,EAAE;IAEL,MAAM,eAAe;QACnB,MAAM;QACN,kBAAkB;QAClB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,WAAW;QACf;YAAE,MAAM;YAAe,MAAM;QAAe;QAC5C;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAU,MAAM;QAAU;QAClC;YAAE,MAAM;YAAS,MAAM;QAAS;KACjC;IAED,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,SAAS;YAAE,GAAG,CAAC;QAAI;QACnB,SAAS;YAAE,GAAG;QAAE;QAChB,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;QAC7C,WAAW,AAAC,+DAIX,OAHC,aACI,2CACA;kBAGN,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,YAAY;wCAAE,QAAQ;oCAAI;oCAC1B,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,WAAU;8CAEV,cAAA,6LAAC,+MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;;;;;;8CAErB,6LAAC;oCAAK,WAAU;8CAAmC;;;;;;;;;;;;sCAMrD,6LAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;;wCAET,KAAK,IAAI;sDACV,6LAAC;4CAAK,WAAU;;;;;;;mCALX,KAAK,IAAI;;;;;;;;;;sCAWpB,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAU;;sDAEV,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;4CAAK,WAAU;sDAAsB;;;;;;;;;;;;8CAIxC,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAU;8CAEV,cAAA,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;8CAIpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,SAAS;oCACT,WAAU;;sDAEV,6LAAC,yNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;wCACvB,YAAY,mBACX,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;4CACV,SAAS;gDAAE,OAAO;4CAAE;4CACpB,SAAS;gDAAE,OAAO;4CAAE;4CACpB,WAAU;sDAET;;;;;;;;;;;;gCAMN,gCACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4CACZ,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,UAAU;gDAAE,OAAO;4CAAK;4CACxB,SAAS,IAAM,kBAAkB,CAAC;4CAClC,WAAU;sDAEV,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DACb,iBAAA,4BAAA,aAAA,KAAM,IAAI,cAAV,iCAAA,WAAY,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;;;;;;sDAKxC,6LAAC,4LAAA,CAAA,kBAAe;sDACb,gCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,GAAG;oDAAI,OAAO;gDAAK;gDAC1C,SAAS;oDAAE,SAAS;oDAAG,GAAG;oDAAG,OAAO;gDAAE;gDACtC,MAAM;oDAAE,SAAS;oDAAG,GAAG;oDAAI,OAAO;gDAAK;gDACvC,YAAY;oDAAE,UAAU;gDAAI;gDAC5B,WAAU;;kEAEV,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EACV,iBAAA,2BAAA,KAAM,IAAI;;;;;;0EAEb,6LAAC;gEAAE,WAAU;0EACV,iBAAA,2BAAA,KAAM,KAAK;;;;;;;;;;;;kEAIhB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,+JAAA,CAAA,UAAI;gEACH,MAAK;gEACL,WAAU;gEACV,SAAS,IAAM,kBAAkB;;kFAEjC,6LAAC,qMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAY;;;;;;;0EAG9B,6LAAC,+JAAA,CAAA,UAAI;gEACH,MAAK;gEACL,WAAU;gEACV,SAAS,IAAM,kBAAkB;;kFAEjC,6LAAC,uMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;oEAAY;;;;;;;0EAG/B,6LAAC,+JAAA,CAAA,UAAI;gEACH,MAAK;gEACL,WAAU;gEACV,SAAS,IAAM,kBAAkB;;kFAEjC,6LAAC,uMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;oEAAY;;;;;;;0EAG/B,6LAAC,+JAAA,CAAA,UAAI;gEACH,MAAK;gEACL,WAAU;gEACV,SAAS,IAAM,kBAAkB;;kFAEjC,6LAAC,6MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;oEAAY;;;;;;;;;;;;;kEAKpC,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DACC,SAAS;4DACT,WAAU;;8EAEV,6LAAC,6MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;6FAS1C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gDACZ,YAAY;oDAAE,OAAO;gDAAK;gDAC1B,UAAU;oDAAE,OAAO;gDAAK;gDACxB,WAAU;0DACX;;;;;;;;;;;sDAIH,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gDACZ,YAAY;oDAAE,OAAO;gDAAK;gDAC1B,UAAU;oDAAE,OAAO;gDAAK;gDACxB,WAAU;0DACX;;;;;;;;;;;;;;;;;8CAQP,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,SAAS,IAAM,cAAc,CAAC;oCAC9B,WAAU;8CAET,2BACC,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;iGAEb,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8BAOxB,6LAAC,4LAAA,CAAA,kBAAe;8BACb,4BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,QAAQ;wBAAE;wBACjC,SAAS;4BAAE,SAAS;4BAAG,QAAQ;wBAAO;wBACtC,MAAM;4BAAE,SAAS;4BAAG,QAAQ;wBAAE;wBAC9B,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,SAAS,IAAM,cAAc;8CAE5B,KAAK,IAAI;mCALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAelC;GA5QM;;QAKW,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;QACc,+HAAA,CAAA,UAAY;QACb,+HAAA,CAAA,UAAY;;;KARjD;uCA8QS", "debugId": null}}, {"offset": {"line": 790, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/project%20food-order%206k/food-ordering-system-modern/frontend/src/components/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { \n  ChefHat, \n  Mail, \n  Phone, \n  MapPin, \n  Facebook, \n  Twitter, \n  Instagram, \n  Youtube,\n  ArrowRight\n} from 'lucide-react';\nimport Link from 'next/link';\n\nconst Footer = () => {\n  const footerLinks = {\n    company: [\n      { name: 'About Us', href: '/about' },\n      { name: 'Careers', href: '/careers' },\n      { name: 'Press', href: '/press' },\n      { name: 'Blog', href: '/blog' },\n    ],\n    support: [\n      { name: 'Help Center', href: '/help' },\n      { name: 'Contact Us', href: '/contact' },\n      { name: 'Safety', href: '/safety' },\n      { name: 'Terms of Service', href: '/terms' },\n    ],\n    partner: [\n      { name: 'Become a Partner', href: '/partner' },\n      { name: 'Restaurant Dashboard', href: '/restaurant-dashboard' },\n      { name: 'Delivery Partner', href: '/delivery-partner' },\n      { name: 'API Documentation', href: '/api-docs' },\n    ],\n    admin: [\n      { name: 'Admin Login', href: '/auth/login?role=admin' },\n      { name: 'Delivery Partner Login', href: '/auth/login?role=delivery' },\n    ],\n  };\n\n  const socialLinks = [\n    { icon: Facebook, href: '#', label: 'Facebook' },\n    { icon: Twitter, href: '#', label: 'Twitter' },\n    { icon: Instagram, href: '#', label: 'Instagram' },\n    { icon: Youtube, href: '#', label: 'YouTube' },\n  ];\n\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      {/* Newsletter Section */}\n      <div className=\"border-b border-gray-800\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"text-center\"\n          >\n            <h3 className=\"text-2xl font-bold mb-4\">\n              Stay Updated with FoodieExpress\n            </h3>\n            <p className=\"text-gray-400 mb-8 max-w-2xl mx-auto\">\n              Get the latest updates on new restaurants, exclusive offers, and delicious deals delivered straight to your inbox.\n            </p>\n            \n            <div className=\"flex flex-col sm:flex-row gap-4 max-w-md mx-auto\">\n              <input\n                type=\"email\"\n                placeholder=\"Enter your email\"\n                className=\"flex-1 px-4 py-3 rounded-lg bg-gray-800 border border-gray-700 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent\"\n              />\n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"px-6 py-3 bg-gradient-to-r from-orange-500 to-red-500 text-white rounded-lg font-medium hover:shadow-lg transition-all duration-200 flex items-center gap-2 justify-center\"\n              >\n                Subscribe\n                <ArrowRight className=\"w-4 h-4\" />\n              </motion.button>\n            </div>\n          </motion.div>\n        </div>\n      </div>\n\n      {/* Main Footer Content */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8\">\n          {/* Brand Section */}\n          <div className=\"lg:col-span-2\">\n            <Link href=\"/\" className=\"flex items-center gap-2 mb-6\">\n              <div className=\"w-10 h-10 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center\">\n                <ChefHat className=\"w-6 h-6 text-white\" />\n              </div>\n              <span className=\"text-2xl font-bold\">FoodieExpress</span>\n            </Link>\n            \n            <p className=\"text-gray-400 mb-6 max-w-md\">\n              Delivering happiness one meal at a time. Order from your favorite restaurants and enjoy fast, reliable delivery service.\n            </p>\n\n            {/* Contact Info */}\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center gap-3 text-gray-400\">\n                <Phone className=\"w-5 h-5\" />\n                <span>+91 98765 43210</span>\n              </div>\n              <div className=\"flex items-center gap-3 text-gray-400\">\n                <Mail className=\"w-5 h-5\" />\n                <span><EMAIL></span>\n              </div>\n              <div className=\"flex items-center gap-3 text-gray-400\">\n                <MapPin className=\"w-5 h-5\" />\n                <span>Mumbai, Maharashtra, India</span>\n              </div>\n            </div>\n          </div>\n\n          {/* Company Links */}\n          <div>\n            <h4 className=\"text-lg font-semibold mb-6\">Company</h4>\n            <ul className=\"space-y-3\">\n              {footerLinks.company.map((link) => (\n                <li key={link.name}>\n                  <Link\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-white transition-colors duration-200\"\n                  >\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Support Links */}\n          <div>\n            <h4 className=\"text-lg font-semibold mb-6\">Support</h4>\n            <ul className=\"space-y-3\">\n              {footerLinks.support.map((link) => (\n                <li key={link.name}>\n                  <Link\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-white transition-colors duration-200\"\n                  >\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Partner Links */}\n          <div>\n            <h4 className=\"text-lg font-semibold mb-6\">Partner With Us</h4>\n            <ul className=\"space-y-3\">\n              {footerLinks.partner.map((link) => (\n                <li key={link.name}>\n                  <Link\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-white transition-colors duration-200\"\n                  >\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Admin Links */}\n          <div>\n            <h4 className=\"text-lg font-semibold mb-6\">Admin Access</h4>\n            <ul className=\"space-y-3\">\n              {footerLinks.admin.map((link) => (\n                <li key={link.name}>\n                  <Link\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-orange-400 transition-colors duration-200\"\n                  >\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n\n          </div>\n        </div>\n      </div>\n\n      {/* Bottom Section */}\n      <div className=\"border-t border-gray-800\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n          <div className=\"flex flex-col md:flex-row items-center justify-between gap-4\">\n            {/* Copyright */}\n            <p className=\"text-gray-400 text-sm\">\n              © 2024 FoodieExpress. All rights reserved.\n            </p>\n\n            {/* Social Links */}\n            <div className=\"flex items-center gap-4\">\n              {socialLinks.map((social) => (\n                <motion.a\n                  key={social.label}\n                  href={social.href}\n                  whileHover={{ scale: 1.1 }}\n                  whileTap={{ scale: 0.9 }}\n                  className=\"w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center text-gray-400 hover:text-white hover:bg-orange-500 transition-all duration-200\"\n                  aria-label={social.label}\n                >\n                  <social.icon className=\"w-5 h-5\" />\n                </motion.a>\n              ))}\n            </div>\n\n            {/* Legal Links */}\n            <div className=\"flex items-center gap-6 text-sm\">\n              <Link\n                href=\"/privacy\"\n                className=\"text-gray-400 hover:text-white transition-colors duration-200\"\n              >\n                Privacy Policy\n              </Link>\n              <Link\n                href=\"/cookies\"\n                className=\"text-gray-400 hover:text-white transition-colors duration-200\"\n              >\n                Cookie Policy\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AAdA;;;;;AAgBA,MAAM,SAAS;IACb,MAAM,cAAc;QAClB,SAAS;YACP;gBAAE,MAAM;gBAAY,MAAM;YAAS;YACnC;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAS,MAAM;YAAS;YAChC;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;SAC/B;QACD,SAAS;YACP;gBAAE,MAAM;gBAAe,MAAM;YAAQ;YACrC;gBAAE,MAAM;gBAAc,MAAM;YAAW;YACvC;gBAAE,MAAM;gBAAU,MAAM;YAAU;YAClC;gBAAE,MAAM;gBAAoB,MAAM;YAAS;SAC5C;QACD,SAAS;YACP;gBAAE,MAAM;gBAAoB,MAAM;YAAW;YAC7C;gBAAE,MAAM;gBAAwB,MAAM;YAAwB;YAC9D;gBAAE,MAAM;gBAAoB,MAAM;YAAoB;YACtD;gBAAE,MAAM;gBAAqB,MAAM;YAAY;SAChD;QACD,OAAO;YACL;gBAAE,MAAM;gBAAe,MAAM;YAAyB;YACtD;gBAAE,MAAM;gBAA0B,MAAM;YAA4B;SACrE;IACH;IAEA,MAAM,cAAc;QAClB;YAAE,MAAM,6MAAA,CAAA,WAAQ;YAAE,MAAM;YAAK,OAAO;QAAW;QAC/C;YAAE,MAAM,2MAAA,CAAA,UAAO;YAAE,MAAM;YAAK,OAAO;QAAU;QAC7C;YAAE,MAAM,+MAAA,CAAA,YAAS;YAAE,MAAM;YAAK,OAAO;QAAY;QACjD;YAAE,MAAM,2MAAA,CAAA,UAAO;YAAE,MAAM;YAAK,OAAO;QAAU;KAC9C;IAED,qBACE,6LAAC;QAAO,WAAU;;0BAEhB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,6LAAC;gCAAG,WAAU;0CAA0B;;;;;;0CAGxC,6LAAC;gCAAE,WAAU;0CAAuC;;;;;;0CAIpD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;kDAEZ,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,WAAU;;4CACX;0DAEC,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;;sDACvB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,+MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;sDAErB,6LAAC;4CAAK,WAAU;sDAAqB;;;;;;;;;;;;8CAGvC,6LAAC;oCAAE,WAAU;8CAA8B;;;;;;8CAK3C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC;8DAAK;;;;;;;;;;;;sDAER,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;8DAAK;;;;;;;;;;;;sDAER,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;sCAMZ,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAG,WAAU;8CACX,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,qBACtB,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAgB5B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;0CAKrC,6LAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAC,uBAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;wCAEP,MAAM,OAAO,IAAI;wCACjB,YAAY;4CAAE,OAAO;wCAAI;wCACzB,UAAU;4CAAE,OAAO;wCAAI;wCACvB,WAAU;wCACV,cAAY,OAAO,KAAK;kDAExB,cAAA,6LAAC,OAAO,IAAI;4CAAC,WAAU;;;;;;uCAPlB,OAAO,KAAK;;;;;;;;;;0CAavB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;KA5NM;uCA8NS", "debugId": null}}, {"offset": {"line": 1398, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/project%20food-order%206k/food-ordering-system-modern/frontend/src/components/LandingPage.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { useState, useEffect } from 'react';\nimport { \n  ChefHat, \n  Clock, \n  MapPin, \n  Star, \n  Truck, \n  Shield, \n  Smartphone,\n  Heart,\n  ArrowRight,\n  Play\n} from 'lucide-react';\nimport Image from 'next/image';\nimport Link from 'next/link';\nimport Header from './Header';\nimport Footer from './Footer';\n\nconst LandingPage = () => {\n  const [isVisible, setIsVisible] = useState(false);\n\n  useEffect(() => {\n    setIsVisible(true);\n  }, []);\n\n  const fadeInUp = {\n    initial: { opacity: 0, y: 60 },\n    animate: { opacity: 1, y: 0 },\n    transition: { duration: 0.6, ease: \"easeOut\" }\n  };\n\n  const staggerContainer = {\n    animate: {\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n\n  const features = [\n    {\n      icon: Clock,\n      title: \"Fast Delivery\",\n      description: \"Get your food delivered in 30 minutes or less\"\n    },\n    {\n      icon: Shield,\n      title: \"Safe & Secure\",\n      description: \"100% secure payments and contactless delivery\"\n    },\n    {\n      icon: ChefHat,\n      title: \"Quality Food\",\n      description: \"Fresh ingredients from the best restaurants\"\n    },\n    {\n      icon: Smartphone,\n      title: \"Easy Ordering\",\n      description: \"Order with just a few taps on your phone\"\n    }\n  ];\n\n  const stats = [\n    { number: \"10K+\", label: \"Happy Customers\" },\n    { number: \"500+\", label: \"Partner Restaurants\" },\n    { number: \"50K+\", label: \"Orders Delivered\" },\n    { number: \"4.8\", label: \"Average Rating\" }\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-orange-50 via-white to-red-50\">\n      <Header />\n      \n      {/* Hero Section */}\n      <section className=\"relative overflow-hidden pt-20 pb-16 lg:pt-32 lg:pb-24\">\n        <div className=\"absolute inset-0 bg-gradient-to-r from-orange-400/10 to-red-400/10\" />\n        <div className=\"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n            {/* Left Content */}\n            <motion.div\n              initial={{ opacity: 0, x: -50 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.8, ease: \"easeOut\" }}\n              className=\"text-center lg:text-left\"\n            >\n              <motion.h1 \n                className=\"text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight\"\n                initial={{ opacity: 0, y: 30 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.8, delay: 0.2 }}\n              >\n                Delicious Food\n                <span className=\"block text-transparent bg-clip-text bg-gradient-to-r from-orange-500 to-red-500\">\n                  Delivered Fast\n                </span>\n              </motion.h1>\n              \n              <motion.p \n                className=\"mt-6 text-xl text-gray-600 max-w-2xl\"\n                initial={{ opacity: 0, y: 30 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.8, delay: 0.4 }}\n              >\n                Order your favorite meals from the best restaurants near you. \n                Fast delivery, great taste, amazing experience.\n              </motion.p>\n\n              <motion.div \n                className=\"mt-8 flex flex-col sm:flex-row gap-4 justify-center lg:justify-start\"\n                initial={{ opacity: 0, y: 30 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.8, delay: 0.6 }}\n              >\n                <Link href=\"/restaurants\">\n                  <motion.button\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    className=\"bg-gradient-to-r from-orange-500 to-red-500 text-white px-8 py-4 rounded-full font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 flex items-center gap-2\"\n                  >\n                    Order Now\n                    <ArrowRight className=\"w-5 h-5\" />\n                  </motion.button>\n                </Link>\n                \n                <motion.button\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                  className=\"border-2 border-gray-300 text-gray-700 px-8 py-4 rounded-full font-semibold text-lg hover:border-orange-500 hover:text-orange-500 transition-all duration-300 flex items-center gap-2\"\n                >\n                  <Play className=\"w-5 h-5\" />\n                  Watch Demo\n                </motion.button>\n              </motion.div>\n\n              {/* Quick Stats */}\n              <motion.div \n                className=\"mt-12 grid grid-cols-2 sm:grid-cols-4 gap-6\"\n                variants={staggerContainer}\n                initial=\"initial\"\n                animate=\"animate\"\n              >\n                {stats.map((stat, index) => (\n                  <motion.div\n                    key={index}\n                    variants={fadeInUp}\n                    className=\"text-center\"\n                  >\n                    <div className=\"text-2xl sm:text-3xl font-bold text-gray-900\">\n                      {stat.number}\n                    </div>\n                    <div className=\"text-sm text-gray-600 mt-1\">\n                      {stat.label}\n                    </div>\n                  </motion.div>\n                ))}\n              </motion.div>\n            </motion.div>\n\n            {/* Right Content - Hero Image */}\n            <motion.div\n              initial={{ opacity: 0, x: 50 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.8, delay: 0.3 }}\n              className=\"relative\"\n            >\n              <div className=\"relative z-10\">\n                <motion.div\n                  animate={{ \n                    y: [0, -10, 0],\n                    rotate: [0, 1, 0]\n                  }}\n                  transition={{ \n                    duration: 6,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  }}\n                  className=\"bg-white rounded-3xl shadow-2xl p-8 max-w-md mx-auto\"\n                >\n                  <div className=\"aspect-square bg-gradient-to-br from-orange-100 to-red-100 rounded-2xl flex items-center justify-center\">\n                    <ChefHat className=\"w-32 h-32 text-orange-500\" />\n                  </div>\n                  \n                  <div className=\"mt-6\">\n                    <h3 className=\"text-xl font-bold text-gray-900\">\n                      Margherita Pizza\n                    </h3>\n                    <p className=\"text-gray-600 mt-2\">\n                      Fresh tomatoes, mozzarella, basil\n                    </p>\n                    <div className=\"flex items-center justify-between mt-4\">\n                      <span className=\"text-2xl font-bold text-orange-500\">\n                        ₹299\n                      </span>\n                      <div className=\"flex items-center gap-1\">\n                        <Star className=\"w-4 h-4 text-yellow-400 fill-current\" />\n                        <span className=\"text-sm font-medium\">4.8</span>\n                      </div>\n                    </div>\n                  </div>\n                </motion.div>\n              </div>\n\n              {/* Floating Elements */}\n              <motion.div\n                animate={{ \n                  y: [0, -15, 0],\n                  x: [0, 5, 0]\n                }}\n                transition={{ \n                  duration: 4,\n                  repeat: Infinity,\n                  ease: \"easeInOut\"\n                }}\n                className=\"absolute -top-4 -left-4 bg-white rounded-full p-4 shadow-lg\"\n              >\n                <Heart className=\"w-6 h-6 text-red-500\" />\n              </motion.div>\n\n              <motion.div\n                animate={{ \n                  y: [0, 10, 0],\n                  x: [0, -5, 0]\n                }}\n                transition={{ \n                  duration: 5,\n                  repeat: Infinity,\n                  ease: \"easeInOut\"\n                }}\n                className=\"absolute -bottom-4 -right-4 bg-white rounded-full p-4 shadow-lg\"\n              >\n                <Truck className=\"w-6 h-6 text-green-500\" />\n              </motion.div>\n            </motion.div>\n          </div>\n        </div>\n      </section>\n\n      {/* Features Section */}\n      <section className=\"py-16 lg:py-24 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"text-center mb-16\"\n          >\n            <h2 className=\"text-3xl sm:text-4xl font-bold text-gray-900 mb-4\">\n              Why Choose FoodieExpress?\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n              We make food ordering simple, fast, and delightful with our amazing features\n            </p>\n          </motion.div>\n\n          <motion.div\n            variants={staggerContainer}\n            initial=\"initial\"\n            whileInView=\"animate\"\n            viewport={{ once: true }}\n            className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-8\"\n          >\n            {features.map((feature, index) => (\n              <motion.div\n                key={index}\n                variants={fadeInUp}\n                whileHover={{ y: -5 }}\n                className=\"text-center p-6 rounded-2xl hover:shadow-lg transition-all duration-300\"\n              >\n                <div className=\"w-16 h-16 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <feature.icon className=\"w-8 h-8 text-white\" />\n                </div>\n                <h3 className=\"text-xl font-bold text-gray-900 mb-2\">\n                  {feature.title}\n                </h3>\n                <p className=\"text-gray-600\">\n                  {feature.description}\n                </p>\n              </motion.div>\n            ))}\n          </motion.div>\n        </div>\n      </section>\n\n      <Footer />\n    </div>\n  );\n};\n\nexport default LandingPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AACA;AACA;;;AAnBA;;;;;;;AAqBA,MAAM,cAAc;;IAClB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,aAAa;QACf;gCAAG,EAAE;IAEL,MAAM,WAAW;QACf,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;IAC/C;IAEA,MAAM,mBAAmB;QACvB,SAAS;YACP,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,WAAW;QACf;YACE,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,yMAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,+MAAA,CAAA,UAAO;YACb,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,iNAAA,CAAA,aAAU;YAChB,OAAO;YACP,aAAa;QACf;KACD;IAED,MAAM,QAAQ;QACZ;YAAE,QAAQ;YAAQ,OAAO;QAAkB;QAC3C;YAAE,QAAQ;YAAQ,OAAO;QAAsB;QAC/C;YAAE,QAAQ;YAAQ,OAAO;QAAmB;QAC5C;YAAE,QAAQ;YAAO,OAAO;QAAiB;KAC1C;IAED,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,+HAAA,CAAA,UAAM;;;;;0BAGP,6LAAC;gBAAQ,WAAU;;kCACjB,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,MAAM;oCAAU;oCAC7C,WAAU;;sDAEV,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4CACR,WAAU;4CACV,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,YAAY;gDAAE,UAAU;gDAAK,OAAO;4CAAI;;gDACzC;8DAEC,6LAAC;oDAAK,WAAU;8DAAkF;;;;;;;;;;;;sDAKpG,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;4CACP,WAAU;4CACV,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,YAAY;gDAAE,UAAU;gDAAK,OAAO;4CAAI;sDACzC;;;;;;sDAKD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,YAAY;gDAAE,UAAU;gDAAK,OAAO;4CAAI;;8DAExC,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;8DACT,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wDACZ,YAAY;4DAAE,OAAO;wDAAK;wDAC1B,UAAU;4DAAE,OAAO;wDAAK;wDACxB,WAAU;;4DACX;0EAEC,6LAAC,qNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;;;;;;;;;;;;8DAI1B,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oDACZ,YAAY;wDAAE,OAAO;oDAAK;oDAC1B,UAAU;wDAAE,OAAO;oDAAK;oDACxB,WAAU;;sEAEV,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAY;;;;;;;;;;;;;sDAMhC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,UAAU;4CACV,SAAQ;4CACR,SAAQ;sDAEP,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oDAET,UAAU;oDACV,WAAU;;sEAEV,6LAAC;4DAAI,WAAU;sEACZ,KAAK,MAAM;;;;;;sEAEd,6LAAC;4DAAI,WAAU;sEACZ,KAAK,KAAK;;;;;;;mDARR;;;;;;;;;;;;;;;;8CAgBb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDACP,GAAG;wDAAC;wDAAG,CAAC;wDAAI;qDAAE;oDACd,QAAQ;wDAAC;wDAAG;wDAAG;qDAAE;gDACnB;gDACA,YAAY;oDACV,UAAU;oDACV,QAAQ;oDACR,MAAM;gDACR;gDACA,WAAU;;kEAEV,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,+MAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;;;;;;kEAGrB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EAAkC;;;;;;0EAGhD,6LAAC;gEAAE,WAAU;0EAAqB;;;;;;0EAGlC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAqC;;;;;;kFAGrD,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,qMAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;0FAChB,6LAAC;gFAAK,WAAU;0FAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAQhD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDACP,GAAG;oDAAC;oDAAG,CAAC;oDAAI;iDAAE;gDACd,GAAG;oDAAC;oDAAG;oDAAG;iDAAE;4CACd;4CACA,YAAY;gDACV,UAAU;gDACV,QAAQ;gDACR,MAAM;4CACR;4CACA,WAAU;sDAEV,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAGnB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDACP,GAAG;oDAAC;oDAAG;oDAAI;iDAAE;gDACb,GAAG;oDAAC;oDAAG,CAAC;oDAAG;iDAAE;4CACf;4CACA,YAAY;gDACV,UAAU;gDACV,QAAQ;gDACR,MAAM;4CACR;4CACA,WAAU;sDAEV,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ3B,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,6LAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,6LAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAKzD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU;4BACV,SAAQ;4BACR,aAAY;4BACZ,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;sCAET,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,UAAU;oCACV,YAAY;wCAAE,GAAG,CAAC;oCAAE;oCACpB,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,QAAQ,IAAI;gDAAC,WAAU;;;;;;;;;;;sDAE1B,6LAAC;4CAAG,WAAU;sDACX,QAAQ,KAAK;;;;;;sDAEhB,6LAAC;4CAAE,WAAU;sDACV,QAAQ,WAAW;;;;;;;mCAZjB;;;;;;;;;;;;;;;;;;;;;0BAoBf,6LAAC,+HAAA,CAAA,UAAM;;;;;;;;;;;AAGb;GA7QM;KAAA;uCA+QS", "debugId": null}}]}