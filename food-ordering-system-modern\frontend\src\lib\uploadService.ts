import { toast } from 'react-hot-toast';

export interface UploadResponse {
  success: boolean;
  data?: {
    url: string;
    filename: string;
    originalName: string;
    size: number;
  };
  message?: string;
}

export interface MultipleUploadResponse {
  success: boolean;
  data?: Array<{
    url: string;
    filename: string;
    originalName: string;
    size: number;
  }>;
  message?: string;
}

class UploadService {
  private baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000';

  async uploadSingleImage(file: File, type: string = 'general'): Promise<string | null> {
    try {
      const formData = new FormData();
      formData.append('image', file);
      formData.append('type', type);

      const response = await fetch(`${this.baseUrl}/api/upload/image`, {
        method: 'POST',
        body: formData,
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`Upload failed: ${response.statusText}`);
      }

      const result: UploadResponse = await response.json();
      
      if (result.success && result.data) {
        return result.data.url;
      } else {
        throw new Error(result.message || 'Upload failed');
      }
    } catch (error) {
      console.error('Upload error:', error);
      toast.error('Failed to upload image');
      return null;
    }
  }

  async uploadMultipleImages(files: File[], type: string = 'general'): Promise<string[]> {
    try {
      const uploadPromises = files.map(file => this.uploadSingleImage(file, type));
      const results = await Promise.all(uploadPromises);
      
      // Filter out null results
      return results.filter((url): url is string => url !== null);
    } catch (error) {
      console.error('Multiple upload error:', error);
      toast.error('Failed to upload images');
      return [];
    }
  }

  async uploadFoodImages(files: File[], foodId?: string): Promise<string[]> {
    try {
      const formData = new FormData();
      files.forEach(file => {
        formData.append('foodImages', file);
      });
      
      if (foodId) {
        formData.append('foodId', foodId);
      }

      const response = await fetch(`${this.baseUrl}/api/upload/food/images`, {
        method: 'POST',
        body: formData,
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`Upload failed: ${response.statusText}`);
      }

      const result = await response.json();
      
      if (result.success && result.data) {
        return result.data;
      } else {
        throw new Error(result.message || 'Upload failed');
      }
    } catch (error) {
      console.error('Food images upload error:', error);
      toast.error('Failed to upload food images');
      return [];
    }
  }

  async uploadRestaurantLogo(file: File, restaurantId?: string): Promise<string | null> {
    try {
      const formData = new FormData();
      formData.append('logo', file);
      
      if (restaurantId) {
        formData.append('restaurantId', restaurantId);
      }

      const response = await fetch(`${this.baseUrl}/api/upload/restaurant/logo`, {
        method: 'POST',
        body: formData,
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`Upload failed: ${response.statusText}`);
      }

      const result: UploadResponse = await response.json();
      
      if (result.success && result.data) {
        return result.data.url;
      } else {
        throw new Error(result.message || 'Upload failed');
      }
    } catch (error) {
      console.error('Logo upload error:', error);
      toast.error('Failed to upload logo');
      return null;
    }
  }

  async uploadRestaurantBanner(file: File, restaurantId?: string): Promise<string | null> {
    try {
      const formData = new FormData();
      formData.append('banner', file);
      
      if (restaurantId) {
        formData.append('restaurantId', restaurantId);
      }

      const response = await fetch(`${this.baseUrl}/api/upload/restaurant/banner`, {
        method: 'POST',
        body: formData,
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`Upload failed: ${response.statusText}`);
      }

      const result: UploadResponse = await response.json();
      
      if (result.success && result.data) {
        return result.data.url;
      } else {
        throw new Error(result.message || 'Upload failed');
      }
    } catch (error) {
      console.error('Banner upload error:', error);
      toast.error('Failed to upload banner');
      return null;
    }
  }

  async deleteImage(imageUrl: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/api/upload/image`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ imageUrl }),
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`Delete failed: ${response.statusText}`);
      }

      const result = await response.json();
      
      if (result.success) {
        toast.success('Image deleted successfully');
        return true;
      } else {
        throw new Error(result.message || 'Delete failed');
      }
    } catch (error) {
      console.error('Delete error:', error);
      toast.error('Failed to delete image');
      return false;
    }
  }

  // Utility function to validate file type
  validateImageFile(file: File): boolean {
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    const maxSize = 5 * 1024 * 1024; // 5MB

    if (!allowedTypes.includes(file.type)) {
      toast.error('Invalid file type. Only JPEG, PNG, and WebP images are allowed.');
      return false;
    }

    if (file.size > maxSize) {
      toast.error('File size too large. Maximum size is 5MB.');
      return false;
    }

    return true;
  }

  // Utility function to validate multiple files
  validateImageFiles(files: File[]): boolean {
    if (files.length === 0) {
      toast.error('No files selected');
      return false;
    }

    if (files.length > 5) {
      toast.error('Maximum 5 files allowed');
      return false;
    }

    return files.every(file => this.validateImageFile(file));
  }
}

export const uploadService = new UploadService();
