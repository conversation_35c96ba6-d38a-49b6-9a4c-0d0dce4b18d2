'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Percent, 
  Gift, 
  Clock, 
  Star, 
  Tag, 
  Copy, 
  Check,
  Calendar,
  MapPin,
  ChefHat
} from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import Image from 'next/image';
import Link from 'next/link';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { restaurantAPI, offersAPI } from '@/lib/api';
import { formatCurrency, getOptimizedImageUrl } from '@/lib/utils';
import { toast } from 'react-hot-toast';

const OffersPage = () => {
  const [copiedCode, setCopiedCode] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  // Fetch offers data
  const { data: offersData, isLoading } = useQuery({
    queryKey: ['offers', selectedCategory],
    queryFn: () => offersAPI.getOffers({ category: selectedCategory }),
  });

  const offers = offersData?.data || [];

  // Mock offers data for fallback (in real app, this would come from API)
  const offers = [
    {
      id: 1,
      title: '50% OFF on First Order',
      description: 'Get 50% discount on your first order. Valid for new users only.',
      code: 'FIRST50',
      discount: 50,
      type: 'percentage',
      minOrder: 25,
      maxDiscount: 15,
      validUntil: '2024-12-31',
      category: 'new-user',
      image: '/images/offers/first-order.jpg',
      restaurants: ['all'],
      isActive: true
    },
    {
      id: 2,
      title: 'Free Delivery Weekend',
      description: 'Free delivery on all orders above $20 during weekends.',
      code: 'FREEWEEKEND',
      discount: 0,
      type: 'free-delivery',
      minOrder: 20,
      validUntil: '2024-12-31',
      category: 'delivery',
      image: '/images/offers/free-delivery.jpg',
      restaurants: ['all'],
      isActive: true
    },
    {
      id: 3,
      title: 'Pizza Party Deal',
      description: 'Buy 2 pizzas and get 1 free. Valid at selected pizza restaurants.',
      code: 'PIZZA3FOR2',
      discount: 33,
      type: 'buy-get',
      minOrder: 30,
      validUntil: '2024-12-31',
      category: 'food-specific',
      image: '/images/offers/pizza-deal.jpg',
      restaurants: ['pizza'],
      isActive: true
    },
    {
      id: 4,
      title: 'Student Discount',
      description: '20% off for students with valid student ID.',
      code: 'STUDENT20',
      discount: 20,
      type: 'percentage',
      minOrder: 15,
      maxDiscount: 10,
      validUntil: '2024-12-31',
      category: 'student',
      image: '/images/offers/student-discount.jpg',
      restaurants: ['all'],
      isActive: true
    },
    {
      id: 5,
      title: 'Happy Hour Special',
      description: '30% off on all orders between 2 PM - 5 PM.',
      code: 'HAPPYHOUR30',
      discount: 30,
      type: 'percentage',
      minOrder: 20,
      maxDiscount: 12,
      validUntil: '2024-12-31',
      category: 'time-based',
      image: '/images/offers/happy-hour.jpg',
      restaurants: ['all'],
      isActive: true
    },
    {
      id: 6,
      title: 'Family Feast',
      description: '$10 off on orders above $50. Perfect for family meals.',
      code: 'FAMILY10',
      discount: 10,
      type: 'fixed',
      minOrder: 50,
      validUntil: '2024-12-31',
      category: 'family',
      image: '/images/offers/family-feast.jpg',
      restaurants: ['all'],
      isActive: true
    }
  ];

  const categories = [
    { id: 'all', name: 'All Offers', icon: Gift },
    { id: 'new-user', name: 'New User', icon: Star },
    { id: 'delivery', name: 'Free Delivery', icon: MapPin },
    { id: 'food-specific', name: 'Food Deals', icon: ChefHat },
    { id: 'student', name: 'Student', icon: Tag },
    { id: 'time-based', name: 'Time Limited', icon: Clock },
    { id: 'family', name: 'Family', icon: Gift }
  ];

  // Use real data if available, otherwise fallback to mock data
  const displayOffers = offers.length > 0 ? offers : mockOffers;

  const filteredOffers = selectedCategory === 'all'
    ? displayOffers
    : displayOffers.filter(offer => offer.category === selectedCategory);

  const copyToClipboard = (code: string) => {
    navigator.clipboard.writeText(code);
    setCopiedCode(code);
    toast.success('Coupon code copied!');
    setTimeout(() => setCopiedCode(''), 2000);
  };

  const getDiscountText = (offer: any) => {
    switch (offer.type) {
      case 'percentage':
        return `${offer.discount}% OFF`;
      case 'fixed':
        return `$${offer.discount} OFF`;
      case 'free-delivery':
        return 'FREE DELIVERY';
      case 'buy-get':
        return 'BUY 2 GET 1';
      default:
        return 'SPECIAL OFFER';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      {/* Hero Section */}
      <section className="pt-24 pb-8 bg-gradient-to-r from-purple-600 to-pink-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center text-white"
          >
            <div className="flex justify-center mb-6">
              <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
                <Percent className="w-8 h-8" />
              </div>
            </div>
            <h1 className="text-3xl sm:text-4xl font-bold mb-4">
              Amazing Offers & Deals
            </h1>
            <p className="text-xl opacity-90 mb-8">
              Save more on your favorite food with our exclusive offers
            </p>
          </motion.div>
        </div>
      </section>

      {/* Categories */}
      <section className="py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-white rounded-2xl shadow-md p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Browse by Category</h2>
            <div className="flex flex-wrap gap-3">
              {categories.map((category) => {
                const IconComponent = category.icon;
                return (
                  <button
                    key={category.id}
                    onClick={() => setSelectedCategory(category.id)}
                    className={`flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200 ${
                      selectedCategory === category.id
                        ? 'bg-purple-500 text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    <IconComponent className="w-4 h-4" />
                    {category.name}
                  </button>
                );
              })}
            </div>
          </div>
        </div>
      </section>

      {/* Offers Grid */}
      <section className="pb-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between mb-8">
            <h2 className="text-2xl font-bold text-gray-900">
              {selectedCategory === 'all' 
                ? `All Offers (${filteredOffers.length})`
                : `${categories.find(c => c.id === selectedCategory)?.name} Offers (${filteredOffers.length})`
              }
            </h2>
          </div>

          <motion.div
            layout
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
          >
            <AnimatePresence>
              {filteredOffers.map((offer) => (
                <motion.div
                  key={offer.id}
                  layout
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.9 }}
                  transition={{ duration: 0.3 }}
                  className="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300"
                >
                  {/* Offer Image */}
                  <div className="relative h-48">
                    <Image
                      src={offer.image}
                      alt={offer.title}
                      fill
                      className="object-cover"
                      onError={(e) => {
                        e.currentTarget.src = '/images/offer-placeholder.jpg';
                      }}
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                    
                    {/* Discount Badge */}
                    <div className="absolute top-4 left-4">
                      <div className="bg-red-500 text-white px-3 py-1 rounded-full text-sm font-bold">
                        {getDiscountText(offer)}
                      </div>
                    </div>

                    {/* Valid Until */}
                    <div className="absolute bottom-4 left-4 text-white">
                      <div className="flex items-center gap-1 text-sm">
                        <Calendar className="w-3 h-3" />
                        <span>Valid until {new Date(offer.validUntil).toLocaleDateString()}</span>
                      </div>
                    </div>
                  </div>

                  {/* Offer Details */}
                  <div className="p-6">
                    <h3 className="text-xl font-bold text-gray-900 mb-2">{offer.title}</h3>
                    <p className="text-gray-600 text-sm mb-4">{offer.description}</p>

                    {/* Offer Terms */}
                    <div className="space-y-2 mb-4">
                      <div className="flex items-center gap-2 text-sm text-gray-500">
                        <Tag className="w-3 h-3" />
                        <span>Min. order: {formatCurrency(offer.minOrder)}</span>
                      </div>
                      {offer.maxDiscount && (
                        <div className="flex items-center gap-2 text-sm text-gray-500">
                          <Percent className="w-3 h-3" />
                          <span>Max. discount: {formatCurrency(offer.maxDiscount)}</span>
                        </div>
                      )}
                    </div>

                    {/* Coupon Code */}
                    <div className="bg-gray-50 rounded-lg p-4 mb-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-xs text-gray-500 mb-1">Coupon Code</p>
                          <p className="font-mono font-bold text-lg text-gray-900">{offer.code}</p>
                        </div>
                        <button
                          onClick={() => copyToClipboard(offer.code)}
                          className="flex items-center gap-2 px-3 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors duration-200"
                        >
                          {copiedCode === offer.code ? (
                            <>
                              <Check className="w-4 h-4" />
                              <span className="text-sm">Copied!</span>
                            </>
                          ) : (
                            <>
                              <Copy className="w-4 h-4" />
                              <span className="text-sm">Copy</span>
                            </>
                          )}
                        </button>
                      </div>
                    </div>

                    {/* Action Button */}
                    <Link
                      href="/restaurants"
                      className="block w-full text-center px-4 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-lg hover:from-purple-600 hover:to-pink-600 transition-all duration-200 font-medium"
                    >
                      Order Now
                    </Link>
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
          </motion.div>

          {filteredOffers.length === 0 && (
            <div className="text-center py-12">
              <Gift className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No offers found</h3>
              <p className="text-gray-600">
                No offers available in this category at the moment.
              </p>
            </div>
          )}
        </div>
      </section>

      {/* How to Use Section */}
      <section className="py-12 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">How to Use Offers</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Follow these simple steps to apply offers and save on your orders
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Copy className="w-8 h-8 text-purple-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">1. Copy Code</h3>
              <p className="text-gray-600">
                Click on any offer to copy the coupon code to your clipboard
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <ChefHat className="w-8 h-8 text-purple-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">2. Order Food</h3>
              <p className="text-gray-600">
                Browse restaurants and add your favorite items to the cart
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Percent className="w-8 h-8 text-purple-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">3. Apply & Save</h3>
              <p className="text-gray-600">
                Paste the code at checkout and enjoy your discount
              </p>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default OffersPage;
