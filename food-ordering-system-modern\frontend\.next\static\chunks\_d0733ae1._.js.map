{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/project%20food-order%206k/food-ordering-system-modern/frontend/src/app/debug/page.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { useQuery } from '@tanstack/react-query';\nimport { vendorAPI, restaurantAPI } from '@/lib/api';\nimport useAuthStore from '@/store/useAuthStore';\n\nconst DebugPage = () => {\n  const { user, isAuthenticated } = useAuthStore();\n\n  // Test vendor restaurant endpoint\n  const { data: restaurantData, isLoading: restaurantLoading, error: restaurantError } = useQuery({\n    queryKey: ['debug-vendor-restaurant'],\n    queryFn: () => vendorAPI.getRestaurant(),\n    enabled: isAuthenticated && user?.role === 'vendor',\n  });\n\n  // Test vendor foods endpoint\n  const { data: foodsData, isLoading: foodsLoading, error: foodsError } = useQuery({\n    queryKey: ['debug-vendor-foods'],\n    queryFn: () => vendorAPI.getFoods(),\n    enabled: isAuthenticated && user?.role === 'vendor',\n  });\n\n  // Test public restaurants endpoint\n  const { data: publicRestaurantsData, isLoading: publicLoading, error: publicError } = useQuery({\n    queryKey: ['debug-public-restaurants'],\n    queryFn: () => restaurantAPI.getAll({ page: 1, limit: 5 }),\n  });\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 p-8\">\n      <div className=\"max-w-6xl mx-auto\">\n        <h1 className=\"text-3xl font-bold text-gray-900 mb-8\">Debug Information</h1>\n\n        {/* User Info */}\n        <div className=\"bg-white rounded-lg shadow p-6 mb-6\">\n          <h2 className=\"text-xl font-semibold mb-4\">User Information</h2>\n          <div className=\"space-y-2\">\n            <p><strong>Authenticated:</strong> {isAuthenticated ? 'Yes' : 'No'}</p>\n            <p><strong>Role:</strong> {user?.role || 'None'}</p>\n            <p><strong>Name:</strong> {user?.name || 'N/A'}</p>\n            <p><strong>Email:</strong> {user?.email || 'N/A'}</p>\n          </div>\n        </div>\n\n        {/* Vendor Restaurant Data */}\n        {user?.role === 'vendor' && (\n          <div className=\"bg-white rounded-lg shadow p-6 mb-6\">\n            <h2 className=\"text-xl font-semibold mb-4\">Vendor Restaurant Data</h2>\n            <div className=\"space-y-2\">\n              <p><strong>Loading:</strong> {restaurantLoading ? 'Yes' : 'No'}</p>\n              <p><strong>Error:</strong> {restaurantError ? JSON.stringify(restaurantError.response?.data || restaurantError.message) : 'None'}</p>\n              <div>\n                <strong>Raw Data:</strong>\n                <pre className=\"mt-2 p-4 bg-gray-100 rounded text-sm overflow-auto\">\n                  {JSON.stringify(restaurantData, null, 2)}\n                </pre>\n              </div>\n              {restaurantData?.data && (\n                <div>\n                  <strong>Restaurant Object:</strong>\n                  <pre className=\"mt-2 p-4 bg-gray-100 rounded text-sm overflow-auto\">\n                    {JSON.stringify(restaurantData.data, null, 2)}\n                  </pre>\n                  <div className=\"mt-4 space-y-1\">\n                    <p><strong>Restaurant ID:</strong> {restaurantData.data.id || 'undefined'}</p>\n                    <p><strong>Restaurant _id:</strong> {restaurantData.data._id || 'undefined'}</p>\n                    <p><strong>Restaurant Name:</strong> {restaurantData.data.name || 'undefined'}</p>\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n\n        {/* Vendor Foods Data */}\n        {user?.role === 'vendor' && (\n          <div className=\"bg-white rounded-lg shadow p-6 mb-6\">\n            <h2 className=\"text-xl font-semibold mb-4\">Vendor Foods Data</h2>\n            <div className=\"space-y-2\">\n              <p><strong>Loading:</strong> {foodsLoading ? 'Yes' : 'No'}</p>\n              <p><strong>Error:</strong> {foodsError ? JSON.stringify(foodsError.response?.data || foodsError.message) : 'None'}</p>\n              <div>\n                <strong>Raw Data:</strong>\n                <pre className=\"mt-2 p-4 bg-gray-100 rounded text-sm overflow-auto max-h-64\">\n                  {JSON.stringify(foodsData, null, 2)}\n                </pre>\n              </div>\n              {foodsData?.data && (\n                <div>\n                  <p><strong>Foods Count:</strong> {Array.isArray(foodsData.data) ? foodsData.data.length : 'Not an array'}</p>\n                  <p><strong>Is Array:</strong> {Array.isArray(foodsData.data) ? 'Yes' : 'No'}</p>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n\n        {/* Public Restaurants Data */}\n        <div className=\"bg-white rounded-lg shadow p-6 mb-6\">\n          <h2 className=\"text-xl font-semibold mb-4\">Public Restaurants Data</h2>\n          <div className=\"space-y-2\">\n            <p><strong>Loading:</strong> {publicLoading ? 'Yes' : 'No'}</p>\n            <p><strong>Error:</strong> {publicError ? JSON.stringify(publicError.response?.data || publicError.message) : 'None'}</p>\n            <div>\n              <strong>Raw Data:</strong>\n              <pre className=\"mt-2 p-4 bg-gray-100 rounded text-sm overflow-auto max-h-64\">\n                {JSON.stringify(publicRestaurantsData, null, 2)}\n              </pre>\n            </div>\n            {publicRestaurantsData?.data && (\n              <div>\n                <p><strong>Restaurants Count:</strong> {Array.isArray(publicRestaurantsData.data) ? publicRestaurantsData.data.length : 'Not an array'}</p>\n                <p><strong>Is Array:</strong> {Array.isArray(publicRestaurantsData.data) ? 'Yes' : 'No'}</p>\n                {Array.isArray(publicRestaurantsData.data) && publicRestaurantsData.data.length > 0 && (\n                  <div>\n                    <strong>First Restaurant:</strong>\n                    <pre className=\"mt-2 p-4 bg-gray-100 rounded text-sm overflow-auto\">\n                      {JSON.stringify(publicRestaurantsData.data[0], null, 2)}\n                    </pre>\n                    <div className=\"mt-2 space-y-1\">\n                      <p><strong>First Restaurant ID:</strong> {publicRestaurantsData.data[0].id || 'undefined'}</p>\n                      <p><strong>First Restaurant _id:</strong> {publicRestaurantsData.data[0]._id || 'undefined'}</p>\n                    </div>\n                  </div>\n                )}\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Quick Actions */}\n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <h2 className=\"text-xl font-semibold mb-4\">Quick Actions</h2>\n          <div className=\"space-y-4\">\n            <div className=\"flex gap-4\">\n              <a \n                href=\"/\" \n                className=\"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600\"\n              >\n                Go to Home\n              </a>\n              <a \n                href=\"/restaurants\" \n                className=\"px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600\"\n              >\n                Go to Restaurants\n              </a>\n              {user?.role === 'vendor' && (\n                <>\n                  <a \n                    href=\"/vendor/dashboard\" \n                    className=\"px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600\"\n                  >\n                    Vendor Dashboard\n                  </a>\n                  <a \n                    href=\"/vendor/menu\" \n                    className=\"px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600\"\n                  >\n                    Vendor Menu\n                  </a>\n                </>\n              )}\n            </div>\n            \n            {/* Test Restaurant Link */}\n            {restaurantData?.data && (\n              <div className=\"mt-4\">\n                <p className=\"mb-2\"><strong>Test Restaurant Links:</strong></p>\n                <div className=\"flex gap-2\">\n                  <a \n                    href={`/restaurants/${restaurantData.data.id}`}\n                    className=\"px-3 py-1 bg-red-500 text-white rounded text-sm hover:bg-red-600\"\n                  >\n                    Using .id: {restaurantData.data.id || 'undefined'}\n                  </a>\n                  <a \n                    href={`/restaurants/${restaurantData.data._id}`}\n                    className=\"px-3 py-1 bg-red-500 text-white rounded text-sm hover:bg-red-600\"\n                  >\n                    Using ._id: {restaurantData.data._id || 'undefined'}\n                  </a>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default DebugPage;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;;;AALA;;;;AAOA,MAAM,YAAY;QA6CyD,2BA8BL,sBAsBD;;IAhGnE,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAY,AAAD;IAE7C,kCAAkC;IAClC,MAAM,EAAE,MAAM,cAAc,EAAE,WAAW,iBAAiB,EAAE,OAAO,eAAe,EAAE,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QAC9F,UAAU;YAAC;SAA0B;QACrC,OAAO;kCAAE,IAAM,oHAAA,CAAA,YAAS,CAAC,aAAa;;QACtC,SAAS,mBAAmB,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK;IAC7C;IAEA,6BAA6B;IAC7B,MAAM,EAAE,MAAM,SAAS,EAAE,WAAW,YAAY,EAAE,OAAO,UAAU,EAAE,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QAC/E,UAAU;YAAC;SAAqB;QAChC,OAAO;kCAAE,IAAM,oHAAA,CAAA,YAAS,CAAC,QAAQ;;QACjC,SAAS,mBAAmB,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK;IAC7C;IAEA,mCAAmC;IACnC,MAAM,EAAE,MAAM,qBAAqB,EAAE,WAAW,aAAa,EAAE,OAAO,WAAW,EAAE,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QAC7F,UAAU;YAAC;SAA2B;QACtC,OAAO;kCAAE,IAAM,oHAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;oBAAE,MAAM;oBAAG,OAAO;gBAAE;;IAC1D;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAAwC;;;;;;8BAGtD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAC3C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDAAE,6LAAC;sDAAO;;;;;;wCAAuB;wCAAE,kBAAkB,QAAQ;;;;;;;8CAC9D,6LAAC;;sDAAE,6LAAC;sDAAO;;;;;;wCAAc;wCAAE,CAAA,iBAAA,2BAAA,KAAM,IAAI,KAAI;;;;;;;8CACzC,6LAAC;;sDAAE,6LAAC;sDAAO;;;;;;wCAAc;wCAAE,CAAA,iBAAA,2BAAA,KAAM,IAAI,KAAI;;;;;;;8CACzC,6LAAC;;sDAAE,6LAAC;sDAAO;;;;;;wCAAe;wCAAE,CAAA,iBAAA,2BAAA,KAAM,KAAK,KAAI;;;;;;;;;;;;;;;;;;;gBAK9C,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK,0BACd,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAC3C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDAAE,6LAAC;sDAAO;;;;;;wCAAiB;wCAAE,oBAAoB,QAAQ;;;;;;;8CAC1D,6LAAC;;sDAAE,6LAAC;sDAAO;;;;;;wCAAe;wCAAE,kBAAkB,KAAK,SAAS,CAAC,EAAA,4BAAA,gBAAgB,QAAQ,cAAxB,gDAAA,0BAA0B,IAAI,KAAI,gBAAgB,OAAO,IAAI;;;;;;;8CAC1H,6LAAC;;sDACC,6LAAC;sDAAO;;;;;;sDACR,6LAAC;4CAAI,WAAU;sDACZ,KAAK,SAAS,CAAC,gBAAgB,MAAM;;;;;;;;;;;;gCAGzC,CAAA,2BAAA,qCAAA,eAAgB,IAAI,mBACnB,6LAAC;;sDACC,6LAAC;sDAAO;;;;;;sDACR,6LAAC;4CAAI,WAAU;sDACZ,KAAK,SAAS,CAAC,eAAe,IAAI,EAAE,MAAM;;;;;;sDAE7C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEAAE,6LAAC;sEAAO;;;;;;wDAAuB;wDAAE,eAAe,IAAI,CAAC,EAAE,IAAI;;;;;;;8DAC9D,6LAAC;;sEAAE,6LAAC;sEAAO;;;;;;wDAAwB;wDAAE,eAAe,IAAI,CAAC,GAAG,IAAI;;;;;;;8DAChE,6LAAC;;sEAAE,6LAAC;sEAAO;;;;;;wDAAyB;wDAAE,eAAe,IAAI,CAAC,IAAI,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAS7E,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK,0BACd,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAC3C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDAAE,6LAAC;sDAAO;;;;;;wCAAiB;wCAAE,eAAe,QAAQ;;;;;;;8CACrD,6LAAC;;sDAAE,6LAAC;sDAAO;;;;;;wCAAe;wCAAE,aAAa,KAAK,SAAS,CAAC,EAAA,uBAAA,WAAW,QAAQ,cAAnB,2CAAA,qBAAqB,IAAI,KAAI,WAAW,OAAO,IAAI;;;;;;;8CAC3G,6LAAC;;sDACC,6LAAC;sDAAO;;;;;;sDACR,6LAAC;4CAAI,WAAU;sDACZ,KAAK,SAAS,CAAC,WAAW,MAAM;;;;;;;;;;;;gCAGpC,CAAA,sBAAA,gCAAA,UAAW,IAAI,mBACd,6LAAC;;sDACC,6LAAC;;8DAAE,6LAAC;8DAAO;;;;;;gDAAqB;gDAAE,MAAM,OAAO,CAAC,UAAU,IAAI,IAAI,UAAU,IAAI,CAAC,MAAM,GAAG;;;;;;;sDAC1F,6LAAC;;8DAAE,6LAAC;8DAAO;;;;;;gDAAkB;gDAAE,MAAM,OAAO,CAAC,UAAU,IAAI,IAAI,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;8BAQjF,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAC3C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDAAE,6LAAC;sDAAO;;;;;;wCAAiB;wCAAE,gBAAgB,QAAQ;;;;;;;8CACtD,6LAAC;;sDAAE,6LAAC;sDAAO;;;;;;wCAAe;wCAAE,cAAc,KAAK,SAAS,CAAC,EAAA,wBAAA,YAAY,QAAQ,cAApB,4CAAA,sBAAsB,IAAI,KAAI,YAAY,OAAO,IAAI;;;;;;;8CAC9G,6LAAC;;sDACC,6LAAC;sDAAO;;;;;;sDACR,6LAAC;4CAAI,WAAU;sDACZ,KAAK,SAAS,CAAC,uBAAuB,MAAM;;;;;;;;;;;;gCAGhD,CAAA,kCAAA,4CAAA,sBAAuB,IAAI,mBAC1B,6LAAC;;sDACC,6LAAC;;8DAAE,6LAAC;8DAAO;;;;;;gDAA2B;gDAAE,MAAM,OAAO,CAAC,sBAAsB,IAAI,IAAI,sBAAsB,IAAI,CAAC,MAAM,GAAG;;;;;;;sDACxH,6LAAC;;8DAAE,6LAAC;8DAAO;;;;;;gDAAkB;gDAAE,MAAM,OAAO,CAAC,sBAAsB,IAAI,IAAI,QAAQ;;;;;;;wCAClF,MAAM,OAAO,CAAC,sBAAsB,IAAI,KAAK,sBAAsB,IAAI,CAAC,MAAM,GAAG,mBAChF,6LAAC;;8DACC,6LAAC;8DAAO;;;;;;8DACR,6LAAC;oDAAI,WAAU;8DACZ,KAAK,SAAS,CAAC,sBAAsB,IAAI,CAAC,EAAE,EAAE,MAAM;;;;;;8DAEvD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EAAE,6LAAC;8EAAO;;;;;;gEAA6B;gEAAE,sBAAsB,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI;;;;;;;sEAC9E,6LAAC;;8EAAE,6LAAC;8EAAO;;;;;;gEAA8B;gEAAE,sBAAsB,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAU9F,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAC3C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,6LAAC;4CACC,MAAK;4CACL,WAAU;sDACX;;;;;;wCAGA,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK,0BACd;;8DACE,6LAAC;oDACC,MAAK;oDACL,WAAU;8DACX;;;;;;8DAGD,6LAAC;oDACC,MAAK;oDACL,WAAU;8DACX;;;;;;;;;;;;;;gCAQN,CAAA,2BAAA,qCAAA,eAAgB,IAAI,mBACnB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAO,cAAA,6LAAC;0DAAO;;;;;;;;;;;sDAC5B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,MAAM,AAAC,gBAAsC,OAAvB,eAAe,IAAI,CAAC,EAAE;oDAC5C,WAAU;;wDACX;wDACa,eAAe,IAAI,CAAC,EAAE,IAAI;;;;;;;8DAExC,6LAAC;oDACC,MAAM,AAAC,gBAAuC,OAAxB,eAAe,IAAI,CAAC,GAAG;oDAC7C,WAAU;;wDACX;wDACc,eAAe,IAAI,CAAC,GAAG,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU5D;GAzLM;;QAC8B,+HAAA,CAAA,UAAY;QAGyC,8KAAA,CAAA,WAAQ;QAOvB,8KAAA,CAAA,WAAQ;QAOM,8KAAA,CAAA,WAAQ;;;KAlB1F;uCA2LS", "debugId": null}}, {"offset": {"line": 807, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/project%20food-order%206k/food-ordering-system-modern/frontend/node_modules/%40tanstack/query-core/src/queryObserver.ts"], "sourcesContent": ["import { focusManager } from './focusManager'\nimport { notifyManager } from './notifyManager'\nimport { fetchState } from './query'\nimport { Subscribable } from './subscribable'\nimport { pendingThenable } from './thenable'\nimport {\n  isServer,\n  isValidTimeout,\n  noop,\n  replaceData,\n  resolveEnabled,\n  resolveStaleTime,\n  shallowEqualObjects,\n  timeUntilStale,\n} from './utils'\nimport type { FetchOptions, Query, QueryState } from './query'\nimport type { QueryClient } from './queryClient'\nimport type { PendingThenable, Thenable } from './thenable'\nimport type {\n  DefaultError,\n  DefaultedQueryObserverOptions,\n  PlaceholderDataFunction,\n  QueryKey,\n  QueryObserverBaseResult,\n  QueryObserverOptions,\n  QueryObserverResult,\n  QueryOptions,\n  RefetchOptions,\n} from './types'\n\ntype QueryObserverListener<TData, TError> = (\n  result: QueryObserverResult<TData, TError>,\n) => void\n\ninterface ObserverFetchOptions extends FetchOptions {\n  throwOnError?: boolean\n}\n\nexport class QueryObserver<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> extends Subscribable<QueryObserverListener<TData, TError>> {\n  #client: QueryClient\n  #currentQuery: Query<TQueryFnData, TError, TQueryData, TQueryKey> = undefined!\n  #currentQueryInitialState: QueryState<TQueryData, TError> = undefined!\n  #currentResult: QueryObserverResult<TData, TError> = undefined!\n  #currentResultState?: QueryState<TQueryData, TError>\n  #currentResultOptions?: QueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >\n  #currentThenable: Thenable<TData>\n  #selectError: TError | null\n  #selectFn?: (data: TQueryData) => TData\n  #selectResult?: TData\n  // This property keeps track of the last query with defined data.\n  // It will be used to pass the previous data and query to the placeholder function between renders.\n  #lastQueryWithDefinedData?: Query<TQueryFnData, TError, TQueryData, TQueryKey>\n  #staleTimeoutId?: ReturnType<typeof setTimeout>\n  #refetchIntervalId?: ReturnType<typeof setInterval>\n  #currentRefetchInterval?: number | false\n  #trackedProps = new Set<keyof QueryObserverResult>()\n\n  constructor(\n    client: QueryClient,\n    public options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ) {\n    super()\n\n    this.#client = client\n    this.#selectError = null\n    this.#currentThenable = pendingThenable()\n    if (!this.options.experimental_prefetchInRender) {\n      this.#currentThenable.reject(\n        new Error('experimental_prefetchInRender feature flag is not enabled'),\n      )\n    }\n\n    this.bindMethods()\n    this.setOptions(options)\n  }\n\n  protected bindMethods(): void {\n    this.refetch = this.refetch.bind(this)\n  }\n\n  protected onSubscribe(): void {\n    if (this.listeners.size === 1) {\n      this.#currentQuery.addObserver(this)\n\n      if (shouldFetchOnMount(this.#currentQuery, this.options)) {\n        this.#executeFetch()\n      } else {\n        this.updateResult()\n      }\n\n      this.#updateTimers()\n    }\n  }\n\n  protected onUnsubscribe(): void {\n    if (!this.hasListeners()) {\n      this.destroy()\n    }\n  }\n\n  shouldFetchOnReconnect(): boolean {\n    return shouldFetchOn(\n      this.#currentQuery,\n      this.options,\n      this.options.refetchOnReconnect,\n    )\n  }\n\n  shouldFetchOnWindowFocus(): boolean {\n    return shouldFetchOn(\n      this.#currentQuery,\n      this.options,\n      this.options.refetchOnWindowFocus,\n    )\n  }\n\n  destroy(): void {\n    this.listeners = new Set()\n    this.#clearStaleTimeout()\n    this.#clearRefetchInterval()\n    this.#currentQuery.removeObserver(this)\n  }\n\n  setOptions(\n    options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): void {\n    const prevOptions = this.options\n    const prevQuery = this.#currentQuery\n\n    this.options = this.#client.defaultQueryOptions(options)\n\n    if (\n      this.options.enabled !== undefined &&\n      typeof this.options.enabled !== 'boolean' &&\n      typeof this.options.enabled !== 'function' &&\n      typeof resolveEnabled(this.options.enabled, this.#currentQuery) !==\n        'boolean'\n    ) {\n      throw new Error(\n        'Expected enabled to be a boolean or a callback that returns a boolean',\n      )\n    }\n\n    this.#updateQuery()\n    this.#currentQuery.setOptions(this.options)\n\n    if (\n      prevOptions._defaulted &&\n      !shallowEqualObjects(this.options, prevOptions)\n    ) {\n      this.#client.getQueryCache().notify({\n        type: 'observerOptionsUpdated',\n        query: this.#currentQuery,\n        observer: this,\n      })\n    }\n\n    const mounted = this.hasListeners()\n\n    // Fetch if there are subscribers\n    if (\n      mounted &&\n      shouldFetchOptionally(\n        this.#currentQuery,\n        prevQuery,\n        this.options,\n        prevOptions,\n      )\n    ) {\n      this.#executeFetch()\n    }\n\n    // Update result\n    this.updateResult()\n\n    // Update stale interval if needed\n    if (\n      mounted &&\n      (this.#currentQuery !== prevQuery ||\n        resolveEnabled(this.options.enabled, this.#currentQuery) !==\n          resolveEnabled(prevOptions.enabled, this.#currentQuery) ||\n        resolveStaleTime(this.options.staleTime, this.#currentQuery) !==\n          resolveStaleTime(prevOptions.staleTime, this.#currentQuery))\n    ) {\n      this.#updateStaleTimeout()\n    }\n\n    const nextRefetchInterval = this.#computeRefetchInterval()\n\n    // Update refetch interval if needed\n    if (\n      mounted &&\n      (this.#currentQuery !== prevQuery ||\n        resolveEnabled(this.options.enabled, this.#currentQuery) !==\n          resolveEnabled(prevOptions.enabled, this.#currentQuery) ||\n        nextRefetchInterval !== this.#currentRefetchInterval)\n    ) {\n      this.#updateRefetchInterval(nextRefetchInterval)\n    }\n  }\n\n  getOptimisticResult(\n    options: DefaultedQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): QueryObserverResult<TData, TError> {\n    const query = this.#client.getQueryCache().build(this.#client, options)\n\n    const result = this.createResult(query, options)\n\n    if (shouldAssignObserverCurrentProperties(this, result)) {\n      // this assigns the optimistic result to the current Observer\n      // because if the query function changes, useQuery will be performing\n      // an effect where it would fetch again.\n      // When the fetch finishes, we perform a deep data cloning in order\n      // to reuse objects references. This deep data clone is performed against\n      // the `observer.currentResult.data` property\n      // When QueryKey changes, we refresh the query and get new `optimistic`\n      // result, while we leave the `observer.currentResult`, so when new data\n      // arrives, it finds the old `observer.currentResult` which is related\n      // to the old QueryKey. Which means that currentResult and selectData are\n      // out of sync already.\n      // To solve this, we move the cursor of the currentResult every time\n      // an observer reads an optimistic value.\n\n      // When keeping the previous data, the result doesn't change until new\n      // data arrives.\n      this.#currentResult = result\n      this.#currentResultOptions = this.options\n      this.#currentResultState = this.#currentQuery.state\n    }\n    return result\n  }\n\n  getCurrentResult(): QueryObserverResult<TData, TError> {\n    return this.#currentResult\n  }\n\n  trackResult(\n    result: QueryObserverResult<TData, TError>,\n    onPropTracked?: (key: keyof QueryObserverResult) => void,\n  ): QueryObserverResult<TData, TError> {\n    return new Proxy(result, {\n      get: (target, key) => {\n        this.trackProp(key as keyof QueryObserverResult)\n        onPropTracked?.(key as keyof QueryObserverResult)\n        return Reflect.get(target, key)\n      },\n    })\n  }\n\n  trackProp(key: keyof QueryObserverResult) {\n    this.#trackedProps.add(key)\n  }\n\n  getCurrentQuery(): Query<TQueryFnData, TError, TQueryData, TQueryKey> {\n    return this.#currentQuery\n  }\n\n  refetch({ ...options }: RefetchOptions = {}): Promise<\n    QueryObserverResult<TData, TError>\n  > {\n    return this.fetch({\n      ...options,\n    })\n  }\n\n  fetchOptimistic(\n    options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): Promise<QueryObserverResult<TData, TError>> {\n    const defaultedOptions = this.#client.defaultQueryOptions(options)\n\n    const query = this.#client\n      .getQueryCache()\n      .build(this.#client, defaultedOptions)\n\n    return query.fetch().then(() => this.createResult(query, defaultedOptions))\n  }\n\n  protected fetch(\n    fetchOptions: ObserverFetchOptions,\n  ): Promise<QueryObserverResult<TData, TError>> {\n    return this.#executeFetch({\n      ...fetchOptions,\n      cancelRefetch: fetchOptions.cancelRefetch ?? true,\n    }).then(() => {\n      this.updateResult()\n      return this.#currentResult\n    })\n  }\n\n  #executeFetch(\n    fetchOptions?: Omit<ObserverFetchOptions, 'initialPromise'>,\n  ): Promise<TQueryData | undefined> {\n    // Make sure we reference the latest query as the current one might have been removed\n    this.#updateQuery()\n\n    // Fetch\n    let promise: Promise<TQueryData | undefined> = this.#currentQuery.fetch(\n      this.options as QueryOptions<TQueryFnData, TError, TQueryData, TQueryKey>,\n      fetchOptions,\n    )\n\n    if (!fetchOptions?.throwOnError) {\n      promise = promise.catch(noop)\n    }\n\n    return promise\n  }\n\n  #updateStaleTimeout(): void {\n    this.#clearStaleTimeout()\n    const staleTime = resolveStaleTime(\n      this.options.staleTime,\n      this.#currentQuery,\n    )\n\n    if (isServer || this.#currentResult.isStale || !isValidTimeout(staleTime)) {\n      return\n    }\n\n    const time = timeUntilStale(this.#currentResult.dataUpdatedAt, staleTime)\n\n    // The timeout is sometimes triggered 1 ms before the stale time expiration.\n    // To mitigate this issue we always add 1 ms to the timeout.\n    const timeout = time + 1\n\n    this.#staleTimeoutId = setTimeout(() => {\n      if (!this.#currentResult.isStale) {\n        this.updateResult()\n      }\n    }, timeout)\n  }\n\n  #computeRefetchInterval() {\n    return (\n      (typeof this.options.refetchInterval === 'function'\n        ? this.options.refetchInterval(this.#currentQuery)\n        : this.options.refetchInterval) ?? false\n    )\n  }\n\n  #updateRefetchInterval(nextInterval: number | false): void {\n    this.#clearRefetchInterval()\n\n    this.#currentRefetchInterval = nextInterval\n\n    if (\n      isServer ||\n      resolveEnabled(this.options.enabled, this.#currentQuery) === false ||\n      !isValidTimeout(this.#currentRefetchInterval) ||\n      this.#currentRefetchInterval === 0\n    ) {\n      return\n    }\n\n    this.#refetchIntervalId = setInterval(() => {\n      if (\n        this.options.refetchIntervalInBackground ||\n        focusManager.isFocused()\n      ) {\n        this.#executeFetch()\n      }\n    }, this.#currentRefetchInterval)\n  }\n\n  #updateTimers(): void {\n    this.#updateStaleTimeout()\n    this.#updateRefetchInterval(this.#computeRefetchInterval())\n  }\n\n  #clearStaleTimeout(): void {\n    if (this.#staleTimeoutId) {\n      clearTimeout(this.#staleTimeoutId)\n      this.#staleTimeoutId = undefined\n    }\n  }\n\n  #clearRefetchInterval(): void {\n    if (this.#refetchIntervalId) {\n      clearInterval(this.#refetchIntervalId)\n      this.#refetchIntervalId = undefined\n    }\n  }\n\n  protected createResult(\n    query: Query<TQueryFnData, TError, TQueryData, TQueryKey>,\n    options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): QueryObserverResult<TData, TError> {\n    const prevQuery = this.#currentQuery\n    const prevOptions = this.options\n    const prevResult = this.#currentResult as\n      | QueryObserverResult<TData, TError>\n      | undefined\n    const prevResultState = this.#currentResultState\n    const prevResultOptions = this.#currentResultOptions\n    const queryChange = query !== prevQuery\n    const queryInitialState = queryChange\n      ? query.state\n      : this.#currentQueryInitialState\n\n    const { state } = query\n    let newState = { ...state }\n    let isPlaceholderData = false\n    let data: TData | undefined\n\n    // Optimistically set result in fetching state if needed\n    if (options._optimisticResults) {\n      const mounted = this.hasListeners()\n\n      const fetchOnMount = !mounted && shouldFetchOnMount(query, options)\n\n      const fetchOptionally =\n        mounted && shouldFetchOptionally(query, prevQuery, options, prevOptions)\n\n      if (fetchOnMount || fetchOptionally) {\n        newState = {\n          ...newState,\n          ...fetchState(state.data, query.options),\n        }\n      }\n      if (options._optimisticResults === 'isRestoring') {\n        newState.fetchStatus = 'idle'\n      }\n    }\n\n    let { error, errorUpdatedAt, status } = newState\n\n    // Per default, use query data\n    data = newState.data as unknown as TData\n    let skipSelect = false\n\n    // use placeholderData if needed\n    if (\n      options.placeholderData !== undefined &&\n      data === undefined &&\n      status === 'pending'\n    ) {\n      let placeholderData\n\n      // Memoize placeholder data\n      if (\n        prevResult?.isPlaceholderData &&\n        options.placeholderData === prevResultOptions?.placeholderData\n      ) {\n        placeholderData = prevResult.data\n        // we have to skip select when reading this memoization\n        // because prevResult.data is already \"selected\"\n        skipSelect = true\n      } else {\n        // compute placeholderData\n        placeholderData =\n          typeof options.placeholderData === 'function'\n            ? (\n                options.placeholderData as unknown as PlaceholderDataFunction<TQueryData>\n              )(\n                this.#lastQueryWithDefinedData?.state.data,\n                this.#lastQueryWithDefinedData as any,\n              )\n            : options.placeholderData\n      }\n\n      if (placeholderData !== undefined) {\n        status = 'success'\n        data = replaceData(\n          prevResult?.data,\n          placeholderData as unknown,\n          options,\n        ) as TData\n        isPlaceholderData = true\n      }\n    }\n\n    // Select data if needed\n    // this also runs placeholderData through the select function\n    if (options.select && data !== undefined && !skipSelect) {\n      // Memoize select result\n      if (\n        prevResult &&\n        data === prevResultState?.data &&\n        options.select === this.#selectFn\n      ) {\n        data = this.#selectResult\n      } else {\n        try {\n          this.#selectFn = options.select\n          data = options.select(data as any)\n          data = replaceData(prevResult?.data, data, options)\n          this.#selectResult = data\n          this.#selectError = null\n        } catch (selectError) {\n          this.#selectError = selectError as TError\n        }\n      }\n    }\n\n    if (this.#selectError) {\n      error = this.#selectError as any\n      data = this.#selectResult\n      errorUpdatedAt = Date.now()\n      status = 'error'\n    }\n\n    const isFetching = newState.fetchStatus === 'fetching'\n    const isPending = status === 'pending'\n    const isError = status === 'error'\n\n    const isLoading = isPending && isFetching\n    const hasData = data !== undefined\n\n    const result: QueryObserverBaseResult<TData, TError> = {\n      status,\n      fetchStatus: newState.fetchStatus,\n      isPending,\n      isSuccess: status === 'success',\n      isError,\n      isInitialLoading: isLoading,\n      isLoading,\n      data,\n      dataUpdatedAt: newState.dataUpdatedAt,\n      error,\n      errorUpdatedAt,\n      failureCount: newState.fetchFailureCount,\n      failureReason: newState.fetchFailureReason,\n      errorUpdateCount: newState.errorUpdateCount,\n      isFetched: newState.dataUpdateCount > 0 || newState.errorUpdateCount > 0,\n      isFetchedAfterMount:\n        newState.dataUpdateCount > queryInitialState.dataUpdateCount ||\n        newState.errorUpdateCount > queryInitialState.errorUpdateCount,\n      isFetching,\n      isRefetching: isFetching && !isPending,\n      isLoadingError: isError && !hasData,\n      isPaused: newState.fetchStatus === 'paused',\n      isPlaceholderData,\n      isRefetchError: isError && hasData,\n      isStale: isStale(query, options),\n      refetch: this.refetch,\n      promise: this.#currentThenable,\n      isEnabled: resolveEnabled(options.enabled, query) !== false,\n    }\n\n    const nextResult = result as QueryObserverResult<TData, TError>\n\n    if (this.options.experimental_prefetchInRender) {\n      const finalizeThenableIfPossible = (thenable: PendingThenable<TData>) => {\n        if (nextResult.status === 'error') {\n          thenable.reject(nextResult.error)\n        } else if (nextResult.data !== undefined) {\n          thenable.resolve(nextResult.data)\n        }\n      }\n\n      /**\n       * Create a new thenable and result promise when the results have changed\n       */\n      const recreateThenable = () => {\n        const pending =\n          (this.#currentThenable =\n          nextResult.promise =\n            pendingThenable())\n\n        finalizeThenableIfPossible(pending)\n      }\n\n      const prevThenable = this.#currentThenable\n      switch (prevThenable.status) {\n        case 'pending':\n          // Finalize the previous thenable if it was pending\n          // and we are still observing the same query\n          if (query.queryHash === prevQuery.queryHash) {\n            finalizeThenableIfPossible(prevThenable)\n          }\n          break\n        case 'fulfilled':\n          if (\n            nextResult.status === 'error' ||\n            nextResult.data !== prevThenable.value\n          ) {\n            recreateThenable()\n          }\n          break\n        case 'rejected':\n          if (\n            nextResult.status !== 'error' ||\n            nextResult.error !== prevThenable.reason\n          ) {\n            recreateThenable()\n          }\n          break\n      }\n    }\n\n    return nextResult\n  }\n\n  updateResult(): void {\n    const prevResult = this.#currentResult as\n      | QueryObserverResult<TData, TError>\n      | undefined\n\n    const nextResult = this.createResult(this.#currentQuery, this.options)\n\n    this.#currentResultState = this.#currentQuery.state\n    this.#currentResultOptions = this.options\n\n    if (this.#currentResultState.data !== undefined) {\n      this.#lastQueryWithDefinedData = this.#currentQuery\n    }\n\n    // Only notify and update result if something has changed\n    if (shallowEqualObjects(nextResult, prevResult)) {\n      return\n    }\n\n    this.#currentResult = nextResult\n\n    const shouldNotifyListeners = (): boolean => {\n      if (!prevResult) {\n        return true\n      }\n\n      const { notifyOnChangeProps } = this.options\n      const notifyOnChangePropsValue =\n        typeof notifyOnChangeProps === 'function'\n          ? notifyOnChangeProps()\n          : notifyOnChangeProps\n\n      if (\n        notifyOnChangePropsValue === 'all' ||\n        (!notifyOnChangePropsValue && !this.#trackedProps.size)\n      ) {\n        return true\n      }\n\n      const includedProps = new Set(\n        notifyOnChangePropsValue ?? this.#trackedProps,\n      )\n\n      if (this.options.throwOnError) {\n        includedProps.add('error')\n      }\n\n      return Object.keys(this.#currentResult).some((key) => {\n        const typedKey = key as keyof QueryObserverResult\n        const changed = this.#currentResult[typedKey] !== prevResult[typedKey]\n\n        return changed && includedProps.has(typedKey)\n      })\n    }\n\n    this.#notify({ listeners: shouldNotifyListeners() })\n  }\n\n  #updateQuery(): void {\n    const query = this.#client.getQueryCache().build(this.#client, this.options)\n\n    if (query === this.#currentQuery) {\n      return\n    }\n\n    const prevQuery = this.#currentQuery as\n      | Query<TQueryFnData, TError, TQueryData, TQueryKey>\n      | undefined\n    this.#currentQuery = query\n    this.#currentQueryInitialState = query.state\n\n    if (this.hasListeners()) {\n      prevQuery?.removeObserver(this)\n      query.addObserver(this)\n    }\n  }\n\n  onQueryUpdate(): void {\n    this.updateResult()\n\n    if (this.hasListeners()) {\n      this.#updateTimers()\n    }\n  }\n\n  #notify(notifyOptions: { listeners: boolean }): void {\n    notifyManager.batch(() => {\n      // First, trigger the listeners\n      if (notifyOptions.listeners) {\n        this.listeners.forEach((listener) => {\n          listener(this.#currentResult)\n        })\n      }\n\n      // Then the cache listeners\n      this.#client.getQueryCache().notify({\n        query: this.#currentQuery,\n        type: 'observerResultsUpdated',\n      })\n    })\n  }\n}\n\nfunction shouldLoadOnMount(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any>,\n): boolean {\n  return (\n    resolveEnabled(options.enabled, query) !== false &&\n    query.state.data === undefined &&\n    !(query.state.status === 'error' && options.retryOnMount === false)\n  )\n}\n\nfunction shouldFetchOnMount(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n): boolean {\n  return (\n    shouldLoadOnMount(query, options) ||\n    (query.state.data !== undefined &&\n      shouldFetchOn(query, options, options.refetchOnMount))\n  )\n}\n\nfunction shouldFetchOn(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n  field: (typeof options)['refetchOnMount'] &\n    (typeof options)['refetchOnWindowFocus'] &\n    (typeof options)['refetchOnReconnect'],\n) {\n  if (\n    resolveEnabled(options.enabled, query) !== false &&\n    resolveStaleTime(options.staleTime, query) !== 'static'\n  ) {\n    const value = typeof field === 'function' ? field(query) : field\n\n    return value === 'always' || (value !== false && isStale(query, options))\n  }\n  return false\n}\n\nfunction shouldFetchOptionally(\n  query: Query<any, any, any, any>,\n  prevQuery: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n  prevOptions: QueryObserverOptions<any, any, any, any, any>,\n): boolean {\n  return (\n    (query !== prevQuery ||\n      resolveEnabled(prevOptions.enabled, query) === false) &&\n    (!options.suspense || query.state.status !== 'error') &&\n    isStale(query, options)\n  )\n}\n\nfunction isStale(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n): boolean {\n  return (\n    resolveEnabled(options.enabled, query) !== false &&\n    query.isStaleByTime(resolveStaleTime(options.staleTime, query))\n  )\n}\n\n// this function would decide if we will update the observer's 'current'\n// properties after an optimistic reading via getOptimisticResult\nfunction shouldAssignObserverCurrentProperties<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  observer: QueryObserver<TQueryFnData, TError, TData, TQueryData, TQueryKey>,\n  optimisticResult: QueryObserverResult<TData, TError>,\n) {\n  // if the newly created result isn't what the observer is holding as current,\n  // then we'll need to update the properties as well\n  if (!shallowEqualObjects(observer.getCurrentResult(), optimisticResult)) {\n    return true\n  }\n\n  // basically, just keep previous properties if nothing changed\n  return false\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA,SAAS,oBAAoB;AAC7B,SAAS,qBAAqB;AAC9B,SAAS,kBAAkB;AAC3B,SAAS,oBAAoB;AAC7B,SAAS,uBAAuB;AAChC;;;;;;aAyCE,eACA,iJAaA,iEAAA;AAAA,mGAAA;4IAoTA,0BAA0B;;;;;;;AA1UrB,IAAM,gZAqBX,qrBA0nBA,eAAqB,6EA/oBhB,gMAMG,eAAA,CAAmD;IAkDjD,cAAoB;QAC5B,IAAA,CAAK,OAAA,GAAU,IAAA,CAAK,OAAA,CAAQ,IAAA,CAAK,IAAI;IACvC;IAEU,cAAoB;QAC5B,IAAI,IAAA,CAAK,SAAA,CAAU,IAAA,KAAS,GAAG;YAC7B,iLAAA,IAAA,EAAK,eAAc,WAAA,CAAY,IAAI;YAEnC,IAAI,oMAAmB,IAAA,EAAK,gBAAe,IAAA,CAAK,OAAO,GAAG;gBACxD,kLAAA,IAAA,iBAAK,cAAc,KAAnB,IAAA;YACF,OAAO;gBACL,IAAA,CAAK,YAAA,CAAa;YACpB;YAEA,kLAAA,IAAA,iBAAK,cAAc,KAAnB,IAAA;QACF;IACF;IAEU,gBAAsB;QAC9B,IAAI,CAAC,IAAA,CAAK,YAAA,CAAa,GAAG;YACxB,IAAA,CAAK,OAAA,CAAQ;QACf;IACF;IAEA,yBAAkC;QAChC,OAAO,+LACL,IAAA,EAAK,gBACL,IAAA,CAAK,OAAA,EACL,IAAA,CAAK,OAAA,CAAQ,kBAAA;IAEjB;IAEA,2BAAoC;QAClC,OAAO,+LACL,IAAA,EAAK,gBACL,IAAA,CAAK,OAAA,EACL,IAAA,CAAK,OAAA,CAAQ,oBAAA;IAEjB;IAEA,UAAgB;QACd,IAAA,CAAK,SAAA,GAAY,aAAA,GAAA,IAAI,IAAI;QACzB,kLAAA,IAAA,sBAAK,mBAAmB,KAAxB,IAAA;QACA,kLAAA,IAAA,yBAAK,sBAAsB,KAA3B,IAAA;QACA,iLAAA,IAAA,EAAK,eAAc,cAAA,CAAe,IAAI;IACxC;IAEA,WACE,OAAA,EAOM;QACN,MAAM,cAAc,IAAA,CAAK,OAAA;QACzB,MAAM,6LAAY,IAAA,EAAK;QAEvB,IAAA,CAAK,OAAA,oLAAU,IAAA,EAAK,SAAQ,mBAAA,CAAoB,OAAO;QAEvD,IACE,IAAA,CAAK,OAAA,CAAQ,OAAA,KAAY,KAAA,KACzB,OAAO,IAAA,CAAK,OAAA,CAAQ,OAAA,KAAY,aAChC,OAAO,IAAA,CAAK,OAAA,CAAQ,OAAA,KAAY,cAChC,sLAAO,iBAAA,EAAe,IAAA,CAAK,OAAA,CAAQ,OAAA,mLAAS,IAAA,EAAK,aAAa,OAC5D,WACF;YACA,MAAM,IAAI,MACR;QAEJ;QAEA,kLAAA,IAAA,gBAAK,aAAa,KAAlB,IAAA;QACA,iLAAA,IAAA,EAAK,eAAc,UAAA,CAAW,IAAA,CAAK,OAAO;QAE1C,IACE,YAAY,UAAA,IACZ,gLAAC,sBAAA,EAAoB,IAAA,CAAK,OAAA,EAAS,WAAW,GAC9C;YACA,iLAAA,IAAA,EAAK,SAAQ,aAAA,CAAc,EAAE,MAAA,CAAO;gBAClC,MAAM;gBACN,KAAA,mLAAO,IAAA,EAAK;gBACZ,UAAU,IAAA;YACZ,CAAC;QACH;QAEA,MAAM,UAAU,IAAA,CAAK,YAAA,CAAa;QAGlC,IACE,WACA,uMACE,IAAA,EAAK,gBACL,WACA,IAAA,CAAK,OAAA,EACL,cAEF;YACA,kLAAA,IAAA,EAAK,cAAc,oBAAnB,IAAA;QACF;QAGA,IAAA,CAAK,YAAA,CAAa;QAGlB,IACE,WAAA,kLACC,IAAA,EAAK,mBAAkB,4LACtB,iBAAA,EAAe,IAAA,CAAK,OAAA,CAAQ,OAAA,mLAAS,IAAA,EAAK,aAAa,sLACrD,iBAAA,EAAe,YAAY,OAAA,mLAAS,IAAA,EAAK,aAAa,qLACxD,mBAAA,EAAiB,IAAA,CAAK,OAAA,CAAQ,SAAA,mLAAW,IAAA,EAAK,aAAa,sLACzD,mBAAA,EAAiB,YAAY,SAAA,mLAAW,IAAA,EAAK,aAAa,EAAA,GAC9D;YACA,kLAAA,IAAA,EAAK,oBAAoB,0BAAzB,IAAA;QACF;QAEA,MAAM,4MAAsB,2BAAK,wBAAwB,KAA7B,IAAA;QAG5B,IACE,WAAA,kLACC,IAAA,EAAK,mBAAkB,2LACtB,kBAAA,EAAe,IAAA,CAAK,OAAA,CAAQ,OAAA,mLAAS,IAAA,EAAK,aAAa,qLACrD,kBAAA,EAAe,YAAY,OAAA,mLAAS,IAAA,EAAK,aAAa,MACxD,yMAAwB,IAAA,EAAK,wBAAA,GAC/B;YACA,kLAAA,IAAA,0BAAK,4BAAL,IAAA,EAA4B,mBAAmB;QACjD;IACF;IAEA,oBACE,OAAA,EAOoC;QACpC,MAAM,yLAAQ,IAAA,EAAK,SAAQ,aAAA,CAAc,EAAE,KAAA,kLAAM,IAAA,EAAK,UAAS,OAAO;QAEtE,MAAM,SAAS,IAAA,CAAK,YAAA,CAAa,OAAO,OAAO;QAE/C,IAAI,sCAAsC,IAAA,EAAM,MAAM,GAAG;mMAiBlD,gBAAiB;mMACjB,uBAAwB,IAAA,CAAK,OAAA;mMAC7B,sMAAsB,IAAA,EAAK,eAAc,KAAA;QAChD;QACA,OAAO;IACT;IAEA,mBAAuD;QACrD,wLAAO,IAAA,EAAK;IACd;IAEA,YACE,MAAA,EACA,aAAA,EACoC;QACpC,OAAO,IAAI,MAAM,QAAQ;YACvB,KAAK,CAAC,QAAQ,QAAQ;gBACpB,IAAA,CAAK,SAAA,CAAU,GAAgC;gBAC/C,0BAAA,oCAAA,cAAgB,GAAgC;gBAChD,OAAO,QAAQ,GAAA,CAAI,QAAQ,GAAG;YAChC;QACF,CAAC;IACH;IAEA,UAAU,GAAA,EAAgC;QACxC,iLAAA,IAAA,EAAK,eAAc,GAAA,CAAI,GAAG;IAC5B;IAEA,kBAAsE;QACpE,wLAAO,IAAA,EAAK;IACd;IAEA,UAEE;cAFQ,GAAG,QAAQ,CAAA,GAAb,iEAAiC,CAAC;QAGxC,OAAO,IAAA,CAAK,KAAA,CAAM;YAChB,GAAG,OAAA;QACL,CAAC;IACH;IAEA,gBACE,OAAA,EAO6C;QAC7C,MAAM,oMAAmB,IAAA,EAAK,SAAQ,mBAAA,CAAoB,OAAO;QAEjE,MAAM,yLAAQ,IAAA,EAAK,SAChB,aAAA,CAAc,EACd,KAAA,kLAAM,IAAA,EAAK,UAAS,gBAAgB;QAEvC,OAAO,MAAM,KAAA,CAAM,EAAE,IAAA,CAAK,IAAM,IAAA,CAAK,YAAA,CAAa,OAAO,gBAAgB,CAAC;IAC5E;IAEU,MACR,YAAA,EAC6C;;QAC7C,6LAAO,iBAAK,mBAAL,IAAA,EAAmB;YACxB,GAAG,YAAA;YACH,2DAA4B,aAAA,uDAAb,8BAA8B;QAC/C,CAAC,EAAE,IAAA,CAAK,MAAM;YACZ,IAAA,CAAK,YAAA,CAAa;YAClB,wLAAO,IAAA,EAAK;QACd,CAAC;IACH;IAgGU,aACR,KAAA,EACA,OAAA,EAOoC;QACpC,MAAM,6LAAY,IAAA,EAAK;QACvB,MAAM,cAAc,IAAA,CAAK,OAAA;QACzB,MAAM,8LAAa,IAAA,EAAK;QAGxB,MAAM,mMAAkB,IAAA,EAAK;QAC7B,MAAM,qMAAoB,IAAA,EAAK;QAC/B,MAAM,cAAc,UAAU;QAC9B,MAAM,oBAAoB,cACtB,MAAM,KAAA,oLACN,IAAA,EAAK;QAET,MAAM,EAAE,KAAA,CAAM,CAAA,GAAI;QAClB,IAAI,WAAW;YAAE,GAAG,KAAA;QAAM;QAC1B,IAAI,oBAAoB;QACxB,IAAI;QAGJ,IAAI,QAAQ,kBAAA,EAAoB;YAC9B,MAAM,UAAU,IAAA,CAAK,YAAA,CAAa;YAElC,MAAM,eAAe,CAAC,WAAW,mBAAmB,OAAO,OAAO;YAElE,MAAM,kBACJ,WAAW,sBAAsB,OAAO,WAAW,SAAS,WAAW;YAEzE,IAAI,gBAAgB,iBAAiB;gBACnC,WAAW;oBACT,GAAG,QAAA;oBACH,kLAAG,aAAA,EAAW,MAAM,IAAA,EAAM,MAAM,OAAO,CAAA;gBACzC;YACF;YACA,IAAI,QAAQ,kBAAA,KAAuB,eAAe;gBAChD,SAAS,WAAA,GAAc;YACzB;QACF;QAEA,IAAI,EAAE,KAAA,EAAO,cAAA,EAAgB,MAAA,CAAO,CAAA,GAAI;QAGxC,OAAO,SAAS,IAAA;QAChB,IAAI,aAAa;QAGjB,IACE,QAAQ,eAAA,KAAoB,KAAA,KAC5B,SAAS,KAAA,KACT,WAAW,WACX;YACA,IAAI;YAGJ,6DACE,WAAY,iBAAA,KACZ,QAAQ,eAAA,4EAAoB,kBAAmB,eAAA,GAC/C;gBACA,kBAAkB,WAAW,IAAA;gBAG7B,aAAa;YACf,OAAO;;gBAEL,kBACE,OAAO,QAAQ,eAAA,KAAoB,aAE7B,QAAQ,eAAA,8MAER,IAAA,EAAK,gHAA2B,KAAA,CAAM,IAAA,mLACtC,IAAA,EAAK,8BAEP,QAAQ,eAAA;YAChB;YAEA,IAAI,oBAAoB,KAAA,GAAW;gBACjC,SAAS;gBACT,sLAAO,cAAA,0DACL,WAAY,IAAA,EACZ,iBACA;gBAEF,oBAAoB;YACtB;QACF;QAIA,IAAI,QAAQ,MAAA,IAAU,SAAS,KAAA,KAAa,CAAC,YAAY;YAEvD,IACE,cACA,4EAAS,gBAAiB,IAAA,KAC1B,QAAQ,MAAA,sLAAW,IAAA,EAAK,YACxB;gBACA,wLAAO,IAAA,EAAK;YACd,OAAO;gBACL,IAAI;2MACG,WAAY,QAAQ,MAAA;oBACzB,OAAO,QAAQ,MAAA,CAAO,IAAW;oBACjC,sLAAO,cAAA,0DAAY,WAAY,IAAA,EAAM,MAAM,OAAO;2MAC7C,eAAgB;2MAChB,cAAe;gBACtB,EAAA,OAAS,aAAa;2MACf,cAAe;gBACtB;YACF;QACF;QAEA,qLAAI,IAAA,EAAK,eAAc;YACrB,yLAAQ,IAAA,EAAK;YACb,wLAAO,IAAA,EAAK;YACZ,iBAAiB,KAAK,GAAA,CAAI;YAC1B,SAAS;QACX;QAEA,MAAM,aAAa,SAAS,WAAA,KAAgB;QAC5C,MAAM,YAAY,WAAW;QAC7B,MAAM,UAAU,WAAW;QAE3B,MAAM,YAAY,aAAa;QAC/B,MAAM,UAAU,SAAS,KAAA;QAEzB,MAAM,SAAiD;YACrD;YACA,aAAa,SAAS,WAAA;YACtB;YACA,WAAW,WAAW;YACtB;YACA,kBAAkB;YAClB;YACA;YACA,eAAe,SAAS,aAAA;YACxB;YACA;YACA,cAAc,SAAS,iBAAA;YACvB,eAAe,SAAS,kBAAA;YACxB,kBAAkB,SAAS,gBAAA;YAC3B,WAAW,SAAS,eAAA,GAAkB,KAAK,SAAS,gBAAA,GAAmB;YACvE,qBACE,SAAS,eAAA,GAAkB,kBAAkB,eAAA,IAC7C,SAAS,gBAAA,GAAmB,kBAAkB,gBAAA;YAChD;YACA,cAAc,cAAc,CAAC;YAC7B,gBAAgB,WAAW,CAAC;YAC5B,UAAU,SAAS,WAAA,KAAgB;YACnC;YACA,gBAAgB,WAAW;YAC3B,SAAS,QAAQ,OAAO,OAAO;YAC/B,SAAS,IAAA,CAAK,OAAA;YACd,OAAA,mLAAS,IAAA,EAAK;YACd,yLAAW,kBAAA,EAAe,QAAQ,OAAA,EAAS,KAAK,MAAM;QACxD;QAEA,MAAM,aAAa;QAEnB,IAAI,IAAA,CAAK,OAAA,CAAQ,6BAAA,EAA+B;YAC9C,MAAM,6BAA6B,CAAC,aAAqC;gBACvE,IAAI,WAAW,MAAA,KAAW,SAAS;oBACjC,SAAS,MAAA,CAAO,WAAW,KAAK;gBAClC,OAAA,IAAW,WAAW,IAAA,KAAS,KAAA,GAAW;oBACxC,SAAS,OAAA,CAAQ,WAAW,IAAI;gBAClC;YACF;YAKA,MAAM,mBAAmB,MAAM;gBAC7B,MAAM,UACH,uLAAK,kBACN,WAAW,OAAA,qLACT,kBAAA,CAAgB;gBAEpB,2BAA2B,OAAO;YACpC;YAEA,MAAM,gMAAe,IAAA,EAAK;YAC1B,OAAQ,aAAa,MAAA,EAAQ;gBAC3B,KAAK;oBAGH,IAAI,MAAM,SAAA,KAAc,UAAU,SAAA,EAAW;wBAC3C,2BAA2B,YAAY;oBACzC;oBACA;gBACF,KAAK;oBACH,IACE,WAAW,MAAA,KAAW,WACtB,WAAW,IAAA,KAAS,aAAa,KAAA,EACjC;wBACA,iBAAiB;oBACnB;oBACA;gBACF,KAAK;oBACH,IACE,WAAW,MAAA,KAAW,WACtB,WAAW,KAAA,KAAU,aAAa,MAAA,EAClC;wBACA,iBAAiB;oBACnB;oBACA;YACJ;QACF;QAEA,OAAO;IACT;IAEA,eAAqB;QACnB,MAAM,8LAAa,IAAA,EAAK;QAIxB,MAAM,aAAa,IAAA,CAAK,YAAA,kLAAa,IAAA,EAAK,gBAAe,IAAA,CAAK,OAAO;+LAEhE,qBAAsB,qLAAA,EAAK,eAAc,KAAA;+LACzC,uBAAwB,IAAA,CAAK,OAAA;QAElC,qLAAI,IAAA,EAAK,qBAAoB,IAAA,KAAS,KAAA,GAAW;mMAC1C,4MAA4B,IAAA,EAAK;QACxC;QAGA,mLAAI,sBAAA,EAAoB,YAAY,UAAU,GAAG;YAC/C;QACF;+LAEK,gBAAiB;QAEtB,MAAM,wBAAwB,MAAe;YAC3C,IAAI,CAAC,YAAY;gBACf,OAAO;YACT;YAEA,MAAM,EAAE,mBAAA,CAAoB,CAAA,GAAI,IAAA,CAAK,OAAA;YACrC,MAAM,2BACJ,OAAO,wBAAwB,aAC3B,oBAAoB,IACpB;YAEN,IACE,6BAA6B,SAC5B,CAAC,4BAA4B,kLAAC,IAAA,EAAK,eAAc,IAAA,EAClD;gBACA,OAAO;YACT;YAEA,MAAM,gBAAgB,IAAI,+EACxB,4MAA4B,IAAA,EAAK;YAGnC,IAAI,IAAA,CAAK,OAAA,CAAQ,YAAA,EAAc;gBAC7B,cAAc,GAAA,CAAI,OAAO;YAC3B;YAEA,OAAO,OAAO,IAAA,kLAAK,IAAA,EAAK,cAAc,GAAE,IAAA,CAAK,CAAC,QAAQ;gBACpD,MAAM,WAAW;gBACjB,MAAM,2LAAU,IAAA,EAAK,eAAA,CAAe,QAAQ,CAAA,KAAM,UAAA,CAAW,QAAQ,CAAA;gBAErE,OAAO,WAAW,cAAc,GAAA,CAAI,QAAQ;YAC9C,CAAC;QACH;QAEA,kLAAA,IAAA,WAAK,aAAL,IAAA,EAAa;YAAE,WAAW,sBAAsB;QAAE,CAAC;IACrD;IAqBA,gBAAsB;QACpB,IAAA,CAAK,YAAA,CAAa;QAElB,IAAI,IAAA,CAAK,YAAA,CAAa,GAAG;YACvB,kLAAA,IAAA,iBAAK,cAAc,KAAnB,IAAA;QACF;IACF;IAzoBA,YACE,MAAA,EACO,OAAA,CAOP;QACA,KAAA,CAAM,4MAyQR,sBAA4B,g+BA6D5B,qBAA2B,4lBAxW3B;;;;;mBACoE,KAAA;;;mBACR,KAAA;oMAC5D;;mBAAqD,KAAA;;;wBACrD;;;wBACA;;;wBAOA;oMACA;;;;;;;;wBAEA;;;wBAGA;;;wBACA;;;mBACA;;;wBACA;oMACA;;mBAAgB,aAAA,GAAA,IAAI,IAA+B;;QAI1C,IAAA,CAAA,OAAA,GAAA;+LAUF,SAAU;+LACV,cAAe;+LACf,oMAAmB,kBAAA,CAAgB;QACxC,IAAI,CAAC,IAAA,CAAK,OAAA,CAAQ,6BAAA,EAA+B;YAC/C,iLAAA,IAAA,EAAK,kBAAiB,MAAA,CACpB,IAAI,MAAM,2DAA2D;QAEzE;QAEA,IAAA,CAAK,WAAA,CAAY;QACjB,IAAA,CAAK,UAAA,CAAW,OAAO;IACzB;AAooBF;AAEA,SAAS,kBACP,KAAA,EACA,OAAA,EACS;IACT,sLACE,iBAAA,EAAe,QAAQ,OAAA,EAAS,KAAK,MAAM,SAC3C,MAAM,KAAA,CAAM,IAAA,KAAS,KAAA,KACrB,CAAA,CAAE,MAAM,KAAA,CAAM,MAAA,KAAW,WAAW,QAAQ,YAAA,KAAiB,KAAA;AAEjE;AAEA,SAAS,mBACP,KAAA,EACA,OAAA,EACS;IACT,OACE,kBAAkB,OAAO,OAAO,KAC/B,MAAM,KAAA,CAAM,IAAA,KAAS,KAAA,KACpB,cAAc,OAAO,SAAS,QAAQ,cAAc;AAE1D;AAEA,SAAS,cACP,KAAA,EACA,OAAA,EACA,KAAA,EAGA;IACA,QACE,4LAAA,EAAe,QAAQ,OAAA,EAAS,KAAK,MAAM,UAC3C,iMAAA,EAAiB,QAAQ,SAAA,EAAW,KAAK,MAAM,UAC/C;QACA,MAAM,QAAQ,OAAO,UAAU,aAAa,MAAM,KAAK,IAAI;QAE3D,OAAO,UAAU,YAAa,UAAU,SAAS,QAAQ,OAAO,OAAO;IACzE;IACA,OAAO;AACT;AAEA,SAAS,sBACP,KAAA,EACA,SAAA,EACA,OAAA,EACA,WAAA,EACS;IACT,OAAA,CACG,UAAU,4LACT,iBAAA,EAAe,YAAY,OAAA,EAAS,KAAK,MAAM,KAAA,KAAA,CAChD,CAAC,QAAQ,QAAA,IAAY,MAAM,KAAA,CAAM,MAAA,KAAW,OAAA,KAC7C,QAAQ,OAAO,OAAO;AAE1B;AAEA,SAAS,QACP,KAAA,EACA,OAAA,EACS;IACT,QACE,+LAAA,EAAe,QAAQ,OAAA,EAAS,KAAK,MAAM,SAC3C,MAAM,aAAA,gLAAc,mBAAA,EAAiB,QAAQ,SAAA,EAAW,KAAK,CAAC;AAElE;AAIA,SAAS,sCAOP,QAAA,EACA,gBAAA,EACA;IAGA,IAAI,gLAAC,sBAAA,EAAoB,SAAS,gBAAA,CAAiB,GAAG,gBAAgB,GAAG;QACvE,OAAO;IACT;IAGA,OAAO;AACT;;SAjfE,aACE,YAAA,EACiC;IAEjC,kLAAA,IAAA,EAAK,aAAa,mBAAlB,IAAA;IAGA,IAAI,2LAA2C,IAAA,EAAK,eAAc,KAAA,CAChE,IAAA,CAAK,OAAA,EACL;IAGF,IAAI,8DAAC,aAAc,YAAA,GAAc;QAC/B,UAAU,QAAQ,KAAA,4KAAM,OAAI;IAC9B;IAEA,OAAO;AACT;;IAGE,kLAAA,IAAA,sBAAK,mBAAmB,KAAxB,IAAA;IACA,MAAM,gBAAY,8LAAA,EAChB,IAAA,CAAK,OAAA,CAAQ,SAAA,mLACb,IAAA,EAAK;IAGP,+KAAI,WAAA,IAAY,qLAAA,EAAK,gBAAe,OAAA,IAAW,gLAAC,iBAAA,EAAe,SAAS,GAAG;QACzE;IACF;IAEA,MAAM,WAAO,4LAAA,mLAAe,IAAA,EAAK,gBAAe,aAAA,EAAe,SAAS;IAIxE,MAAM,UAAU,OAAO;2LAElB,iBAAkB,WAAW,MAAM;QACtC,IAAI,kLAAC,IAAA,EAAK,gBAAe,OAAA,EAAS;YAChC,IAAA,CAAK,YAAA,CAAa;QACpB;IACF,GAAG,OAAO;AACZ;;QAGE;IAAA,OAAA,CAAA,OACG,OAAO,IAAA,CAAK,OAAA,CAAQ,eAAA,KAAoB,aACrC,IAAA,CAAK,OAAA,CAAQ,eAAA,kLAAgB,IAAA,EAAK,aAAa,KAC/C,IAAA,CAAK,OAAA,CAAQ,eAAA,cAHnB,kBAAA,OAGuC;AAEzC;SAEA,sBAAuB,YAAA,EAAoC;IACzD,kLAAA,IAAA,yBAAK,sBAAsB,KAA3B,IAAA;2LAEK,yBAA0B;IAE/B,+KACE,WAAA,KACA,+LAAA,EAAe,IAAA,CAAK,OAAA,CAAQ,OAAA,mLAAS,IAAA,EAAK,aAAa,OAAM,SAC7D,gLAAC,iBAAA,mLAAe,IAAA,EAAK,uBAAuB,uLAC5C,IAAA,EAAK,6BAA4B,GACjC;QACA;IACF;2LAEK,oBAAqB,YAAY,MAAM;QAC1C,IACE,IAAA,CAAK,OAAA,CAAQ,2BAAA,sLACb,eAAA,CAAa,SAAA,CAAU,GACvB;YACA,kLAAA,IAAA,iBAAK,cAAc,KAAnB,IAAA;QACF;IACF,oLAAG,IAAA,EAAK,uBAAuB;AACjC;SAEA,gBAAsB;IACpB,kLAAA,IAAA,uBAAK,oBAAoB,KAAzB,IAAA;IACA,kLAAA,IAAA,0BAAK,4BAAL,IAAA,wLAA4B,2BAAK,wBAAwB,CAAC,IAA9B,IAAA;AAC9B;;IAGE,qLAAI,IAAA,EAAK,kBAAiB;QACxB,8LAAa,IAAA,EAAK,eAAe;+LAC5B,iBAAkB,KAAA;IACzB;AACF;SAEA,wBAA8B;IAC5B,qLAAI,IAAA,EAAK,qBAAoB;QAC3B,+LAAc,IAAA,EAAK,kBAAkB;+LAChC,oBAAqB,KAAA;IAC5B;AACF;;IAqRE,MAAM,yLAAQ,IAAA,EAAK,SAAQ,aAAA,CAAc,EAAE,KAAA,kLAAM,IAAA,EAAK,UAAS,IAAA,CAAK,OAAO;IAE3E,IAAI,2LAAU,IAAA,EAAK,gBAAe;QAChC;IACF;IAEA,MAAM,6LAAY,IAAA,EAAK;2LAGlB,eAAgB;2LAChB,2BAA4B,MAAM,KAAA;IAEvC,IAAI,IAAA,CAAK,YAAA,CAAa,GAAG;QACvB,sBAAA,gCAAA,UAAW,cAAA,CAAe,IAAI;QAC9B,MAAM,WAAA,CAAY,IAAI;IACxB;AACF;SAUA,OAAQ,aAAA,EAA6C;IACnD,kLAAA,CAAA,gBAAA,CAAc,KAAA,CAAM,MAAM;QAExB,IAAI,cAAc,SAAA,EAAW;YAC3B,IAAA,CAAK,SAAA,CAAU,OAAA,CAAQ,CAAC,aAAa;gBACnC,0LAAS,IAAA,EAAK,cAAc;YAC9B,CAAC;QACH;QAGA,iLAAA,IAAA,EAAK,SAAQ,aAAA,CAAc,EAAE,MAAA,CAAO;YAClC,KAAA,mLAAO,IAAA,EAAK;YACZ,MAAM;QACR,CAAC;IACH,CAAC;AACH", "debugId": null}}, {"offset": {"line": 1290, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/project%20food-order%206k/food-ordering-system-modern/frontend/node_modules/%40tanstack/react-query/src/QueryErrorResetBoundary.tsx"], "sourcesContent": ["'use client'\nimport * as React from 'react'\n\n// CONTEXT\nexport type QueryErrorResetFunction = () => void\nexport type QueryErrorIsResetFunction = () => boolean\nexport type QueryErrorClearResetFunction = () => void\n\nexport interface QueryErrorResetBoundaryValue {\n  clearReset: QueryErrorClearResetFunction\n  isReset: QueryErrorIsResetFunction\n  reset: QueryErrorResetFunction\n}\n\nfunction createValue(): QueryErrorResetBoundaryValue {\n  let isReset = false\n  return {\n    clearReset: () => {\n      isReset = false\n    },\n    reset: () => {\n      isReset = true\n    },\n    isReset: () => {\n      return isReset\n    },\n  }\n}\n\nconst QueryErrorResetBoundaryContext = React.createContext(createValue())\n\n// HOOK\n\nexport const useQueryErrorResetBoundary = () =>\n  React.useContext(QueryErrorResetBoundaryContext)\n\n// COMPONENT\n\nexport type QueryErrorResetBoundaryFunction = (\n  value: QueryErrorResetBoundaryValue,\n) => React.ReactNode\n\nexport interface QueryErrorResetBoundaryProps {\n  children: QueryErrorResetBoundaryFunction | React.ReactNode\n}\n\nexport const QueryErrorResetBoundary = ({\n  children,\n}: QueryErrorResetBoundaryProps) => {\n  const [value] = React.useState(() => createValue())\n  return (\n    <QueryErrorResetBoundaryContext.Provider value={value}>\n      {typeof children === 'function' ? children(value) : children}\n    </QueryErrorResetBoundaryContext.Provider>\n  )\n}\n"], "names": [], "mappings": ";;;;;AACA,YAAY,WAAW;AAkDnB;;;;AArCJ,SAAS,cAA4C;IACnD,IAAI,UAAU;IACd,OAAO;QACL,YAAY,MAAM;YAChB,UAAU;QACZ;QACA,OAAO,MAAM;YACX,UAAU;QACZ;QACA,SAAS,MAAM;YACb,OAAO;QACT;IACF;AACF;AAEA,IAAM,+LAAuC,gBAAA,CAAc,YAAY,CAAC;AAIjE,IAAM,6BAA6B,kKAClC,aAAA,CAAW,8BAA8B;AAY1C,IAAM,0BAA0B;QAAC,EACtC,QAAA,EACF,KAAoC;IAClC,MAAM,CAAC,KAAK,CAAA,iKAAU,WAAA;4CAAS,IAAM,YAAY,CAAC;;IAClD,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,+BAA+B,QAAA,EAA/B;QAAwC;QACtC,UAAA,OAAO,aAAa,aAAa,SAAS,KAAK,IAAI;IAAA,CACtD;AAEJ", "debugId": null}}, {"offset": {"line": 1332, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/project%20food-order%206k/food-ordering-system-modern/frontend/node_modules/%40tanstack/react-query/src/errorBoundaryUtils.ts"], "sourcesContent": ["'use client'\nimport * as React from 'react'\nimport { shouldThrowError } from '@tanstack/query-core'\nimport type {\n  DefaultedQueryObserverOptions,\n  Query,\n  QueryKey,\n  QueryObserverResult,\n  ThrowOnError,\n} from '@tanstack/query-core'\nimport type { QueryErrorResetBoundaryValue } from './QueryErrorResetBoundary'\n\nexport const ensurePreventErrorBoundaryRetry = <\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryData,\n  TQueryKey extends QueryKey,\n>(\n  options: DefaultedQueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >,\n  errorResetBoundary: QueryErrorResetBoundaryValue,\n) => {\n  if (\n    options.suspense ||\n    options.throwOnError ||\n    options.experimental_prefetchInRender\n  ) {\n    // Prevent retrying failed query if the error boundary has not been reset yet\n    if (!errorResetBoundary.isReset()) {\n      options.retryOnMount = false\n    }\n  }\n}\n\nexport const useClearResetErrorBoundary = (\n  errorResetBoundary: QueryErrorResetBoundaryValue,\n) => {\n  React.useEffect(() => {\n    errorResetBoundary.clearReset()\n  }, [errorResetBoundary])\n}\n\nexport const getHasError = <\n  TData,\n  TError,\n  TQueryFnData,\n  TQueryData,\n  TQueryKey extends QueryKey,\n>({\n  result,\n  errorResetBoundary,\n  throwOnError,\n  query,\n  suspense,\n}: {\n  result: QueryObserverResult<TData, TError>\n  errorResetBoundary: QueryErrorResetBoundaryValue\n  throwOnError: ThrowOnError<TQueryFnData, TError, TQueryData, TQueryKey>\n  query: Query<TQueryFnData, TError, TQueryData, TQueryKey> | undefined\n  suspense: boolean | undefined\n}) => {\n  return (\n    result.isError &&\n    !errorResetBoundary.isReset() &&\n    !result.isFetching &&\n    query &&\n    ((suspense && result.data === undefined) ||\n      shouldThrowError(throwOnError, [result.error, query]))\n  )\n}\n"], "names": [], "mappings": ";;;;;;AACA,YAAY,WAAW;AACvB,SAAS,wBAAwB;;;;AAU1B,IAAM,kCAAkC,CAO7C,SAOA,uBACG;IACH,IACE,QAAQ,QAAA,IACR,QAAQ,YAAA,IACR,QAAQ,6BAAA,EACR;QAEA,IAAI,CAAC,mBAAmB,OAAA,CAAQ,GAAG;YACjC,QAAQ,YAAA,GAAe;QACzB;IACF;AACF;AAEO,IAAM,6BAA6B,CACxC,uBACG;kKACG,YAAA;gDAAU,MAAM;YACpB,mBAAmB,UAAA,CAAW;QAChC;+CAAG;QAAC,kBAAkB;KAAC;AACzB;AAEO,IAAM,cAAc;QAMzB,EACA,MAAA,EACA,kBAAA,EACA,YAAA,EACA,KAAA,EACA,QAAA,EACF,KAMM;IACJ,OACE,OAAO,OAAA,IACP,CAAC,mBAAmB,OAAA,CAAQ,KAC5B,CAAC,OAAO,UAAA,IACR,SAAA,CACE,YAAY,OAAO,IAAA,KAAS,KAAA,oLAC5B,mBAAA,EAAiB,cAAc;QAAC,OAAO,KAAA;QAAO,KAAK;KAAC,CAAA;AAE1D", "debugId": null}}, {"offset": {"line": 1372, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/project%20food-order%206k/food-ordering-system-modern/frontend/node_modules/%40tanstack/react-query/src/IsRestoringProvider.ts"], "sourcesContent": ["'use client'\nimport * as React from 'react'\n\nconst IsRestoringContext = React.createContext(false)\n\nexport const useIsRestoring = () => React.useContext(IsRestoringContext)\nexport const IsRestoringProvider = IsRestoringContext.Provider\n"], "names": [], "mappings": ";;;;;AACA,YAAY,WAAW;;;AAEvB,IAAM,mLAA2B,gBAAA,CAAc,KAAK;AAE7C,IAAM,iBAAiB,kKAAY,aAAA,CAAW,kBAAkB;AAChE,IAAM,sBAAsB,mBAAmB,QAAA", "debugId": null}}, {"offset": {"line": 1389, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/project%20food-order%206k/food-ordering-system-modern/frontend/node_modules/%40tanstack/react-query/src/suspense.ts"], "sourcesContent": ["import type {\n  DefaultError,\n  DefaultedQueryObserverOptions,\n  Query,\n  QueryKey,\n  QueryObserver,\n  QueryObserverResult,\n} from '@tanstack/query-core'\nimport type { QueryErrorResetBoundaryValue } from './QueryErrorResetBoundary'\n\nexport const defaultThrowOnError = <\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  _error: TError,\n  query: Query<TQueryFnData, TError, TData, TQueryKey>,\n) => query.state.data === undefined\n\nexport const ensureSuspenseTimers = (\n  defaultedOptions: DefaultedQueryObserverOptions<any, any, any, any, any>,\n) => {\n  if (defaultedOptions.suspense) {\n    // Handle staleTime to ensure minimum 1000ms in Suspense mode\n    // This prevents unnecessary refetching when components remount after suspending\n\n    const clamp = (value: number | 'static' | undefined) =>\n      value === 'static' ? value : Math.max(value ?? 1000, 1000)\n\n    const originalStaleTime = defaultedOptions.staleTime\n    defaultedOptions.staleTime =\n      typeof originalStaleTime === 'function'\n        ? (...args) => clamp(originalStaleTime(...args))\n        : clamp(originalStaleTime)\n\n    if (typeof defaultedOptions.gcTime === 'number') {\n      defaultedOptions.gcTime = Math.max(defaultedOptions.gcTime, 1000)\n    }\n  }\n}\n\nexport const willFetch = (\n  result: QueryObserverResult<any, any>,\n  isRestoring: boolean,\n) => result.isLoading && result.isFetching && !isRestoring\n\nexport const shouldSuspend = (\n  defaultedOptions:\n    | DefaultedQueryObserverOptions<any, any, any, any, any>\n    | undefined,\n  result: QueryObserverResult<any, any>,\n) => defaultedOptions?.suspense && result.isPending\n\nexport const fetchOptimistic = <\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryData,\n  TQueryKey extends QueryKey,\n>(\n  defaultedOptions: DefaultedQueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >,\n  observer: QueryObserver<TQueryFnData, TError, TData, TQueryData, TQueryKey>,\n  errorResetBoundary: QueryErrorResetBoundaryValue,\n) =>\n  observer.fetchOptimistic(defaultedOptions).catch(() => {\n    errorResetBoundary.clearReset()\n  })\n"], "names": [], "mappings": ";;;;;;;;AAUO,IAAM,sBAAsB,CAMjC,QACA,QACG,MAAM,KAAA,CAAM,IAAA,KAAS,KAAA;AAEnB,IAAM,uBAAuB,CAClC,qBACG;IACH,IAAI,iBAAiB,QAAA,EAAU;QAI7B,MAAM,QAAQ,CAAC,QACb,UAAU,WAAW,QAAQ,KAAK,GAAA,sCAAI,QAAS,KAAM,GAAI;QAE3D,MAAM,oBAAoB,iBAAiB,SAAA;QAC3C,iBAAiB,SAAA,GACf,OAAO,sBAAsB,aACzB;;gBAAI;;mBAAS,MAAM,kBAAkB,GAAG,IAAI,CAAC;YAC7C,MAAM,iBAAiB;QAE7B,IAAI,OAAO,iBAAiB,MAAA,KAAW,UAAU;YAC/C,iBAAiB,MAAA,GAAS,KAAK,GAAA,CAAI,iBAAiB,MAAA,EAAQ,GAAI;QAClE;IACF;AACF;AAEO,IAAM,YAAY,CACvB,QACA,cACG,OAAO,SAAA,IAAa,OAAO,UAAA,IAAc,CAAC;AAExC,IAAM,gBAAgB,CAC3B,kBAGA,8EACG,iBAAkB,QAAA,KAAY,OAAO,SAAA;AAEnC,IAAM,kBAAkB,CAO7B,kBAOA,UACA,qBAEA,SAAS,eAAA,CAAgB,gBAAgB,EAAE,KAAA,CAAM,MAAM;QACrD,mBAAmB,UAAA,CAAW;IAChC,CAAC", "debugId": null}}, {"offset": {"line": 1424, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/project%20food-order%206k/food-ordering-system-modern/frontend/node_modules/%40tanstack/react-query/src/useBaseQuery.ts"], "sourcesContent": ["'use client'\nimport * as React from 'react'\n\nimport { isServer, noop, notifyManager } from '@tanstack/query-core'\nimport { useQueryClient } from './QueryClientProvider'\nimport { useQueryErrorResetBoundary } from './QueryErrorResetBoundary'\nimport {\n  ensurePreventErrorBoundaryRetry,\n  getHasError,\n  useClearResetErrorBoundary,\n} from './errorBoundaryUtils'\nimport { useIsRestoring } from './IsRestoringProvider'\nimport {\n  ensureSuspenseTimers,\n  fetchOptimistic,\n  shouldSuspend,\n  willFetch,\n} from './suspense'\nimport type {\n  QueryClient,\n  QueryKey,\n  QueryObserver,\n  QueryObserverResult,\n} from '@tanstack/query-core'\nimport type { UseBaseQueryOptions } from './types'\n\nexport function useBaseQuery<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryData,\n  <PERSON><PERSON><PERSON>y<PERSON>ey extends QueryKey,\n>(\n  options: UseBaseQueryOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >,\n  Observer: typeof QueryObserver,\n  queryClient?: QueryClient,\n): QueryObserverResult<TData, TError> {\n  if (process.env.NODE_ENV !== 'production') {\n    if (typeof options !== 'object' || Array.isArray(options)) {\n      throw new Error(\n        'Bad argument type. Starting with v5, only the \"Object\" form is allowed when calling query related functions. Please use the error stack to find the culprit call. More info here: https://tanstack.com/query/latest/docs/react/guides/migrating-to-v5#supports-a-single-signature-one-object',\n      )\n    }\n  }\n\n  const isRestoring = useIsRestoring()\n  const errorResetBoundary = useQueryErrorResetBoundary()\n  const client = useQueryClient(queryClient)\n  const defaultedOptions = client.defaultQueryOptions(options)\n\n  ;(client.getDefaultOptions().queries as any)?._experimental_beforeQuery?.(\n    defaultedOptions,\n  )\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (!defaultedOptions.queryFn) {\n      console.error(\n        `[${defaultedOptions.queryHash}]: No queryFn was passed as an option, and no default queryFn was found. The queryFn parameter is only optional when using a default queryFn. More info here: https://tanstack.com/query/latest/docs/framework/react/guides/default-query-function`,\n      )\n    }\n  }\n\n  // Make sure results are optimistically set in fetching state before subscribing or updating options\n  defaultedOptions._optimisticResults = isRestoring\n    ? 'isRestoring'\n    : 'optimistic'\n\n  ensureSuspenseTimers(defaultedOptions)\n  ensurePreventErrorBoundaryRetry(defaultedOptions, errorResetBoundary)\n\n  useClearResetErrorBoundary(errorResetBoundary)\n\n  // this needs to be invoked before creating the Observer because that can create a cache entry\n  const isNewCacheEntry = !client\n    .getQueryCache()\n    .get(defaultedOptions.queryHash)\n\n  const [observer] = React.useState(\n    () =>\n      new Observer<TQueryFnData, TError, TData, TQueryData, TQueryKey>(\n        client,\n        defaultedOptions,\n      ),\n  )\n\n  // note: this must be called before useSyncExternalStore\n  const result = observer.getOptimisticResult(defaultedOptions)\n\n  const shouldSubscribe = !isRestoring && options.subscribed !== false\n  React.useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) => {\n        const unsubscribe = shouldSubscribe\n          ? observer.subscribe(notifyManager.batchCalls(onStoreChange))\n          : noop\n\n        // Update result to make sure we did not miss any query updates\n        // between creating the observer and subscribing to it.\n        observer.updateResult()\n\n        return unsubscribe\n      },\n      [observer, shouldSubscribe],\n    ),\n    () => observer.getCurrentResult(),\n    () => observer.getCurrentResult(),\n  )\n\n  React.useEffect(() => {\n    observer.setOptions(defaultedOptions)\n  }, [defaultedOptions, observer])\n\n  // Handle suspense\n  if (shouldSuspend(defaultedOptions, result)) {\n    throw fetchOptimistic(defaultedOptions, observer, errorResetBoundary)\n  }\n\n  // Handle error boundary\n  if (\n    getHasError({\n      result,\n      errorResetBoundary,\n      throwOnError: defaultedOptions.throwOnError,\n      query: client\n        .getQueryCache()\n        .get<\n          TQueryFnData,\n          TError,\n          TQueryData,\n          TQueryKey\n        >(defaultedOptions.queryHash),\n      suspense: defaultedOptions.suspense,\n    })\n  ) {\n    throw result.error\n  }\n\n  ;(client.getDefaultOptions().queries as any)?._experimental_afterQuery?.(\n    defaultedOptions,\n    result,\n  )\n\n  if (\n    defaultedOptions.experimental_prefetchInRender &&\n    !isServer &&\n    willFetch(result, isRestoring)\n  ) {\n    const promise = isNewCacheEntry\n      ? // Fetch immediately on render in order to ensure `.promise` is resolved even if the component is unmounted\n        fetchOptimistic(defaultedOptions, observer, errorResetBoundary)\n      : // subscribe to the \"cache promise\" so that we can finalize the currentThenable once data comes in\n        client.getQueryCache().get(defaultedOptions.queryHash)?.promise\n\n    promise?.catch(noop).finally(() => {\n      // `.updateResult()` will trigger `.#currentThenable` to finalize\n      observer.updateResult()\n    })\n  }\n\n  // Handle result property usage tracking\n  return !defaultedOptions.notifyOnChangeProps\n    ? observer.trackResult(result)\n    : result\n}\n"], "names": [], "mappings": ";;;AA2CM,QAAQ,IAAI,aAAa;;AA1C/B,YAAY,WAAW;AAEvB,SAAS,UAAU,MAAM,qBAAqB;;AAC9C,SAAS,sBAAsB;AAC/B,SAAS,kCAAkC;AAC3C;AAKA,SAAS,sBAAsB;AAC/B;;;;;;;;;AAcO,SAAS,aAOd,OAAA,EAOA,QAAA,EACA,WAAA,EACoC;QAclC,4JAuFA;IApGF,wCAA2C;QACzC,IAAI,OAAO,YAAY,YAAY,MAAM,OAAA,CAAQ,OAAO,GAAG;YACzD,MAAM,IAAI,MACR;QAEJ;IACF;IAEA,MAAM,cAAc,+MAAA,CAAe;IACnC,MAAM,uNAAqB,6BAAA,CAA2B;IACtD,MAAM,uMAAS,iBAAA,EAAe,WAAW;IACzC,MAAM,mBAAmB,OAAO,mBAAA,CAAoB,OAAO;gDAElD,iBAAA,CAAkB,EAAE,OAAA,uKAAiB,yBAAA,oMAC5C;IAGF,IAAI,QAAQ,IAAI,aAAa,WAAc;QACzC,IAAI,CAAC,iBAAiB,OAAA,EAAS;YAC7B,QAAQ,KAAA,CACN,IAA8B,OAA1B,iBAAiB,SAAS,EAAA;QAElC;IACF;IAGA,iBAAiB,kBAAA,GAAqB,cAClC,gBACA;IAEJ,CAAA,GAAA,8KAAA,CAAA,uBAAA,EAAqB,gBAAgB;IACrC,CAAA,GAAA,wLAAA,CAAA,kCAAA,EAAgC,kBAAkB,kBAAkB;IAEpE,CAAA,GAAA,wLAAA,CAAA,6BAAA,EAA2B,kBAAkB;IAG7C,MAAM,kBAAkB,CAAC,OACtB,aAAA,CAAc,EACd,GAAA,CAAI,iBAAiB,SAAS;IAEjC,MAAM,CAAC,QAAQ,CAAA,iKAAU,WAAA;iCACvB,IACE,IAAI,SACF,QACA;;IAKN,MAAM,SAAS,SAAS,mBAAA,CAAoB,gBAAgB;IAE5D,MAAM,kBAAkB,CAAC,eAAe,QAAQ,UAAA,KAAe;IACzD,qLAAA,+JACE,cAAA;yDACJ,CAAC,kBAAkB;YACjB,MAAM,cAAc,kBAChB,SAAS,SAAA,oLAAU,gBAAA,CAAc,UAAA,CAAW,aAAa,CAAC,+KAC1D,OAAA;YAIJ,SAAS,YAAA,CAAa;YAEtB,OAAO;QACT;wDACA;QAAC;QAAU,eAAe;KAAA;6CAE5B,IAAM,SAAS,gBAAA,CAAiB;;6CAChC,IAAM,SAAS,gBAAA,CAAiB;;kKAG5B,YAAA;kCAAU,MAAM;YACpB,SAAS,UAAA,CAAW,gBAAgB;QACtC;iCAAG;QAAC;QAAkB,QAAQ;KAAC;IAG/B,uLAAI,gBAAA,EAAc,kBAAkB,MAAM,GAAG;QAC3C,yLAAM,kBAAA,EAAgB,kBAAkB,UAAU,kBAAkB;IACtE;IAGA,iMACE,cAAA,EAAY;QACV;QACA;QACA,cAAc,iBAAiB,YAAA;QAC/B,OAAO,OACJ,aAAA,CAAc,EACd,GAAA,CAKC,iBAAiB,SAAS;QAC9B,UAAU,iBAAiB,QAAA;IAC7B,CAAC,GACD;QACA,MAAM,OAAO,KAAA;IACf;;iDAES,iBAAA,CAAkB,EAAE,OAAA,wKAAiB,wBAAA,mMAC5C,kBACA;IAGF,IACE,iBAAiB,6BAAA,IACjB,4KAAC,WAAA,uLACD,YAAA,EAAU,QAAQ,WAAW,GAC7B;YAGkE,kGAAA;;QAFlE,MAAM,UAAU,kBAAA,2GAAA;2LAEZ,kBAAA,EAAgB,kBAAkB,UAAU,kBAAkB,IAAA,oCAEvD,aAAA,CAAc,EAAE,GAAA,CAAI,iBAAiB,SAAS,CAAG,8DAAxD,0BAAwD,OAAA;QAE5D,oBAAA,8BAAA,QAAS,KAAA,4KAAM,OAAI,EAAE,OAAA,CAAQ,MAAM;YAEjC,SAAS,YAAA,CAAa;QACxB,CAAC;IACH;IAGA,OAAO,CAAC,iBAAiB,mBAAA,GACrB,SAAS,WAAA,CAAY,MAAM,IAC3B;AACN", "debugId": null}}, {"offset": {"line": 1525, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/project%20food-order%206k/food-ordering-system-modern/frontend/node_modules/%40tanstack/react-query/src/useQuery.ts"], "sourcesContent": ["'use client'\nimport { QueryObserver } from '@tanstack/query-core'\nimport { useBaseQuery } from './useBaseQuery'\nimport type {\n  DefaultError,\n  NoInfer,\n  QueryClient,\n  QueryKey,\n} from '@tanstack/query-core'\nimport type {\n  DefinedUseQueryResult,\n  UseQueryOptions,\n  UseQueryResult,\n} from './types'\nimport type {\n  DefinedInitialDataOptions,\n  UndefinedInitialDataOptions,\n} from './queryOptions'\n\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: DefinedInitialDataOptions<TQueryFnData, TError, TData, TQueryKey>,\n  queryClient?: QueryClient,\n): DefinedUseQueryResult<NoInfer<TData>, TError>\n\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  T<PERSON><PERSON><PERSON><PERSON><PERSON> extends QueryKey = QueryKey,\n>(\n  options: UndefinedInitialDataOptions<TQueryFnData, TError, TData, TQueryKey>,\n  queryClient?: QueryClient,\n): UseQueryResult<NoInfer<TData>, TError>\n\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  queryClient?: QueryClient,\n): UseQueryResult<NoInfer<TData>, TError>\n\nexport function useQuery(options: UseQueryOptions, queryClient?: QueryClient) {\n  return useBaseQuery(options, QueryObserver, queryClient)\n}\n"], "names": [], "mappings": ";;;;AACA,SAAS,qBAAqB;AAC9B,SAAS,oBAAoB;;;;AA+CtB,SAAS,SAAS,OAAA,EAA0B,WAAA,EAA2B;IAC5E,8LAAO,eAAA,EAAa,4LAAS,gBAAA,EAAe,WAAW;AACzD", "debugId": null}}]}