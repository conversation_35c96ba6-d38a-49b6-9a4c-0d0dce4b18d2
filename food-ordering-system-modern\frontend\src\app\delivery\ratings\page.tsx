'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { useQuery } from '@tanstack/react-query';
import { 
  Star,
  TrendingUp,
  MessageCircle,
  Calendar,
  Package,
  Filter,
  Search
} from 'lucide-react';
import DeliverySidebar from '@/components/DeliverySidebar';
import { deliveryAPI } from '@/lib/api';

const DeliveryRatingsPage = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [ratingFilter, setRatingFilter] = useState('all');

  // Mock ratings data
  const mockRatings = {
    averageRating: 4.7,
    totalRatings: 156,
    ratingDistribution: {
      5: 89,
      4: 45,
      3: 15,
      2: 5,
      1: 2
    },
    recentReviews: [
      {
        id: 'REV001',
        orderNumber: '#12345',
        customer: 'John <PERSON>',
        rating: 5,
        comment: 'Excellent delivery service! Very fast and professional.',
        date: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        restaurant: 'Spice Garden'
      },
      {
        id: 'REV002',
        orderNumber: '#12346',
        customer: '<PERSON>',
        rating: 4,
        comment: 'Good delivery, food arrived hot and on time.',
        date: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(),
        restaurant: 'Pizza Palace'
      },
      {
        id: 'REV003',
        orderNumber: '#12347',
        customer: 'Mike Johnson',
        rating: 5,
        comment: 'Amazing delivery partner! Very courteous and quick.',
        date: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
        restaurant: 'Burger Hub'
      },
      {
        id: 'REV004',
        orderNumber: '#12348',
        customer: 'Emily Davis',
        rating: 4,
        comment: 'Professional service, delivered exactly on time.',
        date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        restaurant: 'Sushi Express'
      },
      {
        id: 'REV005',
        orderNumber: '#12349',
        customer: 'Alex Wilson',
        rating: 3,
        comment: 'Delivery was okay, could be faster.',
        date: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
        restaurant: 'Thai Garden'
      }
    ]
  };

  const ratings = mockRatings;

  const filteredReviews = ratings.recentReviews.filter(review => {
    const matchesRating = ratingFilter === 'all' || review.rating.toString() === ratingFilter;
    const matchesSearch = !searchTerm || 
      review.comment.toLowerCase().includes(searchTerm.toLowerCase()) ||
      review.customer.toLowerCase().includes(searchTerm.toLowerCase()) ||
      review.orderNumber.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesRating && matchesSearch;
  });

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Less than an hour ago';
    if (diffInHours < 24) return `${diffInHours} hours ago`;
    return `${Math.floor(diffInHours / 24)} days ago`;
  };

  const getRatingPercentage = (rating: number) => {
    return (ratings.ratingDistribution[rating] / ratings.totalRatings) * 100;
  };

  return (
    <div className="min-h-screen bg-gray-50 flex">
      <DeliverySidebar />
      
      <div className="flex-1 p-8">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
                <Star className="w-5 h-5 text-yellow-600" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Ratings & Reviews</h1>
                <p className="text-gray-600">View customer feedback and ratings</p>
              </div>
            </div>
          </div>

          {/* Rating Overview */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
            {/* Overall Rating */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white rounded-2xl p-6 shadow-md text-center"
            >
              <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Star className="w-8 h-8 text-yellow-600 fill-current" />
              </div>
              <h3 className="text-3xl font-bold text-gray-900 mb-2">{ratings.averageRating}</h3>
              <div className="flex items-center justify-center gap-1 mb-2">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`w-5 h-5 ${
                      i < Math.floor(ratings.averageRating) 
                        ? 'text-yellow-400 fill-current' 
                        : 'text-gray-300'
                    }`}
                  />
                ))}
              </div>
              <p className="text-gray-600">Based on {ratings.totalRatings} reviews</p>
            </motion.div>

            {/* Rating Distribution */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="bg-white rounded-2xl p-6 shadow-md"
            >
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Rating Distribution</h3>
              <div className="space-y-3">
                {[5, 4, 3, 2, 1].map((rating) => (
                  <div key={rating} className="flex items-center gap-3">
                    <div className="flex items-center gap-1 w-12">
                      <span className="text-sm font-medium text-gray-700">{rating}</span>
                      <Star className="w-4 h-4 text-yellow-400 fill-current" />
                    </div>
                    <div className="flex-1 bg-gray-200 rounded-full h-2">
                      <motion.div
                        initial={{ width: 0 }}
                        animate={{ width: `${getRatingPercentage(rating)}%` }}
                        transition={{ duration: 0.8, delay: rating * 0.1 }}
                        className="bg-yellow-400 h-2 rounded-full"
                      />
                    </div>
                    <span className="text-sm text-gray-600 w-8">
                      {ratings.ratingDistribution[rating]}
                    </span>
                  </div>
                ))}
              </div>
            </motion.div>

            {/* Performance Stats */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-white rounded-2xl p-6 shadow-md"
            >
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Performance</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <TrendingUp className="w-5 h-5 text-green-500" />
                    <span className="text-gray-700">Rating Trend</span>
                  </div>
                  <span className="text-green-600 font-semibold">+0.2 this week</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Package className="w-5 h-5 text-blue-500" />
                    <span className="text-gray-700">Rated Deliveries</span>
                  </div>
                  <span className="text-gray-900 font-semibold">78%</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <MessageCircle className="w-5 h-5 text-purple-500" />
                    <span className="text-gray-700">With Comments</span>
                  </div>
                  <span className="text-gray-900 font-semibold">65%</span>
                </div>
              </div>
            </motion.div>
          </div>

          {/* Filters */}
          <div className="bg-white rounded-2xl p-6 shadow-md mb-6">
            <div className="flex flex-col md:flex-row gap-4">
              {/* Search */}
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    type="text"
                    placeholder="Search reviews by customer, order, or comment..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
                  />
                </div>
              </div>

              {/* Rating Filter */}
              <div className="flex items-center gap-2">
                <Filter className="w-5 h-5 text-gray-400" />
                <select
                  value={ratingFilter}
                  onChange={(e) => setRatingFilter(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
                >
                  <option value="all">All Ratings</option>
                  <option value="5">5 Stars</option>
                  <option value="4">4 Stars</option>
                  <option value="3">3 Stars</option>
                  <option value="2">2 Stars</option>
                  <option value="1">1 Star</option>
                </select>
              </div>
            </div>
          </div>

          {/* Reviews List */}
          <div className="space-y-6">
            {filteredReviews.map((review, index) => (
              <motion.div
                key={review.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-white rounded-2xl p-6 shadow-md hover:shadow-lg transition-shadow duration-200"
              >
                {/* Review Header */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-start gap-4">
                    <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                      <span className="text-gray-600 font-semibold">
                        {review.customer.split(' ').map(n => n[0]).join('')}
                      </span>
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">{review.customer}</h3>
                      <p className="text-sm text-gray-600">Order {review.orderNumber}</p>
                      <p className="text-sm text-gray-500">{review.restaurant}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="flex items-center gap-1 mb-1">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className={`w-4 h-4 ${
                            i < review.rating 
                              ? 'text-yellow-400 fill-current' 
                              : 'text-gray-300'
                          }`}
                        />
                      ))}
                    </div>
                    <p className="text-sm text-gray-500">{formatTime(review.date)}</p>
                  </div>
                </div>

                {/* Review Content */}
                <div className="pl-16">
                  <p className="text-gray-700 leading-relaxed">{review.comment}</p>
                </div>

                {/* Review Footer */}
                <div className="flex items-center justify-between mt-4 pt-4 border-t border-gray-100">
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4 text-gray-400" />
                    <span className="text-sm text-gray-600">
                      {new Date(review.date).toLocaleDateString('en-IN', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                      review.rating >= 4 
                        ? 'bg-green-100 text-green-800'
                        : review.rating >= 3
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {review.rating >= 4 ? 'Positive' : review.rating >= 3 ? 'Neutral' : 'Negative'}
                    </span>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {filteredReviews.length === 0 && (
            <div className="text-center py-12">
              <Star className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No reviews found</h3>
              <p className="text-gray-600">Try adjusting your search or filter criteria.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DeliveryRatingsPage;
