{"version": 3, "sources": [], "sections": [{"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/project%20food-order%206k/food-ordering-system-modern/frontend/src/store/useCartStore.js"], "sourcesContent": ["import { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport { toast } from 'react-hot-toast';\n\nconst useCartStore = create(\n  persist(\n    (set, get) => ({\n      items: [],\n      restaurant: null,\n      isOpen: false,\n\n      // Add item to cart\n      addItem: (food, quantity = 1, variant = null, addons = [], customizations = []) => {\n        const state = get();\n        \n        // Check if item is from different restaurant\n        if (state.restaurant && state.restaurant._id !== food.restaurant._id) {\n          toast.error('You can only order from one restaurant at a time');\n          return false;\n        }\n\n        // Create item object\n        const newItem = {\n          id: `${food._id}-${variant?.name || 'default'}-${Date.now()}`,\n          food,\n          quantity,\n          variant,\n          addons: addons || [],\n          customizations: customizations || [],\n          price: variant?.price || food.price,\n          itemTotal: calculateItemTotal(food, quantity, variant, addons)\n        };\n\n        set({\n          items: [...state.items, newItem],\n          restaurant: food.restaurant,\n        });\n\n        toast.success(`${food.name} added to cart`);\n        return true;\n      },\n\n      // Update item quantity\n      updateQuantity: (itemId, quantity) => {\n        if (quantity <= 0) {\n          get().removeItem(itemId);\n          return;\n        }\n\n        set((state) => ({\n          items: state.items.map(item => \n            item.id === itemId \n              ? { \n                  ...item, \n                  quantity,\n                  itemTotal: calculateItemTotal(item.food, quantity, item.variant, item.addons)\n                }\n              : item\n          )\n        }));\n      },\n\n      // Remove item from cart\n      removeItem: (itemId) => {\n        set((state) => {\n          const newItems = state.items.filter(item => item.id !== itemId);\n          return {\n            items: newItems,\n            restaurant: newItems.length === 0 ? null : state.restaurant\n          };\n        });\n        toast.success('Item removed from cart');\n      },\n\n      // Clear entire cart\n      clearCart: () => {\n        set({ items: [], restaurant: null });\n        toast.success('Cart cleared');\n      },\n\n      // Toggle cart visibility\n      toggleCart: () => {\n        set((state) => ({ isOpen: !state.isOpen }));\n      },\n\n      // Open cart\n      openCart: () => {\n        set({ isOpen: true });\n      },\n\n      // Close cart\n      closeCart: () => {\n        set({ isOpen: false });\n      },\n\n      // Get cart totals\n      getTotals: () => {\n        const state = get();\n        const subtotal = state.items.reduce((sum, item) => sum + item.itemTotal, 0);\n        const deliveryFee = state.restaurant?.pricing?.deliveryFee || 0;\n        const packagingFee = state.restaurant?.pricing?.packagingFee || 0;\n        const taxes = subtotal * 0.18; // 18% GST\n        const total = subtotal + deliveryFee + packagingFee + taxes;\n\n        return {\n          subtotal,\n          deliveryFee,\n          packagingFee,\n          taxes,\n          total,\n          itemCount: state.items.reduce((sum, item) => sum + item.quantity, 0)\n        };\n      },\n\n      // Check if item exists in cart\n      isItemInCart: (foodId, variant = null) => {\n        const state = get();\n        return state.items.some(item => \n          item.food._id === foodId && \n          (variant ? item.variant?.name === variant.name : !item.variant)\n        );\n      },\n\n      // Get item from cart\n      getCartItem: (foodId, variant = null) => {\n        const state = get();\n        return state.items.find(item => \n          item.food._id === foodId && \n          (variant ? item.variant?.name === variant.name : !item.variant)\n        );\n      },\n    }),\n    {\n      name: 'cart-storage',\n      partialize: (state) => ({ \n        items: state.items, \n        restaurant: state.restaurant \n      }),\n    }\n  )\n);\n\n// Helper function to calculate item total\nconst calculateItemTotal = (food, quantity, variant, addons) => {\n  let basePrice = variant?.price || food.price;\n  \n  // Add addon prices\n  const addonTotal = addons?.reduce((sum, addon) => {\n    return sum + (addon.price * (addon.quantity || 1));\n  }, 0) || 0;\n\n  return (basePrice + addonTotal) * quantity;\n};\n\nexport default useCartStore;\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,MAAM,eAAe,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,EACxB,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,OAAO,EAAE;QACT,YAAY;QACZ,QAAQ;QAER,mBAAmB;QACnB,SAAS,CAAC,MAAM,WAAW,CAAC,EAAE,UAAU,IAAI,EAAE,SAAS,EAAE,EAAE,iBAAiB,EAAE;YAC5E,MAAM,QAAQ;YAEd,6CAA6C;YAC7C,IAAI,MAAM,UAAU,IAAI,MAAM,UAAU,CAAC,GAAG,KAAK,KAAK,UAAU,CAAC,GAAG,EAAE;gBACpE,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,OAAO;YACT;YAEA,qBAAqB;YACrB,MAAM,UAAU;gBACd,IAAI,GAAG,KAAK,GAAG,CAAC,CAAC,EAAE,SAAS,QAAQ,UAAU,CAAC,EAAE,KAAK,GAAG,IAAI;gBAC7D;gBACA;gBACA;gBACA,QAAQ,UAAU,EAAE;gBACpB,gBAAgB,kBAAkB,EAAE;gBACpC,OAAO,SAAS,SAAS,KAAK,KAAK;gBACnC,WAAW,mBAAmB,MAAM,UAAU,SAAS;YACzD;YAEA,IAAI;gBACF,OAAO;uBAAI,MAAM,KAAK;oBAAE;iBAAQ;gBAChC,YAAY,KAAK,UAAU;YAC7B;YAEA,uJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,GAAG,KAAK,IAAI,CAAC,cAAc,CAAC;YAC1C,OAAO;QACT;QAEA,uBAAuB;QACvB,gBAAgB,CAAC,QAAQ;YACvB,IAAI,YAAY,GAAG;gBACjB,MAAM,UAAU,CAAC;gBACjB;YACF;YAEA,IAAI,CAAC,QAAU,CAAC;oBACd,OAAO,MAAM,KAAK,CAAC,GAAG,CAAC,CAAA,OACrB,KAAK,EAAE,KAAK,SACR;4BACE,GAAG,IAAI;4BACP;4BACA,WAAW,mBAAmB,KAAK,IAAI,EAAE,UAAU,KAAK,OAAO,EAAE,KAAK,MAAM;wBAC9E,IACA;gBAER,CAAC;QACH;QAEA,wBAAwB;QACxB,YAAY,CAAC;YACX,IAAI,CAAC;gBACH,MAAM,WAAW,MAAM,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;gBACxD,OAAO;oBACL,OAAO;oBACP,YAAY,SAAS,MAAM,KAAK,IAAI,OAAO,MAAM,UAAU;gBAC7D;YACF;YACA,uJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB;QAEA,oBAAoB;QACpB,WAAW;YACT,IAAI;gBAAE,OAAO,EAAE;gBAAE,YAAY;YAAK;YAClC,uJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB;QAEA,yBAAyB;QACzB,YAAY;YACV,IAAI,CAAC,QAAU,CAAC;oBAAE,QAAQ,CAAC,MAAM,MAAM;gBAAC,CAAC;QAC3C;QAEA,YAAY;QACZ,UAAU;YACR,IAAI;gBAAE,QAAQ;YAAK;QACrB;QAEA,aAAa;QACb,WAAW;YACT,IAAI;gBAAE,QAAQ;YAAM;QACtB;QAEA,kBAAkB;QAClB,WAAW;YACT,MAAM,QAAQ;YACd,MAAM,WAAW,MAAM,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,SAAS,EAAE;YACzE,MAAM,cAAc,MAAM,UAAU,EAAE,SAAS,eAAe;YAC9D,MAAM,eAAe,MAAM,UAAU,EAAE,SAAS,gBAAgB;YAChE,MAAM,QAAQ,WAAW,MAAM,UAAU;YACzC,MAAM,QAAQ,WAAW,cAAc,eAAe;YAEtD,OAAO;gBACL;gBACA;gBACA;gBACA;gBACA;gBACA,WAAW,MAAM,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,QAAQ,EAAE;YACpE;QACF;QAEA,+BAA+B;QAC/B,cAAc,CAAC,QAAQ,UAAU,IAAI;YACnC,MAAM,QAAQ;YACd,OAAO,MAAM,KAAK,CAAC,IAAI,CAAC,CAAA,OACtB,KAAK,IAAI,CAAC,GAAG,KAAK,UAClB,CAAC,UAAU,KAAK,OAAO,EAAE,SAAS,QAAQ,IAAI,GAAG,CAAC,KAAK,OAAO;QAElE;QAEA,qBAAqB;QACrB,aAAa,CAAC,QAAQ,UAAU,IAAI;YAClC,MAAM,QAAQ;YACd,OAAO,MAAM,KAAK,CAAC,IAAI,CAAC,CAAA,OACtB,KAAK,IAAI,CAAC,GAAG,KAAK,UAClB,CAAC,UAAU,KAAK,OAAO,EAAE,SAAS,QAAQ,IAAI,GAAG,CAAC,KAAK,OAAO;QAElE;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,OAAO,MAAM,KAAK;YAClB,YAAY,MAAM,UAAU;QAC9B,CAAC;AACH;AAIJ,0CAA0C;AAC1C,MAAM,qBAAqB,CAAC,MAAM,UAAU,SAAS;IACnD,IAAI,YAAY,SAAS,SAAS,KAAK,KAAK;IAE5C,mBAAmB;IACnB,MAAM,aAAa,QAAQ,OAAO,CAAC,KAAK;QACtC,OAAO,MAAO,MAAM,KAAK,GAAG,CAAC,MAAM,QAAQ,IAAI,CAAC;IAClD,GAAG,MAAM;IAET,OAAO,CAAC,YAAY,UAAU,IAAI;AACpC;uCAEe", "debugId": null}}, {"offset": {"line": 170, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/project%20food-order%206k/food-ordering-system-modern/frontend/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  Menu,\n  X,\n  ShoppingCart,\n  User,\n  MapPin,\n  Search,\n  ChefHat,\n  LogOut,\n  Settings,\n  Heart,\n  Clock\n} from 'lucide-react';\nimport Link from 'next/link';\nimport { useRouter, usePathname } from 'next/navigation';\nimport useAuthStore from '@/store/useAuthStore';\nimport useCartStore from '@/store/useCartStore';\n\nconst Header = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);\n  const [isScrolled, setIsScrolled] = useState(false);\n\n  const router = useRouter();\n  const pathname = usePathname();\n  const { user, isAuthenticated, logout } = useAuthStore();\n  const { items, getTotals, toggleCart } = useCartStore();\n  const { itemCount } = getTotals();\n\n  // Hide header for vendor pages\n  if (pathname?.startsWith('/vendor')) {\n    return null;\n  }\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 10);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const handleLogout = async () => {\n    await logout();\n    setIsUserMenuOpen(false);\n    router.push('/');\n  };\n\n  const navItems = [\n    { name: 'Restaurants', href: '/restaurants' },\n    { name: 'Cuisines', href: '/cuisines' },\n    { name: 'Offers', href: '/offers' },\n    { name: 'About', href: '/about' },\n  ];\n\n  return (\n    <motion.header\n      initial={{ y: -100 }}\n      animate={{ y: 0 }}\n      transition={{ duration: 0.6, ease: \"easeOut\" }}\n      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n        isScrolled \n          ? 'bg-white/95 backdrop-blur-md shadow-lg' \n          : 'bg-transparent'\n      }`}\n    >\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16 lg:h-20\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center gap-2\">\n            <motion.div\n              whileHover={{ rotate: 360 }}\n              transition={{ duration: 0.6 }}\n              className=\"w-10 h-10 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center\"\n            >\n              <ChefHat className=\"w-6 h-6 text-white\" />\n            </motion.div>\n            <span className=\"text-2xl font-bold text-gray-900\">\n              FoodieExpress\n            </span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden lg:flex items-center space-x-8\">\n            {navItems.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className=\"text-gray-700 hover:text-orange-500 font-medium transition-colors duration-200 relative group\"\n              >\n                {item.name}\n                <span className=\"absolute -bottom-1 left-0 w-0 h-0.5 bg-orange-500 transition-all duration-300 group-hover:w-full\" />\n              </Link>\n            ))}\n          </nav>\n\n          {/* Right Side Actions */}\n          <div className=\"flex items-center gap-4\">\n            {/* Location */}\n            <motion.button\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              className=\"hidden sm:flex items-center gap-2 text-gray-700 hover:text-orange-500 transition-colors duration-200\"\n            >\n              <MapPin className=\"w-5 h-5\" />\n              <span className=\"text-sm font-medium\">Location</span>\n            </motion.button>\n\n            {/* Search */}\n            <motion.button\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              className=\"p-2 text-gray-700 hover:text-orange-500 transition-colors duration-200\"\n            >\n              <Search className=\"w-5 h-5\" />\n            </motion.button>\n\n            {/* Cart */}\n            <motion.button\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              onClick={toggleCart}\n              className=\"relative p-2 text-gray-700 hover:text-orange-500 transition-colors duration-200\"\n            >\n              <ShoppingCart className=\"w-5 h-5\" />\n              {itemCount > 0 && (\n                <motion.span\n                  initial={{ scale: 0 }}\n                  animate={{ scale: 1 }}\n                  className=\"absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-medium\"\n                >\n                  {itemCount}\n                </motion.span>\n              )}\n            </motion.button>\n\n            {/* User Menu */}\n            {isAuthenticated ? (\n              <div className=\"relative\">\n                <motion.button\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                  onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}\n                  className=\"flex items-center gap-2 p-2 rounded-full hover:bg-gray-100 transition-colors duration-200\"\n                >\n                  <div className=\"w-8 h-8 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center\">\n                    <span className=\"text-white text-sm font-medium\">\n                      {user?.name?.charAt(0).toUpperCase()}\n                    </span>\n                  </div>\n                </motion.button>\n\n                <AnimatePresence>\n                  {isUserMenuOpen && (\n                    <motion.div\n                      initial={{ opacity: 0, y: 10, scale: 0.95 }}\n                      animate={{ opacity: 1, y: 0, scale: 1 }}\n                      exit={{ opacity: 0, y: 10, scale: 0.95 }}\n                      transition={{ duration: 0.2 }}\n                      className=\"absolute right-0 mt-2 w-64 bg-white rounded-xl shadow-lg border border-gray-200 py-2\"\n                    >\n                      <div className=\"px-4 py-3 border-b border-gray-100\">\n                        <p className=\"text-sm font-medium text-gray-900\">\n                          {user?.name}\n                        </p>\n                        <p className=\"text-sm text-gray-500\">\n                          {user?.email}\n                        </p>\n                      </div>\n\n                      <div className=\"py-2\">\n                        <Link\n                          href=\"/profile\"\n                          className=\"flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50\"\n                          onClick={() => setIsUserMenuOpen(false)}\n                        >\n                          <User className=\"w-4 h-4\" />\n                          Profile\n                        </Link>\n                        <Link\n                          href=\"/orders\"\n                          className=\"flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50\"\n                          onClick={() => setIsUserMenuOpen(false)}\n                        >\n                          <Clock className=\"w-4 h-4\" />\n                          Orders\n                        </Link>\n                        <Link\n                          href=\"/favorites\"\n                          className=\"flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50\"\n                          onClick={() => setIsUserMenuOpen(false)}\n                        >\n                          <Heart className=\"w-4 h-4\" />\n                          Favorites\n                        </Link>\n                        <Link\n                          href=\"/settings\"\n                          className=\"flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50\"\n                          onClick={() => setIsUserMenuOpen(false)}\n                        >\n                          <Settings className=\"w-4 h-4\" />\n                          Settings\n                        </Link>\n                      </div>\n\n                      <div className=\"border-t border-gray-100 pt-2\">\n                        <button\n                          onClick={handleLogout}\n                          className=\"flex items-center gap-3 px-4 py-2 text-sm text-red-600 hover:bg-red-50 w-full text-left\"\n                        >\n                          <LogOut className=\"w-4 h-4\" />\n                          Sign Out\n                        </button>\n                      </div>\n                    </motion.div>\n                  )}\n                </AnimatePresence>\n              </div>\n            ) : (\n              <div className=\"flex items-center gap-2\">\n                <Link href=\"/auth/login\">\n                  <motion.button\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    className=\"px-4 py-2 text-gray-700 hover:text-orange-500 font-medium transition-colors duration-200\"\n                  >\n                    Sign In\n                  </motion.button>\n                </Link>\n                <Link href=\"/auth/register\">\n                  <motion.button\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    className=\"px-4 py-2 bg-gradient-to-r from-orange-500 to-red-500 text-white rounded-full font-medium shadow-md hover:shadow-lg transition-all duration-200\"\n                  >\n                    Sign Up\n                  </motion.button>\n                </Link>\n              </div>\n            )}\n\n            {/* Mobile Menu Button */}\n            <motion.button\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"lg:hidden p-2 text-gray-700 hover:text-orange-500 transition-colors duration-200\"\n            >\n              {isMenuOpen ? (\n                <X className=\"w-6 h-6\" />\n              ) : (\n                <Menu className=\"w-6 h-6\" />\n              )}\n            </motion.button>\n          </div>\n        </div>\n\n        {/* Mobile Menu */}\n        <AnimatePresence>\n          {isMenuOpen && (\n            <motion.div\n              initial={{ opacity: 0, height: 0 }}\n              animate={{ opacity: 1, height: 'auto' }}\n              exit={{ opacity: 0, height: 0 }}\n              transition={{ duration: 0.3 }}\n              className=\"lg:hidden border-t border-gray-200 bg-white\"\n            >\n              <div className=\"py-4 space-y-2\">\n                {navItems.map((item) => (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className=\"block px-4 py-2 text-gray-700 hover:text-orange-500 hover:bg-gray-50 transition-colors duration-200\"\n                    onClick={() => setIsMenuOpen(false)}\n                  >\n                    {item.name}\n                  </Link>\n                ))}\n              </div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </div>\n    </motion.header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AACA;AACA;AACA;AApBA;;;;;;;;;AAsBA,MAAM,SAAS;IACb,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,UAAY,AAAD;IACrD,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,UAAY,AAAD;IACpD,MAAM,EAAE,SAAS,EAAE,GAAG;IAEtB,+BAA+B;IAC/B,IAAI,UAAU,WAAW,YAAY;QACnC,OAAO;IACT;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,cAAc,OAAO,OAAO,GAAG;QACjC;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,eAAe;QACnB,MAAM;QACN,kBAAkB;QAClB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,WAAW;QACf;YAAE,MAAM;YAAe,MAAM;QAAe;QAC5C;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAU,MAAM;QAAU;QAClC;YAAE,MAAM;YAAS,MAAM;QAAS;KACjC;IAED,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,SAAS;YAAE,GAAG,CAAC;QAAI;QACnB,SAAS;YAAE,GAAG;QAAE;QAChB,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;QAC7C,WAAW,CAAC,4DAA4D,EACtE,aACI,2CACA,kBACJ;kBAEF,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,YAAY;wCAAE,QAAQ;oCAAI;oCAC1B,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,WAAU;8CAEV,cAAA,8OAAC,4MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;;;;;;8CAErB,8OAAC;oCAAK,WAAU;8CAAmC;;;;;;;;;;;;sCAMrD,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;;wCAET,KAAK,IAAI;sDACV,8OAAC;4CAAK,WAAU;;;;;;;mCALX,KAAK,IAAI;;;;;;;;;;sCAWpB,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAU;;sDAEV,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;4CAAK,WAAU;sDAAsB;;;;;;;;;;;;8CAIxC,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAU;8CAEV,cAAA,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;8CAIpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,SAAS;oCACT,WAAU;;sDAEV,8OAAC,sNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;wCACvB,YAAY,mBACX,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;4CACV,SAAS;gDAAE,OAAO;4CAAE;4CACpB,SAAS;gDAAE,OAAO;4CAAE;4CACpB,WAAU;sDAET;;;;;;;;;;;;gCAMN,gCACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4CACZ,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,UAAU;gDAAE,OAAO;4CAAK;4CACxB,SAAS,IAAM,kBAAkB,CAAC;4CAClC,WAAU;sDAEV,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DACb,MAAM,MAAM,OAAO,GAAG;;;;;;;;;;;;;;;;sDAK7B,8OAAC,yLAAA,CAAA,kBAAe;sDACb,gCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,GAAG;oDAAI,OAAO;gDAAK;gDAC1C,SAAS;oDAAE,SAAS;oDAAG,GAAG;oDAAG,OAAO;gDAAE;gDACtC,MAAM;oDAAE,SAAS;oDAAG,GAAG;oDAAI,OAAO;gDAAK;gDACvC,YAAY;oDAAE,UAAU;gDAAI;gDAC5B,WAAU;;kEAEV,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EACV,MAAM;;;;;;0EAET,8OAAC;gEAAE,WAAU;0EACV,MAAM;;;;;;;;;;;;kEAIX,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,4JAAA,CAAA,UAAI;gEACH,MAAK;gEACL,WAAU;gEACV,SAAS,IAAM,kBAAkB;;kFAEjC,8OAAC,kMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAY;;;;;;;0EAG9B,8OAAC,4JAAA,CAAA,UAAI;gEACH,MAAK;gEACL,WAAU;gEACV,SAAS,IAAM,kBAAkB;;kFAEjC,8OAAC,oMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;oEAAY;;;;;;;0EAG/B,8OAAC,4JAAA,CAAA,UAAI;gEACH,MAAK;gEACL,WAAU;gEACV,SAAS,IAAM,kBAAkB;;kFAEjC,8OAAC,oMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;oEAAY;;;;;;;0EAG/B,8OAAC,4JAAA,CAAA,UAAI;gEACH,MAAK;gEACL,WAAU;gEACV,SAAS,IAAM,kBAAkB;;kFAEjC,8OAAC,0MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;oEAAY;;;;;;;;;;;;;kEAKpC,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DACC,SAAS;4DACT,WAAU;;8EAEV,8OAAC,0MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;6FAS1C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gDACZ,YAAY;oDAAE,OAAO;gDAAK;gDAC1B,UAAU;oDAAE,OAAO;gDAAK;gDACxB,WAAU;0DACX;;;;;;;;;;;sDAIH,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gDACZ,YAAY;oDAAE,OAAO;gDAAK;gDAC1B,UAAU;oDAAE,OAAO;gDAAK;gDACxB,WAAU;0DACX;;;;;;;;;;;;;;;;;8CAQP,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,SAAS,IAAM,cAAc,CAAC;oCAC9B,WAAU;8CAET,2BACC,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;iGAEb,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8BAOxB,8OAAC,yLAAA,CAAA,kBAAe;8BACb,4BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,QAAQ;wBAAE;wBACjC,SAAS;4BAAE,SAAS;4BAAG,QAAQ;wBAAO;wBACtC,MAAM;4BAAE,SAAS;4BAAG,QAAQ;wBAAE;wBAC9B,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,SAAS,IAAM,cAAc;8CAE5B,KAAK,IAAI;mCALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAelC;uCAEe", "debugId": null}}, {"offset": {"line": 772, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/project%20food-order%206k/food-ordering-system-modern/frontend/src/components/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { \n  ChefHat, \n  Mail, \n  Phone, \n  MapPin, \n  Facebook, \n  Twitter, \n  Instagram, \n  Youtube,\n  ArrowRight\n} from 'lucide-react';\nimport Link from 'next/link';\n\nconst Footer = () => {\n  const footerLinks = {\n    company: [\n      { name: 'About Us', href: '/about' },\n      { name: 'Careers', href: '/careers' },\n      { name: 'Press', href: '/press' },\n      { name: 'Blog', href: '/blog' },\n    ],\n    support: [\n      { name: 'Help Center', href: '/help' },\n      { name: 'Contact Us', href: '/contact' },\n      { name: 'Safety', href: '/safety' },\n      { name: 'Terms of Service', href: '/terms' },\n    ],\n    partner: [\n      { name: 'Become a Partner', href: '/partner' },\n      { name: 'Restaurant Dashboard', href: '/restaurant-dashboard' },\n      { name: 'Delivery Partner', href: '/delivery-partner' },\n      { name: 'API Documentation', href: '/api-docs' },\n    ],\n    admin: [\n      { name: 'Admin Login', href: '/auth/login?role=admin' },\n      { name: 'Delivery Partner Login', href: '/auth/login?role=delivery' },\n    ],\n  };\n\n  const socialLinks = [\n    { icon: Facebook, href: '#', label: 'Facebook' },\n    { icon: Twitter, href: '#', label: 'Twitter' },\n    { icon: Instagram, href: '#', label: 'Instagram' },\n    { icon: Youtube, href: '#', label: 'YouTube' },\n  ];\n\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      {/* Newsletter Section */}\n      <div className=\"border-b border-gray-800\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"text-center\"\n          >\n            <h3 className=\"text-2xl font-bold mb-4\">\n              Stay Updated with FoodieExpress\n            </h3>\n            <p className=\"text-gray-400 mb-8 max-w-2xl mx-auto\">\n              Get the latest updates on new restaurants, exclusive offers, and delicious deals delivered straight to your inbox.\n            </p>\n            \n            <div className=\"flex flex-col sm:flex-row gap-4 max-w-md mx-auto\">\n              <input\n                type=\"email\"\n                placeholder=\"Enter your email\"\n                className=\"flex-1 px-4 py-3 rounded-lg bg-gray-800 border border-gray-700 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent\"\n              />\n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"px-6 py-3 bg-gradient-to-r from-orange-500 to-red-500 text-white rounded-lg font-medium hover:shadow-lg transition-all duration-200 flex items-center gap-2 justify-center\"\n              >\n                Subscribe\n                <ArrowRight className=\"w-4 h-4\" />\n              </motion.button>\n            </div>\n          </motion.div>\n        </div>\n      </div>\n\n      {/* Main Footer Content */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8\">\n          {/* Brand Section */}\n          <div className=\"lg:col-span-2\">\n            <Link href=\"/\" className=\"flex items-center gap-2 mb-6\">\n              <div className=\"w-10 h-10 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center\">\n                <ChefHat className=\"w-6 h-6 text-white\" />\n              </div>\n              <span className=\"text-2xl font-bold\">FoodieExpress</span>\n            </Link>\n            \n            <p className=\"text-gray-400 mb-6 max-w-md\">\n              Delivering happiness one meal at a time. Order from your favorite restaurants and enjoy fast, reliable delivery service.\n            </p>\n\n            {/* Contact Info */}\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center gap-3 text-gray-400\">\n                <Phone className=\"w-5 h-5\" />\n                <span>+91 98765 43210</span>\n              </div>\n              <div className=\"flex items-center gap-3 text-gray-400\">\n                <Mail className=\"w-5 h-5\" />\n                <span><EMAIL></span>\n              </div>\n              <div className=\"flex items-center gap-3 text-gray-400\">\n                <MapPin className=\"w-5 h-5\" />\n                <span>Mumbai, Maharashtra, India</span>\n              </div>\n            </div>\n          </div>\n\n          {/* Company Links */}\n          <div>\n            <h4 className=\"text-lg font-semibold mb-6\">Company</h4>\n            <ul className=\"space-y-3\">\n              {footerLinks.company.map((link) => (\n                <li key={link.name}>\n                  <Link\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-white transition-colors duration-200\"\n                  >\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Support Links */}\n          <div>\n            <h4 className=\"text-lg font-semibold mb-6\">Support</h4>\n            <ul className=\"space-y-3\">\n              {footerLinks.support.map((link) => (\n                <li key={link.name}>\n                  <Link\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-white transition-colors duration-200\"\n                  >\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Partner Links */}\n          <div>\n            <h4 className=\"text-lg font-semibold mb-6\">Partner With Us</h4>\n            <ul className=\"space-y-3\">\n              {footerLinks.partner.map((link) => (\n                <li key={link.name}>\n                  <Link\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-white transition-colors duration-200\"\n                  >\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Admin Links */}\n          <div>\n            <h4 className=\"text-lg font-semibold mb-6\">Admin Access</h4>\n            <ul className=\"space-y-3\">\n              {footerLinks.admin.map((link) => (\n                <li key={link.name}>\n                  <Link\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-orange-400 transition-colors duration-200\"\n                  >\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n\n          </div>\n        </div>\n      </div>\n\n      {/* Bottom Section */}\n      <div className=\"border-t border-gray-800\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n          <div className=\"flex flex-col md:flex-row items-center justify-between gap-4\">\n            {/* Copyright */}\n            <p className=\"text-gray-400 text-sm\">\n              © 2024 FoodieExpress. All rights reserved.\n            </p>\n\n            {/* Social Links */}\n            <div className=\"flex items-center gap-4\">\n              {socialLinks.map((social) => (\n                <motion.a\n                  key={social.label}\n                  href={social.href}\n                  whileHover={{ scale: 1.1 }}\n                  whileTap={{ scale: 0.9 }}\n                  className=\"w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center text-gray-400 hover:text-white hover:bg-orange-500 transition-all duration-200\"\n                  aria-label={social.label}\n                >\n                  <social.icon className=\"w-5 h-5\" />\n                </motion.a>\n              ))}\n            </div>\n\n            {/* Legal Links */}\n            <div className=\"flex items-center gap-6 text-sm\">\n              <Link\n                href=\"/privacy\"\n                className=\"text-gray-400 hover:text-white transition-colors duration-200\"\n              >\n                Privacy Policy\n              </Link>\n              <Link\n                href=\"/cookies\"\n                className=\"text-gray-400 hover:text-white transition-colors duration-200\"\n              >\n                Cookie Policy\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AAdA;;;;;AAgBA,MAAM,SAAS;IACb,MAAM,cAAc;QAClB,SAAS;YACP;gBAAE,MAAM;gBAAY,MAAM;YAAS;YACnC;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAS,MAAM;YAAS;YAChC;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;SAC/B;QACD,SAAS;YACP;gBAAE,MAAM;gBAAe,MAAM;YAAQ;YACrC;gBAAE,MAAM;gBAAc,MAAM;YAAW;YACvC;gBAAE,MAAM;gBAAU,MAAM;YAAU;YAClC;gBAAE,MAAM;gBAAoB,MAAM;YAAS;SAC5C;QACD,SAAS;YACP;gBAAE,MAAM;gBAAoB,MAAM;YAAW;YAC7C;gBAAE,MAAM;gBAAwB,MAAM;YAAwB;YAC9D;gBAAE,MAAM;gBAAoB,MAAM;YAAoB;YACtD;gBAAE,MAAM;gBAAqB,MAAM;YAAY;SAChD;QACD,OAAO;YACL;gBAAE,MAAM;gBAAe,MAAM;YAAyB;YACtD;gBAAE,MAAM;gBAA0B,MAAM;YAA4B;SACrE;IACH;IAEA,MAAM,cAAc;QAClB;YAAE,MAAM,0MAAA,CAAA,WAAQ;YAAE,MAAM;YAAK,OAAO;QAAW;QAC/C;YAAE,MAAM,wMAAA,CAAA,UAAO;YAAE,MAAM;YAAK,OAAO;QAAU;QAC7C;YAAE,MAAM,4MAAA,CAAA,YAAS;YAAE,MAAM;YAAK,OAAO;QAAY;QACjD;YAAE,MAAM,wMAAA,CAAA,UAAO;YAAE,MAAM;YAAK,OAAO;QAAU;KAC9C;IAED,qBACE,8OAAC;QAAO,WAAU;;0BAEhB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;0CAA0B;;;;;;0CAGxC,8OAAC;gCAAE,WAAU;0CAAuC;;;;;;0CAIpD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;kDAEZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,WAAU;;4CACX;0DAEC,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;;sDACvB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,4MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;sDAErB,8OAAC;4CAAK,WAAU;sDAAqB;;;;;;;;;;;;8CAGvC,8OAAC;oCAAE,WAAU;8CAA8B;;;;;;8CAK3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;sCAMZ,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;8CACX,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,qBACtB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAgB5B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;0CAKrC,8OAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAC,uBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wCAEP,MAAM,OAAO,IAAI;wCACjB,YAAY;4CAAE,OAAO;wCAAI;wCACzB,UAAU;4CAAE,OAAO;wCAAI;wCACvB,WAAU;wCACV,cAAY,OAAO,KAAK;kDAExB,cAAA,8OAAC,OAAO,IAAI;4CAAC,WAAU;;;;;;uCAPlB,OAAO,KAAK;;;;;;;;;;0CAavB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;uCAEe", "debugId": null}}, {"offset": {"line": 1372, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/project%20food-order%206k/food-ordering-system-modern/frontend/src/lib/utils.js"], "sourcesContent": ["import { clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs) {\n  return twMerge(clsx(inputs));\n}\n\n// Format currency\nexport const formatCurrency = (amount) => {\n  return new Intl.NumberFormat('en-IN', {\n    style: 'currency',\n    currency: 'INR',\n    minimumFractionDigits: 0,\n    maximumFractionDigits: 2,\n  }).format(amount);\n};\n\n// Format date\nexport const formatDate = (date) => {\n  return new Intl.DateTimeFormat('en-IN', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  }).format(new Date(date));\n};\n\n// Format time\nexport const formatTime = (date) => {\n  return new Intl.DateTimeFormat('en-IN', {\n    hour: '2-digit',\n    minute: '2-digit',\n    hour12: true,\n  }).format(new Date(date));\n};\n\n// Format relative time\nexport const formatRelativeTime = (date) => {\n  const now = new Date();\n  const targetDate = new Date(date);\n  const diffInSeconds = Math.floor((now - targetDate) / 1000);\n\n  if (diffInSeconds < 60) {\n    return 'Just now';\n  } else if (diffInSeconds < 3600) {\n    const minutes = Math.floor(diffInSeconds / 60);\n    return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;\n  } else if (diffInSeconds < 86400) {\n    const hours = Math.floor(diffInSeconds / 3600);\n    return `${hours} hour${hours > 1 ? 's' : ''} ago`;\n  } else {\n    const days = Math.floor(diffInSeconds / 86400);\n    return `${days} day${days > 1 ? 's' : ''} ago`;\n  }\n};\n\n// Calculate distance between two coordinates\nexport const calculateDistance = (lat1, lon1, lat2, lon2) => {\n  const R = 6371; // Radius of the Earth in kilometers\n  const dLat = (lat2 - lat1) * Math.PI / 180;\n  const dLon = (lon2 - lon1) * Math.PI / 180;\n  const a = \n    Math.sin(dLat/2) * Math.sin(dLat/2) +\n    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * \n    Math.sin(dLon/2) * Math.sin(dLon/2);\n  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));\n  const distance = R * c;\n  return Math.round(distance * 10) / 10; // Round to 1 decimal place\n};\n\n// Generate order status color\nexport const getOrderStatusColor = (status) => {\n  const colors = {\n    placed: 'bg-blue-100 text-blue-800',\n    confirmed: 'bg-green-100 text-green-800',\n    preparing: 'bg-yellow-100 text-yellow-800',\n    ready: 'bg-purple-100 text-purple-800',\n    'picked-up': 'bg-indigo-100 text-indigo-800',\n    'out-for-delivery': 'bg-orange-100 text-orange-800',\n    delivered: 'bg-green-100 text-green-800',\n    cancelled: 'bg-red-100 text-red-800',\n    refunded: 'bg-gray-100 text-gray-800',\n  };\n  return colors[status] || 'bg-gray-100 text-gray-800';\n};\n\n// Generate random ID\nexport const generateId = () => {\n  return Math.random().toString(36).substr(2, 9);\n};\n\n// Debounce function\nexport const debounce = (func, wait) => {\n  let timeout;\n  return function executedFunction(...args) {\n    const later = () => {\n      clearTimeout(timeout);\n      func(...args);\n    };\n    clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n  };\n};\n\n// Throttle function\nexport const throttle = (func, limit) => {\n  let inThrottle;\n  return function() {\n    const args = arguments;\n    const context = this;\n    if (!inThrottle) {\n      func.apply(context, args);\n      inThrottle = true;\n      setTimeout(() => inThrottle = false, limit);\n    }\n  };\n};\n\n// Validate email\nexport const isValidEmail = (email) => {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n};\n\n// Validate phone number (Indian format)\nexport const isValidPhone = (phone) => {\n  const phoneRegex = /^[6-9]\\d{9}$/;\n  return phoneRegex.test(phone);\n};\n\n// Get user location\nexport const getUserLocation = () => {\n  return new Promise((resolve, reject) => {\n    if (!navigator.geolocation) {\n      reject(new Error('Geolocation is not supported by this browser.'));\n      return;\n    }\n\n    navigator.geolocation.getCurrentPosition(\n      (position) => {\n        resolve({\n          latitude: position.coords.latitude,\n          longitude: position.coords.longitude,\n        });\n      },\n      (error) => {\n        reject(error);\n      },\n      {\n        enableHighAccuracy: true,\n        timeout: 10000,\n        maximumAge: 600000, // 10 minutes\n      }\n    );\n  });\n};\n\n// Local storage helpers\nexport const storage = {\n  get: (key) => {\n    try {\n      const item = localStorage.getItem(key);\n      return item ? JSON.parse(item) : null;\n    } catch (error) {\n      console.error('Error getting from localStorage:', error);\n      return null;\n    }\n  },\n  set: (key, value) => {\n    try {\n      localStorage.setItem(key, JSON.stringify(value));\n    } catch (error) {\n      console.error('Error setting to localStorage:', error);\n    }\n  },\n  remove: (key) => {\n    try {\n      localStorage.removeItem(key);\n    } catch (error) {\n      console.error('Error removing from localStorage:', error);\n    }\n  },\n};\n\n// Image optimization\nexport const getOptimizedImageUrl = (url, width = 400, height = 300) => {\n  if (!url) return '/images/placeholder.jpg';\n  \n  // If it's already an optimized URL, return as is\n  if (url.includes('w_') || url.includes('h_')) return url;\n  \n  // For Cloudinary URLs, add optimization parameters\n  if (url.includes('cloudinary.com')) {\n    return url.replace('/upload/', `/upload/w_${width},h_${height},c_fill,f_auto,q_auto/`);\n  }\n  \n  return url;\n};\n\n// Truncate text\nexport const truncateText = (text, maxLength = 100) => {\n  if (!text) return '';\n  if (text.length <= maxLength) return text;\n  return text.substr(0, maxLength) + '...';\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAM;IAC1B,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,MAAM,iBAAiB,CAAC;IAC7B,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;QACV,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAGO,MAAM,aAAa,CAAC;IACzB,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAGO,MAAM,aAAa,CAAC;IACzB,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,QAAQ;QACR,QAAQ;IACV,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAGO,MAAM,qBAAqB,CAAC;IACjC,MAAM,MAAM,IAAI;IAChB,MAAM,aAAa,IAAI,KAAK;IAC5B,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,MAAM,UAAU,IAAI;IAEtD,IAAI,gBAAgB,IAAI;QACtB,OAAO;IACT,OAAO,IAAI,gBAAgB,MAAM;QAC/B,MAAM,UAAU,KAAK,KAAK,CAAC,gBAAgB;QAC3C,OAAO,GAAG,QAAQ,OAAO,EAAE,UAAU,IAAI,MAAM,GAAG,IAAI,CAAC;IACzD,OAAO,IAAI,gBAAgB,OAAO;QAChC,MAAM,QAAQ,KAAK,KAAK,CAAC,gBAAgB;QACzC,OAAO,GAAG,MAAM,KAAK,EAAE,QAAQ,IAAI,MAAM,GAAG,IAAI,CAAC;IACnD,OAAO;QACL,MAAM,OAAO,KAAK,KAAK,CAAC,gBAAgB;QACxC,OAAO,GAAG,KAAK,IAAI,EAAE,OAAO,IAAI,MAAM,GAAG,IAAI,CAAC;IAChD;AACF;AAGO,MAAM,oBAAoB,CAAC,MAAM,MAAM,MAAM;IAClD,MAAM,IAAI,MAAM,oCAAoC;IACpD,MAAM,OAAO,CAAC,OAAO,IAAI,IAAI,KAAK,EAAE,GAAG;IACvC,MAAM,OAAO,CAAC,OAAO,IAAI,IAAI,KAAK,EAAE,GAAG;IACvC,MAAM,IACJ,KAAK,GAAG,CAAC,OAAK,KAAK,KAAK,GAAG,CAAC,OAAK,KACjC,KAAK,GAAG,CAAC,OAAO,KAAK,EAAE,GAAG,OAAO,KAAK,GAAG,CAAC,OAAO,KAAK,EAAE,GAAG,OAC3D,KAAK,GAAG,CAAC,OAAK,KAAK,KAAK,GAAG,CAAC,OAAK;IACnC,MAAM,IAAI,IAAI,KAAK,KAAK,CAAC,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAE;IACnD,MAAM,WAAW,IAAI;IACrB,OAAO,KAAK,KAAK,CAAC,WAAW,MAAM,IAAI,2BAA2B;AACpE;AAGO,MAAM,sBAAsB,CAAC;IAClC,MAAM,SAAS;QACb,QAAQ;QACR,WAAW;QACX,WAAW;QACX,OAAO;QACP,aAAa;QACb,oBAAoB;QACpB,WAAW;QACX,WAAW;QACX,UAAU;IACZ;IACA,OAAO,MAAM,CAAC,OAAO,IAAI;AAC3B;AAGO,MAAM,aAAa;IACxB,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAGO,MAAM,WAAW,CAAC,MAAM;IAC7B,IAAI;IACJ,OAAO,SAAS,iBAAiB,GAAG,IAAI;QACtC,MAAM,QAAQ;YACZ,aAAa;YACb,QAAQ;QACV;QACA,aAAa;QACb,UAAU,WAAW,OAAO;IAC9B;AACF;AAGO,MAAM,WAAW,CAAC,MAAM;IAC7B,IAAI;IACJ,OAAO;QACL,MAAM,OAAO;QACb,MAAM,UAAU,IAAI;QACpB,IAAI,CAAC,YAAY;YACf,KAAK,KAAK,CAAC,SAAS;YACpB,aAAa;YACb,WAAW,IAAM,aAAa,OAAO;QACvC;IACF;AACF;AAGO,MAAM,eAAe,CAAC;IAC3B,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAGO,MAAM,eAAe,CAAC;IAC3B,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAGO,MAAM,kBAAkB;IAC7B,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,IAAI,CAAC,UAAU,WAAW,EAAE;YAC1B,OAAO,IAAI,MAAM;YACjB;QACF;QAEA,UAAU,WAAW,CAAC,kBAAkB,CACtC,CAAC;YACC,QAAQ;gBACN,UAAU,SAAS,MAAM,CAAC,QAAQ;gBAClC,WAAW,SAAS,MAAM,CAAC,SAAS;YACtC;QACF,GACA,CAAC;YACC,OAAO;QACT,GACA;YACE,oBAAoB;YACpB,SAAS;YACT,YAAY;QACd;IAEJ;AACF;AAGO,MAAM,UAAU;IACrB,KAAK,CAAC;QACJ,IAAI;YACF,MAAM,OAAO,aAAa,OAAO,CAAC;YAClC,OAAO,OAAO,KAAK,KAAK,CAAC,QAAQ;QACnC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO;QACT;IACF;IACA,KAAK,CAAC,KAAK;QACT,IAAI;YACF,aAAa,OAAO,CAAC,KAAK,KAAK,SAAS,CAAC;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD;IACF;IACA,QAAQ,CAAC;QACP,IAAI;YACF,aAAa,UAAU,CAAC;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;QACrD;IACF;AACF;AAGO,MAAM,uBAAuB,CAAC,KAAK,QAAQ,GAAG,EAAE,SAAS,GAAG;IACjE,IAAI,CAAC,KAAK,OAAO;IAEjB,iDAAiD;IACjD,IAAI,IAAI,QAAQ,CAAC,SAAS,IAAI,QAAQ,CAAC,OAAO,OAAO;IAErD,mDAAmD;IACnD,IAAI,IAAI,QAAQ,CAAC,mBAAmB;QAClC,OAAO,IAAI,OAAO,CAAC,YAAY,CAAC,UAAU,EAAE,MAAM,GAAG,EAAE,OAAO,sBAAsB,CAAC;IACvF;IAEA,OAAO;AACT;AAGO,MAAM,eAAe,CAAC,MAAM,YAAY,GAAG;IAChD,IAAI,CAAC,MAAM,OAAO;IAClB,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,MAAM,CAAC,GAAG,aAAa;AACrC", "debugId": null}}, {"offset": {"line": 1557, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/project%20food-order%206k/food-ordering-system-modern/frontend/src/components/RestaurantCard.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { Star, Clock, MapPin, Heart, Truck } from 'lucide-react';\nimport Image from 'next/image';\nimport Link from 'next/link';\nimport { useState } from 'react';\nimport { formatCurrency, getOptimizedImageUrl } from '@/lib/utils';\nimport useAuthStore from '@/store/useAuthStore';\nimport { userAPI } from '@/lib/api';\nimport { toast } from 'react-hot-toast';\n\ninterface RestaurantCardProps {\n  restaurant: any;\n  viewMode?: 'grid' | 'list';\n  showFavoriteButton?: boolean;\n}\n\nconst RestaurantCard = ({ restaurant, viewMode = 'grid', showFavoriteButton = true }: RestaurantCardProps) => {\n  const [isFavorite, setIsFavorite] = useState(false);\n  const [isLoading, setIsLoading] = useState(false);\n  const { isAuthenticated } = useAuthStore();\n\n  // Don't render if restaurant or restaurant id is missing\n  if (!restaurant || (!restaurant._id && !restaurant.id)) {\n    return null;\n  }\n\n  // Use either _id or id field\n  const restaurantId = restaurant._id || restaurant.id;\n\n  const handleFavoriteToggle = async (e: React.MouseEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n\n    if (!isAuthenticated) {\n      toast.error('Please login to add favorites');\n      return;\n    }\n\n    setIsLoading(true);\n    try {\n      if (isFavorite) {\n        await userAPI.removeFromFavorites(restaurantId);\n        setIsFavorite(false);\n        toast.success('Removed from favorites');\n      } else {\n        await userAPI.addToFavorites(restaurantId);\n        setIsFavorite(true);\n        toast.success('Added to favorites');\n      }\n    } catch (error) {\n      toast.error('Something went wrong');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const cardVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: { \n      opacity: 1, \n      y: 0,\n      transition: { duration: 0.5 }\n    },\n    hover: { \n      y: -5,\n      transition: { duration: 0.2 }\n    }\n  };\n\n  if (viewMode === 'list') {\n    return (\n      <motion.div\n        variants={cardVariants}\n        initial=\"hidden\"\n        animate=\"visible\"\n        whileHover=\"hover\"\n        layout\n      >\n        <Link href={`/restaurants/${restaurantId}`}>\n          <div className=\"bg-white rounded-2xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden\">\n            <div className=\"flex\">\n              {/* Image */}\n              <div className=\"relative w-48 h-32 flex-shrink-0\">\n                <Image\n                  src={getOptimizedImageUrl(restaurant.images?.banner || '/images/restaurant-placeholder.jpg')}\n                  alt={restaurant.name}\n                  fill\n                  className=\"object-cover\"\n                />\n                \n                {/* Status Badge */}\n                <div className=\"absolute top-3 left-3\">\n                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${\n                    restaurant.isOpen \n                      ? 'bg-green-100 text-green-800' \n                      : 'bg-red-100 text-red-800'\n                  }`}>\n                    {restaurant.isOpen ? 'Open' : 'Closed'}\n                  </span>\n                </div>\n\n                {/* Favorite Button */}\n                {showFavoriteButton && (\n                  <motion.button\n                    whileHover={{ scale: 1.1 }}\n                    whileTap={{ scale: 0.9 }}\n                    onClick={handleFavoriteToggle}\n                    disabled={isLoading}\n                    className=\"absolute top-3 right-3 w-8 h-8 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center shadow-md hover:bg-white transition-colors duration-200\"\n                  >\n                    <Heart\n                      className={`w-4 h-4 transition-colors duration-200 ${\n                        isFavorite ? 'text-red-500 fill-current' : 'text-gray-600'\n                      }`}\n                    />\n                  </motion.button>\n                )}\n              </div>\n\n              {/* Content */}\n              <div className=\"flex-1 p-6\">\n                <div className=\"flex items-start justify-between mb-2\">\n                  <h3 className=\"text-lg font-bold text-gray-900 line-clamp-1\">\n                    {restaurant.name}\n                  </h3>\n                  <div className=\"flex items-center gap-1 ml-2\">\n                    <Star className=\"w-4 h-4 text-yellow-400 fill-current\" />\n                    <span className=\"text-sm font-medium text-gray-700\">\n                      {restaurant.ratings?.average?.toFixed(1) || '0.0'}\n                    </span>\n                    <span className=\"text-sm text-gray-500\">\n                      ({restaurant.ratings?.count || 0})\n                    </span>\n                  </div>\n                </div>\n\n                <p className=\"text-gray-600 text-sm mb-3 line-clamp-2\">\n                  {restaurant.description}\n                </p>\n\n                <div className=\"flex items-center gap-4 text-sm text-gray-500 mb-3\">\n                  <div className=\"flex items-center gap-1\">\n                    <Clock className=\"w-4 h-4\" />\n                    <span>{restaurant.preparationTime || 30} mins</span>\n                  </div>\n                  <div className=\"flex items-center gap-1\">\n                    <Truck className=\"w-4 h-4\" />\n                    <span>{formatCurrency(restaurant.pricing?.deliveryFee || 0)} delivery</span>\n                  </div>\n                  <div className=\"flex items-center gap-1\">\n                    <MapPin className=\"w-4 h-4\" />\n                    <span>{restaurant.location?.address?.city}</span>\n                  </div>\n                </div>\n\n                <div className=\"flex flex-wrap gap-2\">\n                  {restaurant.cuisine?.slice(0, 3).map((cuisine: string, index: number) => (\n                    <span\n                      key={`${restaurantId}-cuisine-${cuisine}-${index}`}\n                      className=\"px-2 py-1 bg-orange-100 text-orange-800 text-xs font-medium rounded-full\"\n                    >\n                      {cuisine}\n                    </span>\n                  ))}\n                  {restaurant.cuisine?.length > 3 && (\n                    <span className=\"px-2 py-1 bg-gray-100 text-gray-600 text-xs font-medium rounded-full\">\n                      +{restaurant.cuisine.length - 3} more\n                    </span>\n                  )}\n                </div>\n              </div>\n            </div>\n          </div>\n        </Link>\n      </motion.div>\n    );\n  }\n\n  return (\n    <motion.div\n      variants={cardVariants}\n      initial=\"hidden\"\n      animate=\"visible\"\n      whileHover=\"hover\"\n      layout\n    >\n      <Link href={`/restaurants/${restaurantId}`}>\n        <div className=\"bg-white rounded-2xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden\">\n          {/* Image */}\n          <div className=\"relative h-48\">\n            <Image\n              src={getOptimizedImageUrl(restaurant.images?.banner || '/images/restaurant-placeholder.jpg')}\n              alt={restaurant.name}\n              fill\n              className=\"object-cover\"\n            />\n            \n            {/* Status Badge */}\n            <div className=\"absolute top-3 left-3\">\n              <span className={`px-2 py-1 text-xs font-medium rounded-full ${\n                restaurant.isOpen \n                  ? 'bg-green-100 text-green-800' \n                  : 'bg-red-100 text-red-800'\n              }`}>\n                {restaurant.isOpen ? 'Open' : 'Closed'}\n              </span>\n            </div>\n\n            {/* Favorite Button */}\n            {showFavoriteButton && (\n              <motion.button\n                whileHover={{ scale: 1.1 }}\n                whileTap={{ scale: 0.9 }}\n                onClick={handleFavoriteToggle}\n                disabled={isLoading}\n                className=\"absolute top-3 right-3 w-8 h-8 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center shadow-md hover:bg-white transition-colors duration-200\"\n              >\n                <Heart\n                  className={`w-4 h-4 transition-colors duration-200 ${\n                    isFavorite ? 'text-red-500 fill-current' : 'text-gray-600'\n                  }`}\n                />\n              </motion.button>\n            )}\n\n            {/* Delivery Time Badge */}\n            <div className=\"absolute bottom-3 left-3\">\n              <div className=\"bg-white/90 backdrop-blur-sm px-2 py-1 rounded-full flex items-center gap-1\">\n                <Clock className=\"w-3 h-3 text-gray-600\" />\n                <span className=\"text-xs font-medium text-gray-700\">\n                  {restaurant.preparationTime || 30} mins\n                </span>\n              </div>\n            </div>\n          </div>\n\n          {/* Content */}\n          <div className=\"p-4\">\n            <div className=\"flex items-start justify-between mb-2\">\n              <h3 className=\"text-lg font-bold text-gray-900 line-clamp-1\">\n                {restaurant.name}\n              </h3>\n              <div className=\"flex items-center gap-1 ml-2\">\n                <Star className=\"w-4 h-4 text-yellow-400 fill-current\" />\n                <span className=\"text-sm font-medium text-gray-700\">\n                  {restaurant.ratings?.average?.toFixed(1) || '0.0'}\n                </span>\n              </div>\n            </div>\n\n            <p className=\"text-gray-600 text-sm mb-3 line-clamp-2\">\n              {restaurant.description}\n            </p>\n\n            <div className=\"flex items-center gap-4 text-sm text-gray-500 mb-3\">\n              <div className=\"flex items-center gap-1\">\n                <Truck className=\"w-4 h-4\" />\n                <span>{formatCurrency(restaurant.pricing?.deliveryFee || 0)}</span>\n              </div>\n              <div className=\"flex items-center gap-1\">\n                <MapPin className=\"w-4 h-4\" />\n                <span>{restaurant.location?.address?.city}</span>\n              </div>\n            </div>\n\n            <div className=\"flex flex-wrap gap-2\">\n              {restaurant.cuisine?.slice(0, 2).map((cuisine: string, index: number) => (\n                <span\n                  key={`${restaurantId}-cuisine-list-${cuisine}-${index}`}\n                  className=\"px-2 py-1 bg-orange-100 text-orange-800 text-xs font-medium rounded-full\"\n                >\n                  {cuisine}\n                </span>\n              ))}\n              {restaurant.cuisine?.length > 2 && (\n                <span className=\"px-2 py-1 bg-gray-100 text-gray-600 text-xs font-medium rounded-full\">\n                  +{restaurant.cuisine.length - 2}\n                </span>\n              )}\n            </div>\n          </div>\n        </div>\n      </Link>\n    </motion.div>\n  );\n};\n\nexport default RestaurantCard;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA;;;;;;;;;;;AAkBA,MAAM,iBAAiB,CAAC,EAAE,UAAU,EAAE,WAAW,MAAM,EAAE,qBAAqB,IAAI,EAAuB;IACvG,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,UAAY,AAAD;IAEvC,yDAAyD;IACzD,IAAI,CAAC,cAAe,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,EAAE,EAAG;QACtD,OAAO;IACT;IAEA,6BAA6B;IAC7B,MAAM,eAAe,WAAW,GAAG,IAAI,WAAW,EAAE;IAEpD,MAAM,uBAAuB,OAAO;QAClC,EAAE,cAAc;QAChB,EAAE,eAAe;QAEjB,IAAI,CAAC,iBAAiB;YACpB,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,aAAa;QACb,IAAI;YACF,IAAI,YAAY;gBACd,MAAM,iHAAA,CAAA,UAAO,CAAC,mBAAmB,CAAC;gBAClC,cAAc;gBACd,uJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,MAAM,iHAAA,CAAA,UAAO,CAAC,cAAc,CAAC;gBAC7B,cAAc;gBACd,uJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;QACF,EAAE,OAAO,OAAO;YACd,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBAAE,UAAU;YAAI;QAC9B;QACA,OAAO;YACL,GAAG,CAAC;YACJ,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,IAAI,aAAa,QAAQ;QACvB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,UAAU;YACV,SAAQ;YACR,SAAQ;YACR,YAAW;YACX,MAAM;sBAEN,cAAA,8OAAC,4JAAA,CAAA,UAAI;gBAAC,MAAM,CAAC,aAAa,EAAE,cAAc;0BACxC,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAK,CAAA,GAAA,mHAAA,CAAA,uBAAoB,AAAD,EAAE,WAAW,MAAM,EAAE,UAAU;wCACvD,KAAK,WAAW,IAAI;wCACpB,IAAI;wCACJ,WAAU;;;;;;kDAIZ,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAW,CAAC,2CAA2C,EAC3D,WAAW,MAAM,GACb,gCACA,2BACJ;sDACC,WAAW,MAAM,GAAG,SAAS;;;;;;;;;;;oCAKjC,oCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,YAAY;4CAAE,OAAO;wCAAI;wCACzB,UAAU;4CAAE,OAAO;wCAAI;wCACvB,SAAS;wCACT,UAAU;wCACV,WAAU;kDAEV,cAAA,8OAAC,oMAAA,CAAA,QAAK;4CACJ,WAAW,CAAC,uCAAuC,EACjD,aAAa,8BAA8B,iBAC3C;;;;;;;;;;;;;;;;;0CAOV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DACX,WAAW,IAAI;;;;;;0DAElB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,8OAAC;wDAAK,WAAU;kEACb,WAAW,OAAO,EAAE,SAAS,QAAQ,MAAM;;;;;;kEAE9C,8OAAC;wDAAK,WAAU;;4DAAwB;4DACpC,WAAW,OAAO,EAAE,SAAS;4DAAE;;;;;;;;;;;;;;;;;;;kDAKvC,8OAAC;wCAAE,WAAU;kDACV,WAAW,WAAW;;;;;;kDAGzB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;;4DAAM,WAAW,eAAe,IAAI;4DAAG;;;;;;;;;;;;;0DAE1C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;;4DAAM,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,OAAO,EAAE,eAAe;4DAAG;;;;;;;;;;;;;0DAE9D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC;kEAAM,WAAW,QAAQ,EAAE,SAAS;;;;;;;;;;;;;;;;;;kDAIzC,8OAAC;wCAAI,WAAU;;4CACZ,WAAW,OAAO,EAAE,MAAM,GAAG,GAAG,IAAI,CAAC,SAAiB,sBACrD,8OAAC;oDAEC,WAAU;8DAET;mDAHI,GAAG,aAAa,SAAS,EAAE,QAAQ,CAAC,EAAE,OAAO;;;;;4CAMrD,WAAW,OAAO,EAAE,SAAS,mBAC5B,8OAAC;gDAAK,WAAU;;oDAAuE;oDACnF,WAAW,OAAO,CAAC,MAAM,GAAG;oDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAUpD;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,UAAU;QACV,SAAQ;QACR,SAAQ;QACR,YAAW;QACX,MAAM;kBAEN,cAAA,8OAAC,4JAAA,CAAA,UAAI;YAAC,MAAM,CAAC,aAAa,EAAE,cAAc;sBACxC,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAK,CAAA,GAAA,mHAAA,CAAA,uBAAoB,AAAD,EAAE,WAAW,MAAM,EAAE,UAAU;gCACvD,KAAK,WAAW,IAAI;gCACpB,IAAI;gCACJ,WAAU;;;;;;0CAIZ,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAW,CAAC,2CAA2C,EAC3D,WAAW,MAAM,GACb,gCACA,2BACJ;8CACC,WAAW,MAAM,GAAG,SAAS;;;;;;;;;;;4BAKjC,oCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,YAAY;oCAAE,OAAO;gCAAI;gCACzB,UAAU;oCAAE,OAAO;gCAAI;gCACvB,SAAS;gCACT,UAAU;gCACV,WAAU;0CAEV,cAAA,8OAAC,oMAAA,CAAA,QAAK;oCACJ,WAAW,CAAC,uCAAuC,EACjD,aAAa,8BAA8B,iBAC3C;;;;;;;;;;;0CAMR,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;4CAAK,WAAU;;gDACb,WAAW,eAAe,IAAI;gDAAG;;;;;;;;;;;;;;;;;;;;;;;;kCAO1C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDACX,WAAW,IAAI;;;;;;kDAElB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;gDAAK,WAAU;0DACb,WAAW,OAAO,EAAE,SAAS,QAAQ,MAAM;;;;;;;;;;;;;;;;;;0CAKlD,8OAAC;gCAAE,WAAU;0CACV,WAAW,WAAW;;;;;;0CAGzB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;0DAAM,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,OAAO,EAAE,eAAe;;;;;;;;;;;;kDAE3D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;0DAAM,WAAW,QAAQ,EAAE,SAAS;;;;;;;;;;;;;;;;;;0CAIzC,8OAAC;gCAAI,WAAU;;oCACZ,WAAW,OAAO,EAAE,MAAM,GAAG,GAAG,IAAI,CAAC,SAAiB,sBACrD,8OAAC;4CAEC,WAAU;sDAET;2CAHI,GAAG,aAAa,cAAc,EAAE,QAAQ,CAAC,EAAE,OAAO;;;;;oCAM1D,WAAW,OAAO,EAAE,SAAS,mBAC5B,8OAAC;wCAAK,WAAU;;4CAAuE;4CACnF,WAAW,OAAO,CAAC,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAShD;uCAEe", "debugId": null}}, {"offset": {"line": 2177, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/project%20food-order%206k/food-ordering-system-modern/frontend/src/components/RestaurantFilters.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { Star, X } from 'lucide-react';\n\ninterface RestaurantFiltersProps {\n  filters: any;\n  onFilterChange: (filters: any) => void;\n  cuisineTypes: string[];\n}\n\nconst RestaurantFilters = ({ filters, onFilterChange, cuisineTypes }: RestaurantFiltersProps) => {\n  const handleCuisineChange = (cuisine: string) => {\n    const currentCuisines = filters.cuisine ? filters.cuisine.split(',') : [];\n    let newCuisines;\n    \n    if (currentCuisines.includes(cuisine)) {\n      newCuisines = currentCuisines.filter((c: string) => c !== cuisine);\n    } else {\n      newCuisines = [...currentCuisines, cuisine];\n    }\n    \n    onFilterChange({ cuisine: newCuisines.join(',') });\n  };\n\n  const clearFilters = () => {\n    onFilterChange({\n      cuisine: '',\n      minRating: '',\n      sortBy: 'ratings.average',\n      sortOrder: 'desc'\n    });\n  };\n\n  const selectedCuisines = filters.cuisine ? filters.cuisine.split(',') : [];\n  const hasActiveFilters = filters.cuisine || filters.minRating;\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <h3 className=\"text-lg font-semibold text-gray-900\">Filters</h3>\n        {hasActiveFilters && (\n          <motion.button\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n            onClick={clearFilters}\n            className=\"flex items-center gap-2 text-sm text-orange-600 hover:text-orange-700 font-medium\"\n          >\n            <X className=\"w-4 h-4\" />\n            Clear All\n          </motion.button>\n        )}\n      </div>\n\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n        {/* Cuisine Filter */}\n        <div>\n          <h4 className=\"text-sm font-medium text-gray-900 mb-3\">Cuisine Type</h4>\n          <div className=\"space-y-2 max-h-48 overflow-y-auto\">\n            {cuisineTypes.map((cuisine) => (\n              <label\n                key={cuisine}\n                className=\"flex items-center gap-3 cursor-pointer group\"\n              >\n                <input\n                  type=\"checkbox\"\n                  checked={selectedCuisines.includes(cuisine)}\n                  onChange={() => handleCuisineChange(cuisine)}\n                  className=\"h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded\"\n                />\n                <span className=\"text-sm text-gray-700 group-hover:text-gray-900 transition-colors duration-200\">\n                  {cuisine}\n                </span>\n              </label>\n            ))}\n          </div>\n        </div>\n\n        {/* Rating Filter */}\n        <div>\n          <h4 className=\"text-sm font-medium text-gray-900 mb-3\">Minimum Rating</h4>\n          <div className=\"space-y-2\">\n            {[4.5, 4.0, 3.5, 3.0].map((rating) => (\n              <label\n                key={rating}\n                className=\"flex items-center gap-3 cursor-pointer group\"\n              >\n                <input\n                  type=\"radio\"\n                  name=\"minRating\"\n                  value={rating}\n                  checked={filters.minRating === rating.toString()}\n                  onChange={(e) => onFilterChange({ minRating: e.target.value })}\n                  className=\"h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300\"\n                />\n                <div className=\"flex items-center gap-1\">\n                  <Star className=\"w-4 h-4 text-yellow-400 fill-current\" />\n                  <span className=\"text-sm text-gray-700 group-hover:text-gray-900 transition-colors duration-200\">\n                    {rating}+ and above\n                  </span>\n                </div>\n              </label>\n            ))}\n            <label className=\"flex items-center gap-3 cursor-pointer group\">\n              <input\n                type=\"radio\"\n                name=\"minRating\"\n                value=\"\"\n                checked={!filters.minRating}\n                onChange={(e) => onFilterChange({ minRating: e.target.value })}\n                className=\"h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300\"\n              />\n              <span className=\"text-sm text-gray-700 group-hover:text-gray-900 transition-colors duration-200\">\n                Any rating\n              </span>\n            </label>\n          </div>\n        </div>\n\n        {/* Quick Filters */}\n        <div>\n          <h4 className=\"text-sm font-medium text-gray-900 mb-3\">Quick Filters</h4>\n          <div className=\"space-y-2\">\n            <motion.button\n              whileHover={{ scale: 1.02 }}\n              whileTap={{ scale: 0.98 }}\n              onClick={() => onFilterChange({ sortBy: 'totalOrders', sortOrder: 'desc' })}\n              className={`w-full text-left px-3 py-2 rounded-lg text-sm transition-colors duration-200 ${\n                filters.sortBy === 'totalOrders' && filters.sortOrder === 'desc'\n                  ? 'bg-orange-100 text-orange-800 border border-orange-200'\n                  : 'bg-gray-50 text-gray-700 hover:bg-gray-100'\n              }`}\n            >\n              Most Popular\n            </motion.button>\n            \n            <motion.button\n              whileHover={{ scale: 1.02 }}\n              whileTap={{ scale: 0.98 }}\n              onClick={() => onFilterChange({ sortBy: 'ratings.average', sortOrder: 'desc' })}\n              className={`w-full text-left px-3 py-2 rounded-lg text-sm transition-colors duration-200 ${\n                filters.sortBy === 'ratings.average' && filters.sortOrder === 'desc'\n                  ? 'bg-orange-100 text-orange-800 border border-orange-200'\n                  : 'bg-gray-50 text-gray-700 hover:bg-gray-100'\n              }`}\n            >\n              Highest Rated\n            </motion.button>\n            \n            <motion.button\n              whileHover={{ scale: 1.02 }}\n              whileTap={{ scale: 0.98 }}\n              onClick={() => onFilterChange({ sortBy: 'createdAt', sortOrder: 'desc' })}\n              className={`w-full text-left px-3 py-2 rounded-lg text-sm transition-colors duration-200 ${\n                filters.sortBy === 'createdAt' && filters.sortOrder === 'desc'\n                  ? 'bg-orange-100 text-orange-800 border border-orange-200'\n                  : 'bg-gray-50 text-gray-700 hover:bg-gray-100'\n              }`}\n            >\n              Newest\n            </motion.button>\n          </div>\n        </div>\n      </div>\n\n      {/* Active Filters */}\n      {hasActiveFilters && (\n        <div className=\"pt-4 border-t border-gray-200\">\n          <h4 className=\"text-sm font-medium text-gray-900 mb-3\">Active Filters</h4>\n          <div className=\"flex flex-wrap gap-2\">\n            {selectedCuisines.map((cuisine) => (\n              <motion.span\n                key={cuisine}\n                initial={{ opacity: 0, scale: 0.8 }}\n                animate={{ opacity: 1, scale: 1 }}\n                exit={{ opacity: 0, scale: 0.8 }}\n                className=\"inline-flex items-center gap-1 px-3 py-1 bg-orange-100 text-orange-800 text-sm font-medium rounded-full\"\n              >\n                {cuisine}\n                <button\n                  onClick={() => handleCuisineChange(cuisine)}\n                  className=\"ml-1 hover:text-orange-900\"\n                >\n                  <X className=\"w-3 h-3\" />\n                </button>\n              </motion.span>\n            ))}\n            \n            {filters.minRating && (\n              <motion.span\n                initial={{ opacity: 0, scale: 0.8 }}\n                animate={{ opacity: 1, scale: 1 }}\n                exit={{ opacity: 0, scale: 0.8 }}\n                className=\"inline-flex items-center gap-1 px-3 py-1 bg-orange-100 text-orange-800 text-sm font-medium rounded-full\"\n              >\n                <Star className=\"w-3 h-3 fill-current\" />\n                {filters.minRating}+ rating\n                <button\n                  onClick={() => onFilterChange({ minRating: '' })}\n                  className=\"ml-1 hover:text-orange-900\"\n                >\n                  <X className=\"w-3 h-3\" />\n                </button>\n              </motion.span>\n            )}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default RestaurantFilters;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AAWA,MAAM,oBAAoB,CAAC,EAAE,OAAO,EAAE,cAAc,EAAE,YAAY,EAA0B;IAC1F,MAAM,sBAAsB,CAAC;QAC3B,MAAM,kBAAkB,QAAQ,OAAO,GAAG,QAAQ,OAAO,CAAC,KAAK,CAAC,OAAO,EAAE;QACzE,IAAI;QAEJ,IAAI,gBAAgB,QAAQ,CAAC,UAAU;YACrC,cAAc,gBAAgB,MAAM,CAAC,CAAC,IAAc,MAAM;QAC5D,OAAO;YACL,cAAc;mBAAI;gBAAiB;aAAQ;QAC7C;QAEA,eAAe;YAAE,SAAS,YAAY,IAAI,CAAC;QAAK;IAClD;IAEA,MAAM,eAAe;QACnB,eAAe;YACb,SAAS;YACT,WAAW;YACX,QAAQ;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB,QAAQ,OAAO,GAAG,QAAQ,OAAO,CAAC,KAAK,CAAC,OAAO,EAAE;IAC1E,MAAM,mBAAmB,QAAQ,OAAO,IAAI,QAAQ,SAAS;IAE7D,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;oBACnD,kCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wBACZ,YAAY;4BAAE,OAAO;wBAAK;wBAC1B,UAAU;4BAAE,OAAO;wBAAK;wBACxB,SAAS;wBACT,WAAU;;0CAEV,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;;0BAM/B,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAI,WAAU;0CACZ,aAAa,GAAG,CAAC,CAAC,wBACjB,8OAAC;wCAEC,WAAU;;0DAEV,8OAAC;gDACC,MAAK;gDACL,SAAS,iBAAiB,QAAQ,CAAC;gDACnC,UAAU,IAAM,oBAAoB;gDACpC,WAAU;;;;;;0DAEZ,8OAAC;gDAAK,WAAU;0DACb;;;;;;;uCAVE;;;;;;;;;;;;;;;;kCAkBb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAI,WAAU;;oCACZ;wCAAC;wCAAK;wCAAK;wCAAK;qCAAI,CAAC,GAAG,CAAC,CAAC,uBACzB,8OAAC;4CAEC,WAAU;;8DAEV,8OAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAO;oDACP,SAAS,QAAQ,SAAS,KAAK,OAAO,QAAQ;oDAC9C,UAAU,CAAC,IAAM,eAAe;4DAAE,WAAW,EAAE,MAAM,CAAC,KAAK;wDAAC;oDAC5D,WAAU;;;;;;8DAEZ,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;4DAAK,WAAU;;gEACb;gEAAO;;;;;;;;;;;;;;2CAdP;;;;;kDAmBT,8OAAC;wCAAM,WAAU;;0DACf,8OAAC;gDACC,MAAK;gDACL,MAAK;gDACL,OAAM;gDACN,SAAS,CAAC,QAAQ,SAAS;gDAC3B,UAAU,CAAC,IAAM,eAAe;wDAAE,WAAW,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAC5D,WAAU;;;;;;0DAEZ,8OAAC;gDAAK,WAAU;0DAAiF;;;;;;;;;;;;;;;;;;;;;;;;kCAQvG,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,SAAS,IAAM,eAAe;gDAAE,QAAQ;gDAAe,WAAW;4CAAO;wCACzE,WAAW,CAAC,6EAA6E,EACvF,QAAQ,MAAM,KAAK,iBAAiB,QAAQ,SAAS,KAAK,SACtD,2DACA,8CACJ;kDACH;;;;;;kDAID,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,SAAS,IAAM,eAAe;gDAAE,QAAQ;gDAAmB,WAAW;4CAAO;wCAC7E,WAAW,CAAC,6EAA6E,EACvF,QAAQ,MAAM,KAAK,qBAAqB,QAAQ,SAAS,KAAK,SAC1D,2DACA,8CACJ;kDACH;;;;;;kDAID,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,SAAS,IAAM,eAAe;gDAAE,QAAQ;gDAAa,WAAW;4CAAO;wCACvE,WAAW,CAAC,6EAA6E,EACvF,QAAQ,MAAM,KAAK,eAAe,QAAQ,SAAS,KAAK,SACpD,2DACA,8CACJ;kDACH;;;;;;;;;;;;;;;;;;;;;;;;YAQN,kCACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,8OAAC;wBAAI,WAAU;;4BACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oCAEV,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCAChC,MAAM;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAC/B,WAAU;;wCAET;sDACD,8OAAC;4CACC,SAAS,IAAM,oBAAoB;4CACnC,WAAU;sDAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;;;;;;;mCAXV;;;;;4BAgBR,QAAQ,SAAS,kBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;gCACV,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCAChC,MAAM;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAC/B,WAAU;;kDAEV,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCACf,QAAQ,SAAS;oCAAC;kDACnB,8OAAC;wCACC,SAAS,IAAM,eAAe;gDAAE,WAAW;4CAAG;wCAC9C,WAAU;kDAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS/B;uCAEe", "debugId": null}}, {"offset": {"line": 2629, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/project%20food-order%206k/food-ordering-system-modern/frontend/src/app/restaurants/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { \n  Search, \n  Filter, \n  MapPin, \n  Star, \n  Clock, \n  Truck,\n  Heart,\n  ChefHat,\n  Grid3X3,\n  List,\n  SlidersHorizontal\n} from 'lucide-react';\nimport { useQuery } from '@tanstack/react-query';\nimport Header from '@/components/Header';\nimport Footer from '@/components/Footer';\nimport RestaurantCard from '@/components/RestaurantCard';\nimport RestaurantFilters from '@/components/RestaurantFilters';\nimport { restaurantAPI } from '@/lib/api';\nimport { formatCurrency } from '@/lib/utils';\n\nconst RestaurantsPage = () => {\n  const [searchQuery, setSearchQuery] = useState('');\n  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');\n  const [showFilters, setShowFilters] = useState(false);\n  const [filters, setFilters] = useState({\n    cuisine: '',\n    minRating: '',\n    sortBy: 'ratings.average',\n    sortOrder: 'desc',\n    page: 1,\n    limit: 12\n  });\n\n  // Fetch restaurants\n  const { data: restaurantsData, isLoading, error } = useQuery({\n    queryKey: ['restaurants', { ...filters, search: searchQuery }],\n    queryFn: () => restaurantAPI.getAll({\n      ...filters,\n      search: searchQuery || undefined\n    }),\n    placeholderData: (previousData) => previousData,\n  });\n\n  const restaurants = restaurantsData?.data?.data || [];\n  const totalPages = restaurantsData?.data?.pages || 1;\n  const currentPage = restaurantsData?.data?.currentPage || 1;\n\n  const handleFilterChange = (newFilters: any) => {\n    setFilters(prev => ({ ...prev, ...newFilters, page: 1 }));\n  };\n\n  const handlePageChange = (page: number) => {\n    setFilters(prev => ({ ...prev, page }));\n  };\n\n  const cuisineTypes = [\n    'Italian', 'Chinese', 'Indian', 'Mexican', 'Thai', 'Japanese', \n    'American', 'Mediterranean', 'French', 'Korean'\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Header />\n      \n      {/* Hero Section */}\n      <section className=\"pt-24 pb-8 bg-gradient-to-r from-orange-500 to-red-500\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            className=\"text-center text-white\"\n          >\n            <h1 className=\"text-3xl sm:text-4xl font-bold mb-4\">\n              Discover Amazing Restaurants\n            </h1>\n            <p className=\"text-xl opacity-90 mb-8\">\n              Find and order from the best restaurants in your area\n            </p>\n\n            {/* Search Bar */}\n            <div className=\"max-w-2xl mx-auto\">\n              <div className=\"relative shadow-2xl\">\n                <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\">\n                  <Search className=\"h-6 w-6 text-gray-400\" />\n                </div>\n                <input\n                  type=\"text\"\n                  value={searchQuery}\n                  onChange={(e) => setSearchQuery(e.target.value)}\n                  className=\"block w-full pl-14 pr-4 py-5 border-0 rounded-2xl text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-4 focus:ring-white/30 focus:ring-offset-2 focus:ring-offset-orange-500 text-lg font-medium shadow-xl\"\n                  placeholder=\"Search restaurants, cuisines, or dishes...\"\n                />\n              </div>\n            </div>\n          </motion.div>\n        </div>\n      </section>\n\n      {/* Filters and Controls */}\n      <section className=\"py-6 bg-white border-b border-gray-200\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex flex-col sm:flex-row items-center justify-between gap-4\">\n            {/* Results Info */}\n            <div className=\"flex items-center gap-4\">\n              <p className=\"text-gray-600\">\n                {isLoading ? 'Loading...' : `${restaurants.length} restaurants found`}\n              </p>\n            </div>\n\n            {/* Controls */}\n            <div className=\"flex items-center gap-4\">\n              {/* View Mode Toggle */}\n              <div className=\"flex items-center bg-gray-100 rounded-lg p-1\">\n                <button\n                  onClick={() => setViewMode('grid')}\n                  className={`p-2 rounded-md transition-colors duration-200 ${\n                    viewMode === 'grid' \n                      ? 'bg-white text-orange-500 shadow-sm' \n                      : 'text-gray-500 hover:text-gray-700'\n                  }`}\n                >\n                  <Grid3X3 className=\"w-4 h-4\" />\n                </button>\n                <button\n                  onClick={() => setViewMode('list')}\n                  className={`p-2 rounded-md transition-colors duration-200 ${\n                    viewMode === 'list' \n                      ? 'bg-white text-orange-500 shadow-sm' \n                      : 'text-gray-500 hover:text-gray-700'\n                  }`}\n                >\n                  <List className=\"w-4 h-4\" />\n                </button>\n              </div>\n\n              {/* Filters Button */}\n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                onClick={() => setShowFilters(!showFilters)}\n                className=\"flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors duration-200\"\n              >\n                <SlidersHorizontal className=\"w-4 h-4\" />\n                Filters\n              </motion.button>\n\n              {/* Sort Dropdown */}\n              <select\n                value={`${filters.sortBy}-${filters.sortOrder}`}\n                onChange={(e) => {\n                  const [sortBy, sortOrder] = e.target.value.split('-');\n                  handleFilterChange({ sortBy, sortOrder });\n                }}\n                className=\"px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent\"\n              >\n                <option value=\"ratings.average-desc\">Highest Rated</option>\n                <option value=\"totalOrders-desc\">Most Popular</option>\n                <option value=\"name-asc\">Name A-Z</option>\n                <option value=\"createdAt-desc\">Newest</option>\n              </select>\n            </div>\n          </div>\n\n          {/* Filters Panel */}\n          <AnimatePresence>\n            {showFilters && (\n              <motion.div\n                initial={{ opacity: 0, height: 0 }}\n                animate={{ opacity: 1, height: 'auto' }}\n                exit={{ opacity: 0, height: 0 }}\n                transition={{ duration: 0.3 }}\n                className=\"mt-6 p-6 bg-gray-50 rounded-lg\"\n              >\n                <RestaurantFilters\n                  filters={filters}\n                  onFilterChange={handleFilterChange}\n                  cuisineTypes={cuisineTypes}\n                />\n              </motion.div>\n            )}\n          </AnimatePresence>\n        </div>\n      </section>\n\n      {/* Restaurants Grid */}\n      <section className=\"py-8\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          {isLoading ? (\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n              {[...Array(8)].map((_, index) => (\n                <div key={index} className=\"animate-pulse\">\n                  <div className=\"bg-gray-200 rounded-2xl h-48 mb-4\"></div>\n                  <div className=\"space-y-2\">\n                    <div className=\"h-4 bg-gray-200 rounded w-3/4\"></div>\n                    <div className=\"h-3 bg-gray-200 rounded w-1/2\"></div>\n                    <div className=\"h-3 bg-gray-200 rounded w-2/3\"></div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          ) : error ? (\n            <div className=\"text-center py-12\">\n              <div className=\"text-gray-500 mb-4\">\n                <ChefHat className=\"w-16 h-16 mx-auto mb-4 opacity-50\" />\n                <p className=\"text-lg\">Something went wrong</p>\n                <p className=\"text-sm\">Please try again later</p>\n              </div>\n            </div>\n          ) : restaurants.length === 0 ? (\n            <div className=\"text-center py-12\">\n              <div className=\"text-gray-500 mb-4\">\n                <Search className=\"w-16 h-16 mx-auto mb-4 opacity-50\" />\n                <p className=\"text-lg\">No restaurants found</p>\n                <p className=\"text-sm\">Try adjusting your search or filters</p>\n              </div>\n            </div>\n          ) : (\n            <motion.div\n              layout\n              className={`grid gap-6 ${\n                viewMode === 'grid'\n                  ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'\n                  : 'grid-cols-1'\n              }`}\n            >\n              <AnimatePresence>\n                {restaurants.map((restaurant: any) => (\n                  <RestaurantCard\n                    key={restaurant._id}\n                    restaurant={restaurant}\n                    viewMode={viewMode}\n                  />\n                ))}\n              </AnimatePresence>\n            </motion.div>\n          )}\n\n          {/* Pagination */}\n          {totalPages > 1 && (\n            <div className=\"mt-12 flex justify-center\">\n              <nav className=\"flex items-center gap-2\">\n                <button\n                  onClick={() => handlePageChange(currentPage - 1)}\n                  disabled={currentPage === 1}\n                  className=\"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\n                >\n                  Previous\n                </button>\n                \n                {[...Array(totalPages)].map((_, index) => {\n                  const page = index + 1;\n                  return (\n                    <button\n                      key={page}\n                      onClick={() => handlePageChange(page)}\n                      className={`px-3 py-2 text-sm font-medium rounded-md ${\n                        currentPage === page\n                          ? 'text-white bg-orange-500 border border-orange-500'\n                          : 'text-gray-500 bg-white border border-gray-300 hover:bg-gray-50'\n                      }`}\n                    >\n                      {page}\n                    </button>\n                  );\n                })}\n                \n                <button\n                  onClick={() => handlePageChange(currentPage + 1)}\n                  disabled={currentPage === totalPages}\n                  className=\"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\n                >\n                  Next\n                </button>\n              </nav>\n            </div>\n          )}\n        </div>\n      </section>\n\n      <Footer />\n    </div>\n  );\n};\n\nexport default RestaurantsPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAaA;AACA;AACA;AACA;AACA;AACA;AAtBA;;;;;;;;;;;AAyBA,MAAM,kBAAkB;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACrC,SAAS;QACT,WAAW;QACX,QAAQ;QACR,WAAW;QACX,MAAM;QACN,OAAO;IACT;IAEA,oBAAoB;IACpB,MAAM,EAAE,MAAM,eAAe,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QAC3D,UAAU;YAAC;YAAe;gBAAE,GAAG,OAAO;gBAAE,QAAQ;YAAY;SAAE;QAC9D,SAAS,IAAM,iHAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;gBAClC,GAAG,OAAO;gBACV,QAAQ,eAAe;YACzB;QACA,iBAAiB,CAAC,eAAiB;IACrC;IAEA,MAAM,cAAc,iBAAiB,MAAM,QAAQ,EAAE;IACrD,MAAM,aAAa,iBAAiB,MAAM,SAAS;IACnD,MAAM,cAAc,iBAAiB,MAAM,eAAe;IAE1D,MAAM,qBAAqB,CAAC;QAC1B,WAAW,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,GAAG,UAAU;gBAAE,MAAM;YAAE,CAAC;IACzD;IAEA,MAAM,mBAAmB,CAAC;QACxB,WAAW,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE;YAAK,CAAC;IACvC;IAEA,MAAM,eAAe;QACnB;QAAW;QAAW;QAAU;QAAW;QAAQ;QACnD;QAAY;QAAiB;QAAU;KACxC;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,UAAM;;;;;0BAGP,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAGpD,8OAAC;gCAAE,WAAU;0CAA0B;;;;;;0CAKvC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,8OAAC;4CACC,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4CAC9C,WAAU;4CACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASxB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;kDACV,YAAY,eAAe,GAAG,YAAY,MAAM,CAAC,kBAAkB,CAAC;;;;;;;;;;;8CAKzE,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS,IAAM,YAAY;oDAC3B,WAAW,CAAC,8CAA8C,EACxD,aAAa,SACT,uCACA,qCACJ;8DAEF,cAAA,8OAAC,4MAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;;;;;;8DAErB,8OAAC;oDACC,SAAS,IAAM,YAAY;oDAC3B,WAAW,CAAC,8CAA8C,EACxD,aAAa,SACT,uCACA,qCACJ;8DAEF,cAAA,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAKpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4CACZ,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,UAAU;gDAAE,OAAO;4CAAK;4CACxB,SAAS,IAAM,eAAe,CAAC;4CAC/B,WAAU;;8DAEV,8OAAC,gOAAA,CAAA,oBAAiB;oDAAC,WAAU;;;;;;gDAAY;;;;;;;sDAK3C,8OAAC;4CACC,OAAO,GAAG,QAAQ,MAAM,CAAC,CAAC,EAAE,QAAQ,SAAS,EAAE;4CAC/C,UAAU,CAAC;gDACT,MAAM,CAAC,QAAQ,UAAU,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC;gDACjD,mBAAmB;oDAAE;oDAAQ;gDAAU;4CACzC;4CACA,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAuB;;;;;;8DACrC,8OAAC;oDAAO,OAAM;8DAAmB;;;;;;8DACjC,8OAAC;oDAAO,OAAM;8DAAW;;;;;;8DACzB,8OAAC;oDAAO,OAAM;8DAAiB;;;;;;;;;;;;;;;;;;;;;;;;sCAMrC,8OAAC,yLAAA,CAAA,kBAAe;sCACb,6BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,QAAQ;gCAAE;gCACjC,SAAS;oCAAE,SAAS;oCAAG,QAAQ;gCAAO;gCACtC,MAAM;oCAAE,SAAS;oCAAG,QAAQ;gCAAE;gCAC9B,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,WAAU;0CAEV,cAAA,8OAAC,uIAAA,CAAA,UAAiB;oCAChB,SAAS;oCACT,gBAAgB;oCAChB,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS1B,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;wBACZ,0BACC,8OAAC;4BAAI,WAAU;sCACZ;mCAAI,MAAM;6BAAG,CAAC,GAAG,CAAC,CAAC,GAAG,sBACrB,8OAAC;oCAAgB,WAAU;;sDACzB,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;;;;;;;;;;;;mCALT;;;;;;;;;uEAUZ,sBACF,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,8OAAC;wCAAE,WAAU;kDAAU;;;;;;kDACvB,8OAAC;wCAAE,WAAU;kDAAU;;;;;;;;;;;;;;;;uEAGzB,YAAY,MAAM,KAAK,kBACzB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCAAE,WAAU;kDAAU;;;;;;kDACvB,8OAAC;wCAAE,WAAU;kDAAU;;;;;;;;;;;;;;;;qFAI3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,MAAM;4BACN,WAAW,CAAC,WAAW,EACrB,aAAa,SACT,6DACA,eACJ;sCAEF,cAAA,8OAAC,yLAAA,CAAA,kBAAe;0CACb,YAAY,GAAG,CAAC,CAAC,2BAChB,8OAAC,oIAAA,CAAA,UAAc;wCAEb,YAAY;wCACZ,UAAU;uCAFL,WAAW,GAAG;;;;;;;;;;;;;;;wBAU5B,aAAa,mBACZ,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,iBAAiB,cAAc;wCAC9C,UAAU,gBAAgB;wCAC1B,WAAU;kDACX;;;;;;oCAIA;2CAAI,MAAM;qCAAY,CAAC,GAAG,CAAC,CAAC,GAAG;wCAC9B,MAAM,OAAO,QAAQ;wCACrB,qBACE,8OAAC;4CAEC,SAAS,IAAM,iBAAiB;4CAChC,WAAW,CAAC,yCAAyC,EACnD,gBAAgB,OACZ,sDACA,kEACJ;sDAED;2CARI;;;;;oCAWX;kDAEA,8OAAC;wCACC,SAAS,IAAM,iBAAiB,cAAc;wCAC9C,UAAU,gBAAgB;wCAC1B,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASX,8OAAC,4HAAA,CAAA,UAAM;;;;;;;;;;;AAGb;uCAEe", "debugId": null}}]}