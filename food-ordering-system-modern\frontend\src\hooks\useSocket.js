import React, { useEffect, useRef } from 'react';
import { io } from 'socket.io-client';
import { toast } from 'react-hot-toast';
import useAuthStore from '@/store/useAuthStore';

const useSocket = () => {
  const socketRef = useRef(null);
  const { user, isAuthenticated } = useAuthStore();

  useEffect(() => {
    if (!isAuthenticated || !user) return;

    // Initialize socket connection
    socketRef.current = io(process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000', {
      withCredentials: true,
      transports: ['websocket', 'polling']
    });

    const socket = socketRef.current;

    // Connection events
    socket.on('connect', () => {
      console.log('Connected to server');
      
      // Join user-specific room
      socket.emit('join-user-room', user._id);
      
      // Join restaurant room if user is a vendor
      if (user.role === 'vendor' && user.restaurant) {
        socket.emit('join-restaurant-room', user.restaurant._id);
      }
    });

    socket.on('disconnect', () => {
      console.log('Disconnected from server');
    });

    // Order-related events for customers
    if (user.role === 'customer') {
      socket.on('order-status-changed', (data) => {
        toast.success(`Order ${data.status}: ${data.message}`);
        // You can dispatch to a global state or trigger a refetch here
      });

      socket.on('order-placed', (data) => {
        toast.success('Order placed successfully!');
      });
    }

    // Restaurant-related events for vendors
    if (user.role === 'vendor') {
      socket.on('new-order-received', (orderData) => {
        toast.success('New order received!');
        // Play notification sound
        playNotificationSound();
      });

      socket.on('payment-received', (data) => {
        toast.success(`Payment received: ₹${data.amount}`);
      });

      socket.on('order-cancelled', (data) => {
        toast.error(`Order cancelled: ${data.reason}`);
      });
    }

    // Admin events
    if (user.role === 'admin') {
      socket.on('new-restaurant-registration', (data) => {
        toast.info('New restaurant registration pending approval');
      });

      socket.on('new-user-registration', (data) => {
        toast.info('New user registered');
      });
    }

    // Cleanup on unmount
    return () => {
      if (socket) {
        socket.disconnect();
      }
    };
  }, [isAuthenticated, user]);

  // Helper function to play notification sound
  const playNotificationSound = () => {
    try {
      const audio = new Audio('/sounds/notification.mp3');
      audio.play().catch(console.error);
    } catch (error) {
      console.error('Error playing notification sound:', error);
    }
  };

  // Emit order status update (for vendors)
  const updateOrderStatus = (orderData) => {
    if (socketRef.current) {
      socketRef.current.emit('order-status-update', orderData);
    }
  };

  // Emit new order (for customers)
  const emitNewOrder = (orderData) => {
    if (socketRef.current) {
      socketRef.current.emit('new-order', orderData);
    }
  };

  return {
    socket: socketRef.current,
    updateOrderStatus,
    emitNewOrder,
    isConnected: socketRef.current?.connected || false
  };
};

export default useSocket;
