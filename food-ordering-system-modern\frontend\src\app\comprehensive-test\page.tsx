'use client';

import React, { useState, useEffect } from 'react';
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  AlertCircle, 
  Upload, 
  Image as ImageIcon,
  Server,
  Globe,
  Database,
  User,
  ShoppingCart,
  Settings,
  BarChart,
  Bell,
  Star,
  Truck,
  Shield,
  Play,
  Pause,
  RotateCcw,
  ExternalLink,
  FileText,
  Camera,
  CreditCard,
  MapPin
} from 'lucide-react';
import Link from 'next/link';

interface TestResult {
  name: string;
  category: string;
  status: 'success' | 'error' | 'loading' | 'pending' | 'skipped';
  message?: string;
  data?: any;
  duration?: number;
  details?: string;
}

interface APITest {
  name: string;
  category: string;
  endpoint: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  requiresAuth?: boolean;
  testData?: any;
  description: string;
  expectedStatus?: number;
}

interface PageTest {
  name: string;
  category: string;
  path: string;
  description: string;
  requiresAuth?: boolean;
  userType?: 'customer' | 'vendor' | 'admin' | 'delivery';
  features: string[];
}

const ComprehensiveTestPage = () => {
  const [results, setResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [activeTab, setActiveTab] = useState<'apis' | 'pages' | 'features'>('apis');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [testProgress, setTestProgress] = useState(0);

  // Comprehensive API Tests
  const apiTests: APITest[] = [
    // Authentication APIs
    { name: 'Health Check', category: 'System', endpoint: '/api/health', method: 'GET', description: 'Check if backend is running', expectedStatus: 200 },
    { name: 'Get Current User', category: 'Auth', endpoint: '/api/auth/me', method: 'GET', requiresAuth: true, description: 'Get authenticated user info' },
    { name: 'Refresh Token', category: 'Auth', endpoint: '/api/auth/refresh', method: 'POST', description: 'Refresh authentication token' },
    { name: 'Logout', category: 'Auth', endpoint: '/api/auth/logout', method: 'POST', requiresAuth: true, description: 'User logout' },
    
    // Restaurant APIs
    { name: 'Get All Restaurants', category: 'Restaurants', endpoint: '/api/restaurants', method: 'GET', description: 'Fetch all restaurants' },
    { name: 'Get Restaurant by ID', category: 'Restaurants', endpoint: '/api/restaurants/test-id', method: 'GET', description: 'Fetch specific restaurant' },
    { name: 'Search Restaurants', category: 'Restaurants', endpoint: '/api/restaurants?search=pizza', method: 'GET', description: 'Search restaurants by query' },
    { name: 'Filter by Cuisine', category: 'Restaurants', endpoint: '/api/restaurants?cuisine=italian', method: 'GET', description: 'Filter restaurants by cuisine' },
    { name: 'Filter by Rating', category: 'Restaurants', endpoint: '/api/restaurants?minRating=4', method: 'GET', description: 'Filter restaurants by rating' },
    
    // Food APIs
    { name: 'Get All Foods', category: 'Foods', endpoint: '/api/foods', method: 'GET', description: 'Fetch all food items' },
    { name: 'Get Foods by Restaurant', category: 'Foods', endpoint: '/api/foods/restaurant/test-id', method: 'GET', description: 'Get foods for specific restaurant' },
    { name: 'Search Foods', category: 'Foods', endpoint: '/api/foods?search=burger', method: 'GET', description: 'Search food items' },
    { name: 'Filter by Category', category: 'Foods', endpoint: '/api/foods?category=main-course', method: 'GET', description: 'Filter foods by category' },
    { name: 'Filter by Dietary', category: 'Foods', endpoint: '/api/foods?isVegetarian=true', method: 'GET', description: 'Filter by dietary preferences' },
    
    // Vendor APIs
    { name: 'Get Vendor Restaurant', category: 'Vendor', endpoint: '/api/vendors/restaurant', method: 'GET', requiresAuth: true, description: 'Get vendor restaurant info' },
    { name: 'Get Vendor Foods', category: 'Vendor', endpoint: '/api/vendors/foods', method: 'GET', requiresAuth: true, description: 'Get vendor food items' },
    { name: 'Get Vendor Orders', category: 'Vendor', endpoint: '/api/vendors/orders', method: 'GET', requiresAuth: true, description: 'Get vendor orders' },
    { name: 'Get Recent Orders', category: 'Vendor', endpoint: '/api/vendors/orders/recent?limit=5', method: 'GET', requiresAuth: true, description: 'Get recent orders' },
    { name: 'Get Order Stats', category: 'Vendor', endpoint: '/api/vendors/orders/stats', method: 'GET', requiresAuth: true, description: 'Get order statistics' },
    { name: 'Get Analytics', category: 'Vendor', endpoint: '/api/vendors/analytics?period=7d', method: 'GET', requiresAuth: true, description: 'Get 7-day analytics' },
    { name: 'Get Monthly Analytics', category: 'Vendor', endpoint: '/api/vendors/analytics?period=30d', method: 'GET', requiresAuth: true, description: 'Get 30-day analytics' },
    { name: 'Get Reviews', category: 'Vendor', endpoint: '/api/vendors/reviews?rating=all', method: 'GET', requiresAuth: true, description: 'Get all reviews' },
    { name: 'Get 5-Star Reviews', category: 'Vendor', endpoint: '/api/vendors/reviews?rating=5', method: 'GET', requiresAuth: true, description: 'Get 5-star reviews' },
    { name: 'Get Customers', category: 'Vendor', endpoint: '/api/vendors/customers', method: 'GET', requiresAuth: true, description: 'Get customer list' },
    { name: 'Get Notifications', category: 'Vendor', endpoint: '/api/vendors/notifications', method: 'GET', requiresAuth: true, description: 'Get notifications' },
    
    // Upload APIs
    { name: 'Upload Single Image', category: 'Upload', endpoint: '/api/upload/image', method: 'POST', requiresAuth: true, description: 'Upload single image file' },
    { name: 'Upload Multiple Images', category: 'Upload', endpoint: '/api/upload/images', method: 'POST', requiresAuth: true, description: 'Upload multiple images' },
    { name: 'Upload Food Images', category: 'Upload', endpoint: '/api/upload/food/images', method: 'POST', requiresAuth: true, description: 'Upload food images' },
    { name: 'Upload Restaurant Logo', category: 'Upload', endpoint: '/api/upload/restaurant/logo', method: 'POST', requiresAuth: true, description: 'Upload restaurant logo' },
    { name: 'Upload Restaurant Banner', category: 'Upload', endpoint: '/api/upload/restaurant/banner', method: 'POST', requiresAuth: true, description: 'Upload restaurant banner' },
    
    // Order APIs
    { name: 'Get All Orders', category: 'Orders', endpoint: '/api/orders', method: 'GET', requiresAuth: true, description: 'Get all orders' },
    { name: 'Get Order by ID', category: 'Orders', endpoint: '/api/orders/test-id', method: 'GET', requiresAuth: true, description: 'Get specific order' },
    { name: 'Get Order History', category: 'Orders', endpoint: '/api/orders/history', method: 'GET', requiresAuth: true, description: 'Get order history' },
    
    // User APIs
    { name: 'Get User Profile', category: 'Users', endpoint: '/api/users/profile', method: 'GET', requiresAuth: true, description: 'Get user profile' },
    { name: 'Get All Users', category: 'Users', endpoint: '/api/users', method: 'GET', requiresAuth: true, description: 'Get all users (admin)' },
    
    // Admin APIs
    { name: 'Admin Dashboard', category: 'Admin', endpoint: '/api/admin/dashboard', method: 'GET', requiresAuth: true, description: 'Get admin dashboard data' },
    { name: 'Admin Users', category: 'Admin', endpoint: '/api/admin/users', method: 'GET', requiresAuth: true, description: 'Get all users for admin' },
    { name: 'Admin Restaurants', category: 'Admin', endpoint: '/api/admin/restaurants', method: 'GET', requiresAuth: true, description: 'Get all restaurants for admin' },
    { name: 'Admin Orders', category: 'Admin', endpoint: '/api/admin/orders', method: 'GET', requiresAuth: true, description: 'Get all orders for admin' },
    
    // Delivery APIs
    { name: 'Delivery Orders', category: 'Delivery', endpoint: '/api/delivery/orders', method: 'GET', requiresAuth: true, description: 'Get delivery orders' },
    { name: 'Available Orders', category: 'Delivery', endpoint: '/api/delivery/orders/available', method: 'GET', requiresAuth: true, description: 'Get available delivery orders' },
    
    // Payment APIs
    { name: 'Payment Methods', category: 'Payment', endpoint: '/api/payments/methods', method: 'GET', requiresAuth: true, description: 'Get payment methods' },
    { name: 'Payment History', category: 'Payment', endpoint: '/api/payments/history', method: 'GET', requiresAuth: true, description: 'Get payment history' },
    
    // Notification APIs
    { name: 'Get Notifications', category: 'Notifications', endpoint: '/api/notifications', method: 'GET', requiresAuth: true, description: 'Get user notifications' },
    { name: 'Mark as Read', category: 'Notifications', endpoint: '/api/notifications/read', method: 'POST', requiresAuth: true, description: 'Mark notifications as read' }
  ];

  // Comprehensive Page Tests
  const pageTests: PageTest[] = [
    // Public Pages
    { 
      name: 'Home Page', 
      category: 'Public', 
      path: '/', 
      description: 'Landing page with restaurant listings',
      features: ['Restaurant grid', 'Search functionality', 'Filter options', 'Hero section', 'Navigation']
    },
    { 
      name: 'Restaurants Page', 
      category: 'Public', 
      path: '/restaurants', 
      description: 'Restaurant browsing and filtering',
      features: ['Restaurant cards', 'Search bar', 'Cuisine filters', 'Rating filters', 'Pagination']
    },
    { 
      name: 'Restaurant Details', 
      category: 'Public', 
      path: '/restaurants/test-id', 
      description: 'Individual restaurant page',
      features: ['Restaurant info', 'Menu display', 'Reviews', 'Add to cart', 'Image gallery']
    },
    { 
      name: 'Login Page', 
      category: 'Auth', 
      path: '/auth/login', 
      description: 'User authentication',
      features: ['Login form', 'Role selection', 'Remember me', 'Forgot password', 'Social login']
    },
    { 
      name: 'Register Page', 
      category: 'Auth', 
      path: '/auth/register', 
      description: 'User registration',
      features: ['Registration form', 'Email verification', 'Terms acceptance', 'Role selection']
    },
    
    // Customer Pages
    { 
      name: 'Customer Dashboard', 
      category: 'Customer', 
      path: '/customer/dashboard', 
      description: 'Customer main dashboard',
      requiresAuth: true, 
      userType: 'customer',
      features: ['Order history', 'Favorite restaurants', 'Quick reorder', 'Profile summary', 'Recommendations']
    },
    { 
      name: 'Customer Orders', 
      category: 'Customer', 
      path: '/customer/orders', 
      description: 'Order history and tracking',
      requiresAuth: true, 
      userType: 'customer',
      features: ['Order list', 'Order tracking', 'Reorder button', 'Order details', 'Cancel order']
    },
    { 
      name: 'Customer Profile', 
      category: 'Customer', 
      path: '/customer/profile', 
      description: 'Profile management',
      requiresAuth: true, 
      userType: 'customer',
      features: ['Personal info', 'Address management', 'Payment methods', 'Preferences', 'Avatar upload']
    },
    { 
      name: 'Shopping Cart', 
      category: 'Customer', 
      path: '/customer/cart', 
      description: 'Shopping cart management',
      requiresAuth: true, 
      userType: 'customer',
      features: ['Item list', 'Quantity controls', 'Remove items', 'Total calculation', 'Checkout button']
    },
    
    // Vendor Pages
    { 
      name: 'Vendor Dashboard', 
      category: 'Vendor', 
      path: '/vendor/dashboard', 
      description: 'Vendor main dashboard with analytics',
      requiresAuth: true, 
      userType: 'vendor',
      features: ['Sales overview', 'Recent orders', 'Analytics charts', 'Quick actions', 'Notifications']
    },
    { 
      name: 'Vendor Menu', 
      category: 'Vendor', 
      path: '/vendor/menu', 
      description: 'Menu management with image upload',
      requiresAuth: true, 
      userType: 'vendor',
      features: ['Food list', 'Add food modal', 'Image upload', 'Category filters', 'Edit/Delete items']
    },
    { 
      name: 'Vendor Restaurant', 
      category: 'Vendor', 
      path: '/vendor/restaurant', 
      description: 'Restaurant profile management',
      requiresAuth: true, 
      userType: 'vendor',
      features: ['Restaurant info', 'Logo upload', 'Banner upload', 'Gallery management', 'Operating hours']
    },
    { 
      name: 'Vendor Orders', 
      category: 'Vendor', 
      path: '/vendor/orders', 
      description: 'Order management and processing',
      requiresAuth: true, 
      userType: 'vendor',
      features: ['Order list', 'Status updates', 'Order details', 'Print receipts', 'Filter orders']
    },
    { 
      name: 'Vendor Analytics', 
      category: 'Vendor', 
      path: '/vendor/analytics', 
      description: 'Sales analytics and reports',
      requiresAuth: true, 
      userType: 'vendor',
      features: ['Sales charts', 'Period filters', 'Revenue metrics', 'Popular items', 'Export data']
    },
    { 
      name: 'Vendor Reviews', 
      category: 'Vendor', 
      path: '/vendor/reviews', 
      description: 'Customer reviews and ratings',
      requiresAuth: true, 
      userType: 'vendor',
      features: ['Review list', 'Rating filters', 'Response system', 'Review analytics', 'Export reviews']
    },
    { 
      name: 'Vendor Customers', 
      category: 'Vendor', 
      path: '/vendor/customers', 
      description: 'Customer management',
      requiresAuth: true, 
      userType: 'vendor',
      features: ['Customer list', 'Order history', 'Customer insights', 'Contact info', 'Loyalty tracking']
    },
    { 
      name: 'Vendor Notifications', 
      category: 'Vendor', 
      path: '/vendor/notifications', 
      description: 'Notification center',
      requiresAuth: true, 
      userType: 'vendor',
      features: ['Notification list', 'Mark as read', 'Filter by type', 'Notification settings', 'Real-time updates']
    },
    { 
      name: 'Vendor Settings', 
      category: 'Vendor', 
      path: '/vendor/settings', 
      description: 'Account and business settings',
      requiresAuth: true, 
      userType: 'vendor',
      features: ['Account info', 'Business settings', 'Notification preferences', 'Payment settings', 'Security']
    },
    
    // Admin Pages
    { 
      name: 'Admin Dashboard', 
      category: 'Admin', 
      path: '/admin/dashboard', 
      description: 'System administration dashboard',
      requiresAuth: true, 
      userType: 'admin',
      features: ['System overview', 'User statistics', 'Revenue metrics', 'Recent activity', 'System health']
    },
    { 
      name: 'Admin Users', 
      category: 'Admin', 
      path: '/admin/users', 
      description: 'User management',
      requiresAuth: true, 
      userType: 'admin',
      features: ['User list', 'User details', 'Role management', 'Account status', 'User actions']
    },
    { 
      name: 'Admin Restaurants', 
      category: 'Admin', 
      path: '/admin/restaurants', 
      description: 'Restaurant management',
      requiresAuth: true, 
      userType: 'admin',
      features: ['Restaurant list', 'Approval system', 'Restaurant details', 'Performance metrics', 'Actions']
    },
    { 
      name: 'Admin Orders', 
      category: 'Admin', 
      path: '/admin/orders', 
      description: 'Order monitoring and management',
      requiresAuth: true, 
      userType: 'admin',
      features: ['Order overview', 'Order details', 'Dispute resolution', 'Refund management', 'Analytics']
    },
    
    // Delivery Pages
    { 
      name: 'Delivery Dashboard', 
      category: 'Delivery', 
      path: '/delivery/dashboard', 
      description: 'Delivery partner dashboard',
      requiresAuth: true, 
      userType: 'delivery',
      features: ['Available orders', 'Current deliveries', 'Earnings', 'Performance metrics', 'Map integration']
    },
    { 
      name: 'Delivery Orders', 
      category: 'Delivery', 
      path: '/delivery/orders', 
      description: 'Delivery order management',
      requiresAuth: true, 
      userType: 'delivery',
      features: ['Order list', 'Accept/Decline', 'Navigation', 'Status updates', 'Customer contact']
    },
    
    // Testing Pages
    { 
      name: 'Debug Dashboard', 
      category: 'Testing', 
      path: '/debug', 
      description: 'System debugging and testing',
      features: ['API tests', 'Page tests', 'Upload tests', 'System status', 'Performance metrics']
    },
    { 
      name: 'System Test', 
      category: 'Testing', 
      path: '/test-system', 
      description: 'Comprehensive system testing',
      features: ['Automated tests', 'Manual tests', 'Test reports', 'Performance tests', 'Load tests']
    }
  ];

  const categories = ['all', ...Array.from(new Set([...apiTests.map(t => t.category), ...pageTests.map(t => t.category)]))];

  const runAPITest = async (test: APITest): Promise<TestResult> => {
    const startTime = Date.now();
    try {
      const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000';
      
      let response;
      if (test.method === 'POST' && test.endpoint.includes('upload')) {
        // Skip upload tests that require files
        return {
          name: test.name,
          category: test.category,
          status: 'skipped',
          message: 'Requires file upload',
          duration: Date.now() - startTime
        };
      } else {
        const options: RequestInit = {
          method: test.method,
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json'
          }
        };

        if (test.testData && (test.method === 'POST' || test.method === 'PUT')) {
          options.body = JSON.stringify(test.testData);
        }

        response = await fetch(`${baseUrl}${test.endpoint}`, options);
      }

      const duration = Date.now() - startTime;
      let data;
      try {
        data = await response.json();
      } catch (e) {
        data = await response.text();
      }
      
      const isSuccess = test.expectedStatus ? response.status === test.expectedStatus : response.ok;
      
      return {
        name: test.name,
        category: test.category,
        status: isSuccess ? 'success' : 'error',
        message: isSuccess ? `${response.status} OK` : `${response.status} ${response.statusText}`,
        data: data,
        duration,
        details: test.description
      };
    } catch (error) {
      return {
        name: test.name,
        category: test.category,
        status: 'error',
        message: error instanceof Error ? error.message : 'Network error',
        duration: Date.now() - startTime,
        details: test.description
      };
    }
  };

  const runAllAPITests = async () => {
    setIsRunning(true);
    setResults([]);
    setTestProgress(0);

    const filteredTests = selectedCategory === 'all' 
      ? apiTests 
      : apiTests.filter(test => test.category === selectedCategory);

    for (let i = 0; i < filteredTests.length; i++) {
      const test = filteredTests[i];
      
      setResults(prev => [...prev, { 
        name: test.name, 
        category: test.category, 
        status: 'loading',
        details: test.description
      }]);
      
      const result = await runAPITest(test);
      
      setResults(prev => 
        prev.map(r => r.name === test.name ? result : r)
      );

      setTestProgress(((i + 1) / filteredTests.length) * 100);
      
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 200));
    }

    setIsRunning(false);
  };

  const testPageLoad = async (page: PageTest): Promise<TestResult> => {
    const startTime = Date.now();
    try {
      // Test if page loads without errors
      const response = await fetch(`http://localhost:3000${page.path}`, {
        method: 'GET',
        credentials: 'include'
      });

      const duration = Date.now() - startTime;
      
      return {
        name: page.name,
        category: page.category,
        status: response.ok ? 'success' : 'error',
        message: response.ok ? 'Page loads successfully' : `${response.status} ${response.statusText}`,
        duration,
        details: `Features: ${page.features.join(', ')}`
      };
    } catch (error) {
      return {
        name: page.name,
        category: page.category,
        status: 'error',
        message: error instanceof Error ? error.message : 'Page load failed',
        duration: Date.now() - startTime,
        details: page.description
      };
    }
  };

  const runAllPageTests = async () => {
    setIsRunning(true);
    setResults([]);
    setTestProgress(0);

    const filteredTests = selectedCategory === 'all' 
      ? pageTests 
      : pageTests.filter(test => test.category === selectedCategory);

    for (let i = 0; i < filteredTests.length; i++) {
      const test = filteredTests[i];
      
      setResults(prev => [...prev, { 
        name: test.name, 
        category: test.category, 
        status: 'loading',
        details: test.description
      }]);
      
      const result = await testPageLoad(test);
      
      setResults(prev => 
        prev.map(r => r.name === test.name ? result : r)
      );

      setTestProgress(((i + 1) / filteredTests.length) * 100);
      
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 300));
    }

    setIsRunning(false);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'error':
        return <XCircle className="w-5 h-5 text-red-500" />;
      case 'loading':
        return <Clock className="w-5 h-5 text-blue-500 animate-spin" />;
      case 'pending':
        return <AlertCircle className="w-5 h-5 text-yellow-500" />;
      case 'skipped':
        return <Pause className="w-5 h-5 text-gray-500" />;
      default:
        return <Clock className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'error':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'loading':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'pending':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'skipped':
        return 'text-gray-600 bg-gray-50 border-gray-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'System': return <Database className="w-4 h-4" />;
      case 'Auth': return <Shield className="w-4 h-4" />;
      case 'Restaurants': return <Store className="w-4 h-4" />;
      case 'Foods': return <ShoppingCart className="w-4 h-4" />;
      case 'Vendor': return <User className="w-4 h-4" />;
      case 'Upload': return <Upload className="w-4 h-4" />;
      case 'Orders': return <FileText className="w-4 h-4" />;
      case 'Users': return <User className="w-4 h-4" />;
      case 'Admin': return <Shield className="w-4 h-4" />;
      case 'Delivery': return <Truck className="w-4 h-4" />;
      case 'Payment': return <CreditCard className="w-4 h-4" />;
      case 'Notifications': return <Bell className="w-4 h-4" />;
      case 'Public': return <Globe className="w-4 h-4" />;
      case 'Customer': return <User className="w-4 h-4" />;
      case 'Testing': return <Settings className="w-4 h-4" />;
      default: return <Server className="w-4 h-4" />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">🧪 Comprehensive System Testing</h1>
          <p className="text-gray-600 mb-4">Complete testing of all APIs, pages, and features in the food ordering system</p>
          
          {/* Progress Bar */}
          {isRunning && (
            <div className="mb-4">
              <div className="flex justify-between text-sm text-gray-600 mb-1">
                <span>Testing Progress</span>
                <span>{Math.round(testProgress)}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-orange-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${testProgress}%` }}
                ></div>
              </div>
            </div>
          )}
        </div>

        {/* Tab Navigation */}
        <div className="bg-white rounded-lg shadow-sm border mb-6">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              {[
                { id: 'apis', label: 'API Tests', icon: Server, count: apiTests.length },
                { id: 'pages', label: 'Page Tests', icon: Globe, count: pageTests.length },
                { id: 'features', label: 'Feature Tests', icon: Settings, count: 0 }
              ].map(tab => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm ${
                      activeTab === tab.id
                        ? 'border-orange-500 text-orange-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <Icon className="w-5 h-5 mr-2" />
                    {tab.label}
                    <span className="ml-2 bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs">
                      {tab.count}
                    </span>
                  </button>
                );
              })}
            </nav>
          </div>

          <div className="p-6">
            {/* Category Filter */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-4">
                <label className="text-sm font-medium text-gray-700">Filter by Category:</label>
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm"
                >
                  {categories.map(category => (
                    <option key={category} value={category}>
                      {category === 'all' ? 'All Categories' : category}
                    </option>
                  ))}
                </select>
              </div>

              <div className="flex space-x-3">
                {activeTab === 'apis' && (
                  <button
                    onClick={runAllAPITests}
                    disabled={isRunning}
                    className="bg-orange-500 text-white px-6 py-2 rounded-md hover:bg-orange-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                  >
                    {isRunning ? <Clock className="w-4 h-4 mr-2 animate-spin" /> : <Play className="w-4 h-4 mr-2" />}
                    {isRunning ? 'Testing APIs...' : 'Test All APIs'}
                  </button>
                )}
                
                {activeTab === 'pages' && (
                  <button
                    onClick={runAllPageTests}
                    disabled={isRunning}
                    className="bg-blue-500 text-white px-6 py-2 rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                  >
                    {isRunning ? <Clock className="w-4 h-4 mr-2 animate-spin" /> : <Play className="w-4 h-4 mr-2" />}
                    {isRunning ? 'Testing Pages...' : 'Test All Pages'}
                  </button>
                )}

                <button
                  onClick={() => setResults([])}
                  className="bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600 flex items-center"
                >
                  <RotateCcw className="w-4 h-4 mr-2" />
                  Clear Results
                </button>
              </div>
            </div>

            {/* Test Results */}
            {activeTab === 'apis' && (
              <div>
                <h2 className="text-xl font-semibold mb-4">API Test Results</h2>
                
                {results.length === 0 ? (
                  <div className="text-center py-12 bg-gray-50 rounded-lg">
                    <Server className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">Click "Test All APIs" to start testing</p>
                    <p className="text-sm text-gray-400 mt-2">
                      {selectedCategory === 'all' 
                        ? `${apiTests.length} APIs available for testing`
                        : `${apiTests.filter(t => t.category === selectedCategory).length} APIs in ${selectedCategory} category`
                      }
                    </p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {results.map((result, index) => (
                      <div
                        key={index}
                        className={`p-4 rounded-lg border ${getStatusColor(result.status)}`}
                      >
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center space-x-3">
                            {getStatusIcon(result.status)}
                            {getCategoryIcon(result.category)}
                            <div>
                              <span className="font-medium">{result.name}</span>
                              <span className="ml-2 text-xs bg-white bg-opacity-50 px-2 py-1 rounded">
                                {result.category}
                              </span>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2 text-sm">
                            {result.duration && (
                              <span className="text-gray-500">{result.duration}ms</span>
                            )}
                            <span>{result.message}</span>
                          </div>
                        </div>
                        
                        {result.details && (
                          <p className="text-sm text-gray-600 mb-2">{result.details}</p>
                        )}
                        
                        {result.data && (
                          <details className="mt-3">
                            <summary className="cursor-pointer text-sm font-medium text-gray-700 hover:text-gray-900">
                              View Response Data
                            </summary>
                            <div className="mt-2 p-3 bg-white bg-opacity-50 rounded text-sm">
                              <pre className="overflow-x-auto whitespace-pre-wrap">
                                {typeof result.data === 'string' 
                                  ? result.data 
                                  : JSON.stringify(result.data, null, 2)
                                }
                              </pre>
                            </div>
                          </details>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {activeTab === 'pages' && (
              <div>
                <h2 className="text-xl font-semibold mb-4">Page Test Results</h2>
                
                {results.length === 0 ? (
                  <div className="text-center py-12 bg-gray-50 rounded-lg">
                    <Globe className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">Click "Test All Pages" to start testing</p>
                    <p className="text-sm text-gray-400 mt-2">
                      {selectedCategory === 'all' 
                        ? `${pageTests.length} pages available for testing`
                        : `${pageTests.filter(t => t.category === selectedCategory).length} pages in ${selectedCategory} category`
                      }
                    </p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {results.map((result, index) => (
                      <div
                        key={index}
                        className={`p-4 rounded-lg border ${getStatusColor(result.status)}`}
                      >
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center space-x-3">
                            {getStatusIcon(result.status)}
                            {getCategoryIcon(result.category)}
                            <div>
                              <span className="font-medium">{result.name}</span>
                              <span className="ml-2 text-xs bg-white bg-opacity-50 px-2 py-1 rounded">
                                {result.category}
                              </span>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            {result.duration && (
                              <span className="text-sm text-gray-500">{result.duration}ms</span>
                            )}
                            <span className="text-sm">{result.message}</span>
                            <Link
                              href={pageTests.find(p => p.name === result.name)?.path || '/'}
                              target="_blank"
                              className="text-orange-500 hover:text-orange-600"
                            >
                              <ExternalLink className="w-4 h-4" />
                            </Link>
                          </div>
                        </div>
                        
                        {result.details && (
                          <p className="text-sm text-gray-600">{result.details}</p>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {activeTab === 'features' && (
              <div>
                <h2 className="text-xl font-semibold mb-4">Feature Tests</h2>
                <div className="text-center py-12 bg-gray-50 rounded-lg">
                  <Settings className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">Feature testing coming soon...</p>
                  <p className="text-sm text-gray-400 mt-2">
                    This will include upload functionality, form submissions, and interactive features
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Summary Statistics */}
        {results.length > 0 && (
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <h2 className="text-xl font-semibold mb-4">Test Summary</h2>
            
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">
                  {results.filter(r => r.status === 'success').length}
                </div>
                <div className="text-sm text-green-600">Passed</div>
              </div>
              
              <div className="text-center p-4 bg-red-50 rounded-lg">
                <div className="text-2xl font-bold text-red-600">
                  {results.filter(r => r.status === 'error').length}
                </div>
                <div className="text-sm text-red-600">Failed</div>
              </div>
              
              <div className="text-center p-4 bg-yellow-50 rounded-lg">
                <div className="text-2xl font-bold text-yellow-600">
                  {results.filter(r => r.status === 'pending').length}
                </div>
                <div className="text-sm text-yellow-600">Pending</div>
              </div>
              
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-2xl font-bold text-gray-600">
                  {results.filter(r => r.status === 'skipped').length}
                </div>
                <div className="text-sm text-gray-600">Skipped</div>
              </div>
              
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">
                  {results.length}
                </div>
                <div className="text-sm text-blue-600">Total</div>
              </div>
            </div>
            
            {results.length > 0 && (
              <div className="mt-4 text-center">
                <div className="text-sm text-gray-600">
                  Success Rate: {Math.round((results.filter(r => r.status === 'success').length / results.length) * 100)}%
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  Average Response Time: {Math.round(results.reduce((acc, r) => acc + (r.duration || 0), 0) / results.length)}ms
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default ComprehensiveTestPage;
