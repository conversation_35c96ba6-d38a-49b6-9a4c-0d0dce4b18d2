'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useQuery } from '@tanstack/react-query';
import { 
  Activity,
  Server,
  Database,
  Wifi,
  AlertTriangle,
  CheckCircle,
  Clock,
  Users,
  Package,
  TrendingUp
} from 'lucide-react';

interface SystemMetric {
  name: string;
  value: number;
  unit: string;
  status: 'healthy' | 'warning' | 'critical';
  icon: React.ComponentType<any>;
  description: string;
}

const SystemHealthDashboard = () => {
  const [realTimeMetrics, setRealTimeMetrics] = useState<SystemMetric[]>([]);

  // Mock system health data (in real app, this would come from monitoring APIs)
  const mockSystemHealth = {
    apiStatus: 'healthy',
    databaseStatus: 'healthy',
    serverLoad: 45,
    responseTime: 120,
    activeUsers: 1247,
    activeOrders: 89,
    errorRate: 0.02,
    uptime: 99.9
  };

  const systemMetrics: SystemMetric[] = [
    {
      name: 'API Status',
      value: mockSystemHealth.apiStatus === 'healthy' ? 100 : 0,
      unit: '%',
      status: mockSystemHealth.apiStatus as 'healthy',
      icon: Server,
      description: 'API endpoints availability'
    },
    {
      name: 'Database',
      value: mockSystemHealth.databaseStatus === 'healthy' ? 100 : 0,
      unit: '%',
      status: mockSystemHealth.databaseStatus as 'healthy',
      icon: Database,
      description: 'Database connection and performance'
    },
    {
      name: 'Server Load',
      value: mockSystemHealth.serverLoad,
      unit: '%',
      status: mockSystemHealth.serverLoad > 80 ? 'critical' : mockSystemHealth.serverLoad > 60 ? 'warning' : 'healthy',
      icon: Activity,
      description: 'Current server CPU usage'
    },
    {
      name: 'Response Time',
      value: mockSystemHealth.responseTime,
      unit: 'ms',
      status: mockSystemHealth.responseTime > 500 ? 'critical' : mockSystemHealth.responseTime > 200 ? 'warning' : 'healthy',
      icon: Clock,
      description: 'Average API response time'
    },
    {
      name: 'Active Users',
      value: mockSystemHealth.activeUsers,
      unit: '',
      status: 'healthy',
      icon: Users,
      description: 'Currently active users'
    },
    {
      name: 'Active Orders',
      value: mockSystemHealth.activeOrders,
      unit: '',
      status: 'healthy',
      icon: Package,
      description: 'Orders being processed'
    },
    {
      name: 'Error Rate',
      value: mockSystemHealth.errorRate,
      unit: '%',
      status: mockSystemHealth.errorRate > 1 ? 'critical' : mockSystemHealth.errorRate > 0.5 ? 'warning' : 'healthy',
      icon: AlertTriangle,
      description: 'System error rate'
    },
    {
      name: 'Uptime',
      value: mockSystemHealth.uptime,
      unit: '%',
      status: mockSystemHealth.uptime < 99 ? 'warning' : 'healthy',
      icon: TrendingUp,
      description: 'System uptime percentage'
    }
  ];

  // Simulate real-time updates
  useEffect(() => {
    const interval = setInterval(() => {
      setRealTimeMetrics(prev => {
        return systemMetrics.map(metric => ({
          ...metric,
          value: metric.name === 'Server Load' 
            ? Math.max(20, Math.min(90, metric.value + (Math.random() - 0.5) * 10))
            : metric.name === 'Response Time'
            ? Math.max(50, Math.min(300, metric.value + (Math.random() - 0.5) * 20))
            : metric.name === 'Active Users'
            ? Math.max(1000, Math.min(2000, metric.value + Math.floor((Math.random() - 0.5) * 100)))
            : metric.name === 'Active Orders'
            ? Math.max(50, Math.min(150, metric.value + Math.floor((Math.random() - 0.5) * 20)))
            : metric.value
        }));
      });
    }, 5000); // Update every 5 seconds

    // Initialize with base metrics
    setRealTimeMetrics(systemMetrics);

    return () => clearInterval(interval);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'text-green-600 bg-green-100';
      case 'warning':
        return 'text-yellow-600 bg-yellow-100';
      case 'critical':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="w-4 h-4" />;
      case 'warning':
        return <AlertTriangle className="w-4 h-4" />;
      case 'critical':
        return <AlertTriangle className="w-4 h-4" />;
      default:
        return <Activity className="w-4 h-4" />;
    }
  };

  const overallStatus = realTimeMetrics.some(m => m.status === 'critical') 
    ? 'critical' 
    : realTimeMetrics.some(m => m.status === 'warning') 
    ? 'warning' 
    : 'healthy';

  return (
    <div className="bg-white rounded-2xl p-6 shadow-md">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${getStatusColor(overallStatus)}`}>
            <Activity className="w-5 h-5" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">System Health</h3>
            <div className="flex items-center gap-2">
              {getStatusIcon(overallStatus)}
              <span className={`text-sm font-medium ${
                overallStatus === 'healthy' ? 'text-green-600' :
                overallStatus === 'warning' ? 'text-yellow-600' : 'text-red-600'
              }`}>
                {overallStatus === 'healthy' ? 'All Systems Operational' :
                 overallStatus === 'warning' ? 'Some Issues Detected' : 'Critical Issues'}
              </span>
            </div>
          </div>
        </div>
        
        {/* Live Indicator */}
        <div className="flex items-center gap-2 text-sm text-gray-500">
          <motion.div
            className="w-2 h-2 bg-green-500 rounded-full"
            animate={{ opacity: [1, 0.5, 1] }}
            transition={{ duration: 2, repeat: Infinity }}
          />
          Live
        </div>
      </div>

      {/* Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {realTimeMetrics.map((metric, index) => (
          <motion.div
            key={metric.name}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
            className="p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow duration-200"
          >
            <div className="flex items-center justify-between mb-3">
              <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${getStatusColor(metric.status)}`}>
                <metric.icon className="w-4 h-4" />
              </div>
              <div className={`flex items-center gap-1 text-xs font-medium ${getStatusColor(metric.status)} px-2 py-1 rounded-full`}>
                {getStatusIcon(metric.status)}
                {metric.status.toUpperCase()}
              </div>
            </div>
            
            <div className="mb-2">
              <div className="flex items-baseline gap-1">
                <motion.span
                  key={metric.value}
                  initial={{ scale: 1.1 }}
                  animate={{ scale: 1 }}
                  className="text-xl font-bold text-gray-900"
                >
                  {typeof metric.value === 'number' && metric.value % 1 !== 0 
                    ? metric.value.toFixed(2) 
                    : metric.value.toLocaleString()}
                </motion.span>
                <span className="text-sm text-gray-500">{metric.unit}</span>
              </div>
              <h4 className="text-sm font-medium text-gray-700">{metric.name}</h4>
            </div>
            
            <p className="text-xs text-gray-500">{metric.description}</p>
          </motion.div>
        ))}
      </div>

      {/* Recent Events */}
      <div className="mt-6 pt-6 border-t border-gray-200">
        <h4 className="text-sm font-semibold text-gray-900 mb-3">Recent System Events</h4>
        <div className="space-y-2">
          <div className="flex items-center gap-3 text-sm">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span className="text-gray-600">System backup completed successfully</span>
            <span className="text-gray-400">2 minutes ago</span>
          </div>
          <div className="flex items-center gap-3 text-sm">
            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
            <span className="text-gray-600">Database optimization completed</span>
            <span className="text-gray-400">15 minutes ago</span>
          </div>
          <div className="flex items-center gap-3 text-sm">
            <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
            <span className="text-gray-600">High traffic detected - auto-scaling triggered</span>
            <span className="text-gray-400">1 hour ago</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SystemHealthDashboard;
