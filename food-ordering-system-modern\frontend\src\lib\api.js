import axios from 'axios';
import { toast } from 'react-hot-toast';

// Create axios instance
const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api',
  timeout: 10000,
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // Only try to refresh if we have a token and this is an auth-related 401
    if (error.response?.status === 401 && !originalRequest._retry && localStorage.getItem('token')) {
      originalRequest._retry = true;

      try {
        // Try to refresh token
        const response = await api.post('/auth/refresh');
        const { token } = response.data.data;

        localStorage.setItem('token', token);
        originalRequest.headers.Authorization = `Bearer ${token}`;

        // Update auth store with new token
        if (typeof window !== 'undefined' && window.useAuthStore) {
          window.useAuthStore.getState().setToken(token);
        }

        return api(originalRequest);
      } catch (refreshError) {
        // Refresh failed, clear auth data
        localStorage.removeItem('token');
        localStorage.removeItem('user');

        // Only redirect to login if not already on auth pages and this was a protected route
        if (!window.location.pathname.startsWith('/auth') && originalRequest.headers.Authorization) {
          window.location.href = '/auth/login';
        }

        return Promise.reject(refreshError);
      }
    }

    // Show error toast for non-401 errors
    if (error.response?.data?.message) {
      toast.error(error.response.data.message);
    } else {
      toast.error('Something went wrong. Please try again.');
    }

    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  register: (data) => api.post('/auth/register', data),
  login: (data) => api.post('/auth/login', data),
  logout: () => api.post('/auth/logout'),
  getProfile: () => api.get('/auth/me'),
  forgotPassword: (email) => api.post('/auth/forgot-password', { email }),
  resetPassword: (data) => api.put('/auth/reset-password', data),
  refreshToken: () => api.post('/auth/refresh'),
};

// User API
export const userAPI = {
  getProfile: () => api.get('/users/profile'),
  updateProfile: (data) => api.put('/users/profile', data),
  addAddress: (data) => api.post('/users/addresses', data),
  updateAddress: (id, data) => api.put(`/users/addresses/${id}`, data),
  deleteAddress: (id) => api.delete(`/users/addresses/${id}`),
  getFavorites: () => api.get('/users/favorites'),
  addToFavorites: (restaurantId) => api.post(`/users/favorites/${restaurantId}`),
  removeFromFavorites: (restaurantId) => api.delete(`/users/favorites/${restaurantId}`),
  getOrders: (params) => api.get('/users/orders', { params }),
  getStats: () => api.get('/users/stats'),
};

// Restaurant API
export const restaurantAPI = {
  getAll: (params) => api.get('/restaurants', { params }),
  getById: (id) => api.get(`/restaurants/${id}`),
  getNearby: (lat, lng, params) => api.get(`/restaurants/nearby/${lat}/${lng}`, { params }),
  create: (data) => api.post('/restaurants', data),
  update: (id, data) => api.put(`/restaurants/${id}`, data),
  delete: (id) => api.delete(`/restaurants/${id}`),
  toggleStatus: (id) => api.patch(`/restaurants/${id}/toggle-status`),
};

// Food API
export const foodAPI = {
  getAll: (params) => api.get('/foods', { params }),
  getById: (id) => api.get(`/foods/${id}`),
  getByRestaurant: (restaurantId, params) => api.get(`/foods/restaurant/${restaurantId}`, { params }),
  create: (data) => api.post('/foods', data),
  update: (id, data) => api.put(`/foods/${id}`, data),
  delete: (id) => api.delete(`/foods/${id}`),
  toggleAvailability: (id) => api.patch(`/foods/${id}/toggle-availability`),
};

// Order API
export const orderAPI = {
  create: (data) => api.post('/orders', data),
  getAll: (params) => api.get('/orders', { params }),
  getById: (id) => api.get(`/orders/${id}`),
  getUserOrders: (params) => api.get('/users/orders', { params }),
  updateStatus: (id, data) => api.patch(`/orders/${id}/status`, data),
  cancel: (id, reason) => api.patch(`/orders/${id}/cancel`, { reason }),
};

// Payment API
export const paymentAPI = {
  process: (data) => api.post('/payments/process', data),
  getMethods: () => api.get('/payments/methods'),
  getHistory: (params) => api.get('/payments/history', { params }),
  refund: (data) => api.post('/payments/refund', data),
};

// Admin API
export const adminAPI = {
  getStats: (params) => api.get('/admin/stats', { params }),
  getUsers: (params) => api.get('/admin/users', { params }),
  getRestaurants: (params) => api.get('/admin/restaurants', { params }),
  getOrders: (params) => api.get('/admin/orders', { params }),
  updateRestaurantStatus: (id, status) => api.patch(`/admin/restaurants/${id}/status`, { status }),
  toggleUserStatus: (id) => api.patch(`/admin/users/${id}/toggle-status`),
  getAnalytics: (period) => api.get('/admin/analytics', { params: { period } }),
  getPayments: (params) => api.get('/admin/payments', { params }),
  getReviews: (params) => api.get('/admin/reviews', { params }),
  getReports: (type, params) => api.get(`/admin/reports/${type}`, { params }),
  getSettings: () => api.get('/admin/settings'),
  updateSettings: (data) => api.put('/admin/settings', data),
};

// Vendor API
export const vendorAPI = {
  getDashboard: (params) => api.get('/vendors/dashboard', { params }),
  getAnalytics: (period) => api.get('/vendors/analytics', { params: { period } }),
  getOrders: (params) => api.get('/vendors/orders', { params }),
  getRecentOrders: (params) => api.get('/vendors/orders/recent', { params }),
  getFoods: (params) => api.get('/vendors/foods', { params }),
  getRestaurant: () => api.get('/vendors/restaurant'),
  createRestaurant: (data) => api.post('/vendors/restaurant', data),
  updateRestaurant: (data) => api.put('/vendors/restaurant', data),
  createFood: (data) => api.post('/vendors/foods', data),
  updateFood: (id, data) => api.put(`/vendors/foods/${id}`, data),
  deleteFood: (id) => api.delete(`/vendors/foods/${id}`),
  updateOrderStatus: (id, status) => api.patch(`/vendors/orders/${id}/status`, { status }),
  getCustomers: (params) => api.get('/vendors/customers', { params }),
  getReviews: (params) => api.get('/vendors/reviews', { params }),
  getNotifications: (params) => api.get('/vendors/notifications', { params }),
};

// Delivery API
export const deliveryAPI = {
  getDashboard: () => api.get('/delivery/dashboard'),
  getOrders: (params) => api.get('/delivery/orders', { params }),
  acceptOrder: (id) => api.patch(`/delivery/orders/${id}/accept`),
  pickupOrder: (id) => api.patch(`/delivery/orders/${id}/pickup`),
  deliverOrder: (id) => api.patch(`/delivery/orders/${id}/deliver`),
  getEarnings: (params) => api.get('/delivery/earnings', { params }),
  getHistory: (params) => api.get('/delivery/history', { params }),
  updateLocation: (data) => api.patch('/delivery/location', data),
  updateStatus: (status) => api.patch('/delivery/status', { status }),
};

// Offers API
export const offersAPI = {
  getOffers: (params) => api.get('/offers', { params }),
  getOfferById: (id) => api.get(`/offers/${id}`),
  applyOffer: (code, orderData) => api.post('/offers/apply', { code, ...orderData }),
  validateOffer: (code) => api.post('/offers/validate', { code }),
};

// Notifications API
export const notificationsAPI = {
  getNotifications: (params) => api.get('/notifications', { params }),
  getUnreadCount: () => api.get('/notifications/unread-count'),
  markAsRead: (id) => api.patch(`/notifications/${id}/read`),
  markAllAsRead: () => api.patch('/notifications/mark-all-read'),
  deleteNotification: (id) => api.delete(`/notifications/${id}`),
};

export default api;
