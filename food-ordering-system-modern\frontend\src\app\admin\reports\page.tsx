'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { useQuery } from '@tanstack/react-query';
import { 
  FileText, 
  Download,
  Calendar,
  Filter,
  BarChart3,
  PieChart,
  TrendingUp,
  Users,
  Store,
  Package,
  DollarSign
} from 'lucide-react';
import AdminSidebar from '@/components/AdminSidebar';
import { adminAPI } from '@/lib/api';

const AdminReports = () => {
  const [selectedReport, setSelectedReport] = useState('revenue');
  const [selectedPeriod, setSelectedPeriod] = useState('30d');
  const [dateRange, setDateRange] = useState({
    start: '',
    end: ''
  });

  const reportTypes = [
    {
      id: 'revenue',
      name: 'Revenue Report',
      description: 'Detailed revenue analysis and trends',
      icon: DollarSign,
      color: 'text-green-600',
      bgColor: 'bg-green-100'
    },
    {
      id: 'orders',
      name: 'Orders Report',
      description: 'Order volume and performance metrics',
      icon: Package,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    },
    {
      id: 'restaurants',
      name: 'Restaurant Report',
      description: 'Restaurant performance and analytics',
      icon: Store,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100'
    },
    {
      id: 'users',
      name: 'User Report',
      description: 'User growth and engagement metrics',
      icon: Users,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100'
    },
    {
      id: 'performance',
      name: 'Performance Report',
      description: 'Overall platform performance analysis',
      icon: TrendingUp,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-100'
    }
  ];

  const periods = [
    { value: '7d', label: 'Last 7 Days' },
    { value: '30d', label: 'Last 30 Days' },
    { value: '90d', label: 'Last 90 Days' },
    { value: '1y', label: 'Last Year' },
    { value: 'custom', label: 'Custom Range' },
  ];

  // Mock report data
  const mockReportData = {
    revenue: {
      summary: {
        totalRevenue: 125000,
        growth: 12.5,
        avgOrderValue: 450,
        commission: 12500
      },
      chartData: [
        { date: '2024-01-01', value: 5000 },
        { date: '2024-01-02', value: 7500 },
        { date: '2024-01-03', value: 6200 },
        { date: '2024-01-04', value: 8900 },
        { date: '2024-01-05', value: 12000 },
      ]
    },
    orders: {
      summary: {
        totalOrders: 2450,
        growth: 8.3,
        avgDeliveryTime: 35,
        completionRate: 94.5
      },
      chartData: [
        { date: '2024-01-01', value: 120 },
        { date: '2024-01-02', value: 150 },
        { date: '2024-01-03', value: 135 },
        { date: '2024-01-04', value: 180 },
        { date: '2024-01-05', value: 200 },
      ]
    },
    restaurants: {
      summary: {
        totalRestaurants: 125,
        activeRestaurants: 118,
        avgRating: 4.2,
        newRegistrations: 15
      },
      topPerformers: [
        { name: 'Spice Garden', revenue: 15000, orders: 245 },
        { name: 'Pizza Palace', revenue: 12500, orders: 198 },
        { name: 'Burger Hub', revenue: 11200, orders: 187 },
      ]
    },
    users: {
      summary: {
        totalUsers: 1850,
        activeUsers: 1420,
        newUsers: 320,
        retentionRate: 76.8
      },
      demographics: [
        { age: '18-25', count: 450 },
        { age: '26-35', count: 680 },
        { age: '36-45', count: 520 },
        { age: '46+', count: 200 },
      ]
    }
  };

  const currentReportData = mockReportData[selectedReport];

  const handleGenerateReport = () => {
    console.log('Generating report:', selectedReport, selectedPeriod);
    // Implement report generation
  };

  const handleExportReport = (format) => {
    console.log('Exporting report as:', format);
    // Implement export functionality
  };

  const renderReportContent = () => {
    switch (selectedReport) {
      case 'revenue':
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h4 className="text-sm font-medium text-gray-500 mb-2">Total Revenue</h4>
                <p className="text-2xl font-bold text-gray-900">₹{currentReportData.summary.totalRevenue.toLocaleString()}</p>
                <p className="text-sm text-green-600">+{currentReportData.summary.growth}%</p>
              </div>
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h4 className="text-sm font-medium text-gray-500 mb-2">Avg Order Value</h4>
                <p className="text-2xl font-bold text-gray-900">₹{currentReportData.summary.avgOrderValue}</p>
              </div>
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h4 className="text-sm font-medium text-gray-500 mb-2">Commission Earned</h4>
                <p className="text-2xl font-bold text-gray-900">₹{currentReportData.summary.commission.toLocaleString()}</p>
              </div>
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h4 className="text-sm font-medium text-gray-500 mb-2">Growth Rate</h4>
                <p className="text-2xl font-bold text-gray-900">{currentReportData.summary.growth}%</p>
              </div>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h4 className="text-lg font-semibold text-gray-900 mb-4">Revenue Trend</h4>
              <div className="h-64 flex items-center justify-center text-gray-500">
                <div className="text-center">
                  <BarChart3 className="w-12 h-12 mx-auto mb-2 text-gray-300" />
                  <p>Revenue chart will be implemented with Chart.js</p>
                </div>
              </div>
            </div>
          </div>
        );

      case 'orders':
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h4 className="text-sm font-medium text-gray-500 mb-2">Total Orders</h4>
                <p className="text-2xl font-bold text-gray-900">{currentReportData.summary.totalOrders.toLocaleString()}</p>
                <p className="text-sm text-green-600">+{currentReportData.summary.growth}%</p>
              </div>
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h4 className="text-sm font-medium text-gray-500 mb-2">Avg Delivery Time</h4>
                <p className="text-2xl font-bold text-gray-900">{currentReportData.summary.avgDeliveryTime} min</p>
              </div>
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h4 className="text-sm font-medium text-gray-500 mb-2">Completion Rate</h4>
                <p className="text-2xl font-bold text-gray-900">{currentReportData.summary.completionRate}%</p>
              </div>
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h4 className="text-sm font-medium text-gray-500 mb-2">Growth Rate</h4>
                <p className="text-2xl font-bold text-gray-900">{currentReportData.summary.growth}%</p>
              </div>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h4 className="text-lg font-semibold text-gray-900 mb-4">Order Volume Trend</h4>
              <div className="h-64 flex items-center justify-center text-gray-500">
                <div className="text-center">
                  <BarChart3 className="w-12 h-12 mx-auto mb-2 text-gray-300" />
                  <p>Order volume chart will be implemented with Chart.js</p>
                </div>
              </div>
            </div>
          </div>
        );

      case 'restaurants':
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h4 className="text-sm font-medium text-gray-500 mb-2">Total Restaurants</h4>
                <p className="text-2xl font-bold text-gray-900">{currentReportData.summary.totalRestaurants}</p>
              </div>
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h4 className="text-sm font-medium text-gray-500 mb-2">Active Restaurants</h4>
                <p className="text-2xl font-bold text-gray-900">{currentReportData.summary.activeRestaurants}</p>
              </div>
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h4 className="text-sm font-medium text-gray-500 mb-2">Average Rating</h4>
                <p className="text-2xl font-bold text-gray-900">{currentReportData.summary.avgRating}</p>
              </div>
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h4 className="text-sm font-medium text-gray-500 mb-2">New Registrations</h4>
                <p className="text-2xl font-bold text-gray-900">{currentReportData.summary.newRegistrations}</p>
              </div>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h4 className="text-lg font-semibold text-gray-900 mb-4">Top Performing Restaurants</h4>
              <div className="space-y-4">
                {currentReportData.topPerformers.map((restaurant, index) => (
                  <div key={restaurant.name} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <span className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center text-orange-600 font-semibold">
                        {index + 1}
                      </span>
                      <span className="font-medium text-gray-900">{restaurant.name}</span>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-gray-900">₹{restaurant.revenue.toLocaleString()}</p>
                      <p className="text-sm text-gray-500">{restaurant.orders} orders</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        );

      case 'users':
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h4 className="text-sm font-medium text-gray-500 mb-2">Total Users</h4>
                <p className="text-2xl font-bold text-gray-900">{currentReportData.summary.totalUsers.toLocaleString()}</p>
              </div>
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h4 className="text-sm font-medium text-gray-500 mb-2">Active Users</h4>
                <p className="text-2xl font-bold text-gray-900">{currentReportData.summary.activeUsers.toLocaleString()}</p>
              </div>
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h4 className="text-sm font-medium text-gray-500 mb-2">New Users</h4>
                <p className="text-2xl font-bold text-gray-900">{currentReportData.summary.newUsers}</p>
              </div>
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h4 className="text-sm font-medium text-gray-500 mb-2">Retention Rate</h4>
                <p className="text-2xl font-bold text-gray-900">{currentReportData.summary.retentionRate}%</p>
              </div>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h4 className="text-lg font-semibold text-gray-900 mb-4">User Demographics</h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {currentReportData.demographics.map((demo) => (
                  <div key={demo.age} className="text-center p-4 bg-gray-50 rounded-lg">
                    <p className="text-lg font-semibold text-gray-900">{demo.count}</p>
                    <p className="text-sm text-gray-500">{demo.age} years</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        );

      default:
        return (
          <div className="bg-white p-6 rounded-lg shadow-md">
            <div className="text-center py-12">
              <FileText className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Performance Report</h3>
              <p className="text-gray-600">Comprehensive platform performance analysis will be displayed here.</p>
            </div>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex">
      <AdminSidebar />
      
      <div className="flex-1 p-8">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <FileText className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Reports & Analytics</h1>
                <p className="text-gray-600">Generate comprehensive business reports</p>
              </div>
            </div>

            <div className="flex items-center gap-4">
              <button
                onClick={handleGenerateReport}
                className="flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200"
              >
                <BarChart3 className="w-4 h-4" />
                Generate Report
              </button>
              <div className="flex items-center gap-2">
                <button
                  onClick={() => handleExportReport('pdf')}
                  className="flex items-center gap-2 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors duration-200"
                >
                  <Download className="w-4 h-4" />
                  Export PDF
                </button>
                <button
                  onClick={() => handleExportReport('excel')}
                  className="flex items-center gap-2 px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors duration-200"
                >
                  <Download className="w-4 h-4" />
                  Export Excel
                </button>
              </div>
            </div>
          </div>

          {/* Report Types */}
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4 mb-8">
            {reportTypes.map((report) => (
              <motion.button
                key={report.id}
                onClick={() => setSelectedReport(report.id)}
                className={`p-4 rounded-lg border-2 transition-all duration-200 ${
                  selectedReport === report.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 bg-white hover:border-gray-300'
                }`}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <div className={`w-12 h-12 ${report.bgColor} rounded-lg flex items-center justify-center mx-auto mb-3`}>
                  <report.icon className={`w-6 h-6 ${report.color}`} />
                </div>
                <h3 className="font-semibold text-gray-900 text-sm mb-1">{report.name}</h3>
                <p className="text-xs text-gray-600">{report.description}</p>
              </motion.button>
            ))}
          </div>

          {/* Filters */}
          <div className="bg-white rounded-2xl p-6 shadow-md mb-6">
            <div className="flex flex-col md:flex-row gap-4">
              {/* Period Selector */}
              <div className="flex items-center gap-2">
                <Calendar className="w-5 h-5 text-gray-400" />
                <select
                  value={selectedPeriod}
                  onChange={(e) => setSelectedPeriod(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {periods.map((period) => (
                    <option key={period.value} value={period.value}>
                      {period.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Custom Date Range */}
              {selectedPeriod === 'custom' && (
                <div className="flex items-center gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                    <input
                      type="date"
                      value={dateRange.start}
                      onChange={(e) => setDateRange({ ...dateRange, start: e.target.value })}
                      className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                    <input
                      type="date"
                      value={dateRange.end}
                      onChange={(e) => setDateRange({ ...dateRange, end: e.target.value })}
                      className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Report Content */}
          <motion.div
            key={selectedReport}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            {renderReportContent()}
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default AdminReports;
