'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Upload, X, Loader, Camera, Image as ImageIcon } from 'lucide-react';
import Image from 'next/image';
import { uploadService } from '@/lib/uploadService';
import { toast } from 'react-hot-toast';

interface RestaurantImageUploadProps {
  restaurantId: string;
  currentLogo?: string;
  currentBanner?: string;
  currentGallery?: string[];
  onLogoUpdate?: (url: string) => void;
  onBannerUpdate?: (url: string) => void;
  onGalleryUpdate?: (urls: string[]) => void;
}

const RestaurantImageUpload = ({
  restaurantId,
  currentLogo,
  currentBanner,
  currentGallery = [],
  onLogoUpdate,
  onBannerUpdate,
  onGalleryUpdate
}: RestaurantImageUploadProps) => {
  const [uploading, setUploading] = useState({
    logo: false,
    banner: false,
    gallery: false
  });

  const [logo, setLogo] = useState(currentLogo || '');
  const [banner, setBanner] = useState(currentBanner || '');
  const [gallery, setGallery] = useState(currentGallery);

  // Upload logo
  const handleLogoUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    setUploading(prev => ({ ...prev, logo: true }));

    try {
      uploadService.validateFile(file);
      
      const result = await uploadService.uploadRestaurantLogo(file, restaurantId);
      
      if (result.success) {
        setLogo(result.data.url);
        onLogoUpdate?.(result.data.url);
        toast.success('Logo uploaded successfully!');
      }
    } catch (error: any) {
      console.error('Logo upload error:', error);
      toast.error('Failed to upload logo: ' + (error.response?.data?.message || error.message));
    } finally {
      setUploading(prev => ({ ...prev, logo: false }));
    }
  };

  // Upload banner
  const handleBannerUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    setUploading(prev => ({ ...prev, banner: true }));

    try {
      uploadService.validateFile(file);
      
      const result = await uploadService.uploadRestaurantBanner(file, restaurantId);
      
      if (result.success) {
        setBanner(result.data.url);
        onBannerUpdate?.(result.data.url);
        toast.success('Banner uploaded successfully!');
      }
    } catch (error: any) {
      console.error('Banner upload error:', error);
      toast.error('Failed to upload banner: ' + (error.response?.data?.message || error.message));
    } finally {
      setUploading(prev => ({ ...prev, banner: false }));
    }
  };

  // Upload gallery images
  const handleGalleryUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    setUploading(prev => ({ ...prev, gallery: true }));

    try {
      // Validate files
      const validFiles: File[] = [];
      for (const file of Array.from(files)) {
        try {
          uploadService.validateFile(file);
          validFiles.push(file);
        } catch (error: any) {
          toast.error(`${file.name}: ${error.message}`);
        }
      }

      if (validFiles.length === 0) {
        setUploading(prev => ({ ...prev, gallery: false }));
        return;
      }

      const result = await uploadService.uploadRestaurantGallery(validFiles, restaurantId);
      
      if (result.success) {
        const newGallery = [...gallery, ...result.data];
        setGallery(newGallery);
        onGalleryUpdate?.(newGallery);
        toast.success(`${validFiles.length} image(s) added to gallery!`);
      }
    } catch (error: any) {
      console.error('Gallery upload error:', error);
      toast.error('Failed to upload gallery images: ' + (error.response?.data?.message || error.message));
    } finally {
      setUploading(prev => ({ ...prev, gallery: false }));
    }
  };

  // Remove gallery image
  const removeGalleryImage = async (index: number) => {
    const imageToRemove = gallery[index];
    const newGallery = gallery.filter((_, i) => i !== index);
    
    setGallery(newGallery);
    onGalleryUpdate?.(newGallery);

    // Attempt to delete from server
    try {
      await uploadService.deleteImage(imageToRemove);
    } catch (error) {
      console.error('Failed to delete image from server:', error);
    }
  };

  return (
    <div className="space-y-8">
      {/* Logo Upload */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Restaurant Logo</h3>
        <div className="flex items-center gap-6">
          <div className="relative">
            {logo ? (
              <div className="w-24 h-24 rounded-lg overflow-hidden border-2 border-gray-200">
                <Image
                  src={logo}
                  alt="Restaurant Logo"
                  width={96}
                  height={96}
                  className="w-full h-full object-cover"
                />
              </div>
            ) : (
              <div className="w-24 h-24 rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center">
                <Camera className="w-8 h-8 text-gray-400" />
              </div>
            )}
          </div>
          
          <div>
            <input
              type="file"
              accept="image/*"
              onChange={handleLogoUpload}
              className="hidden"
              id="logo-upload"
              disabled={uploading.logo}
            />
            <label
              htmlFor="logo-upload"
              className={`inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 cursor-pointer ${
                uploading.logo ? 'opacity-50 pointer-events-none' : ''
              }`}
            >
              {uploading.logo ? (
                <>
                  <Loader className="w-4 h-4 mr-2 animate-spin" />
                  Uploading...
                </>
              ) : (
                <>
                  <Upload className="w-4 h-4 mr-2" />
                  Upload Logo
                </>
              )}
            </label>
            <p className="text-xs text-gray-500 mt-1">Square image recommended (1:1 ratio)</p>
          </div>
        </div>
      </div>

      {/* Banner Upload */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Restaurant Banner</h3>
        <div className="space-y-4">
          {banner ? (
            <div className="relative w-full h-48 rounded-lg overflow-hidden border-2 border-gray-200">
              <Image
                src={banner}
                alt="Restaurant Banner"
                fill
                className="object-cover"
              />
            </div>
          ) : (
            <div className="w-full h-48 rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center">
              <div className="text-center">
                <ImageIcon className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                <p className="text-gray-500">No banner image</p>
              </div>
            </div>
          )}
          
          <div>
            <input
              type="file"
              accept="image/*"
              onChange={handleBannerUpload}
              className="hidden"
              id="banner-upload"
              disabled={uploading.banner}
            />
            <label
              htmlFor="banner-upload"
              className={`inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 cursor-pointer ${
                uploading.banner ? 'opacity-50 pointer-events-none' : ''
              }`}
            >
              {uploading.banner ? (
                <>
                  <Loader className="w-4 h-4 mr-2 animate-spin" />
                  Uploading...
                </>
              ) : (
                <>
                  <Upload className="w-4 h-4 mr-2" />
                  Upload Banner
                </>
              )}
            </label>
            <p className="text-xs text-gray-500 mt-1">Wide image recommended (16:9 ratio)</p>
          </div>
        </div>
      </div>

      {/* Gallery Upload */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Restaurant Gallery</h3>
        
        {/* Upload Area */}
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 mb-6">
          <input
            type="file"
            multiple
            accept="image/*"
            onChange={handleGalleryUpload}
            className="hidden"
            id="gallery-upload"
            disabled={uploading.gallery}
          />
          <label
            htmlFor="gallery-upload"
            className={`cursor-pointer flex flex-col items-center ${
              uploading.gallery ? 'opacity-50 pointer-events-none' : ''
            }`}
          >
            {uploading.gallery ? (
              <>
                <Loader className="w-8 h-8 text-orange-500 mb-2 animate-spin" />
                <span className="text-sm text-orange-600">Uploading images...</span>
              </>
            ) : (
              <>
                <Upload className="w-8 h-8 text-gray-400 mb-2" />
                <span className="text-sm text-gray-600">Click to upload gallery images</span>
                <span className="text-xs text-gray-500 mt-1">Max 5MB per image, JPEG/PNG/WebP</span>
              </>
            )}
          </label>
        </div>

        {/* Gallery Grid */}
        {gallery.length > 0 && (
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {gallery.map((image, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                className="relative group"
              >
                <div className="aspect-square rounded-lg overflow-hidden border-2 border-gray-200">
                  <Image
                    src={image}
                    alt={`Gallery image ${index + 1}`}
                    width={200}
                    height={200}
                    className="w-full h-full object-cover"
                  />
                </div>
                <button
                  onClick={() => removeGalleryImage(index)}
                  className="absolute top-2 right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <X className="w-4 h-4" />
                </button>
              </motion.div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default RestaurantImageUpload;
