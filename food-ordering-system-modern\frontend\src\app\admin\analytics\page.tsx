'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { useQuery } from '@tanstack/react-query';
import { 
  BarChart3, 
  TrendingUp, 
  DollarSign, 
  Users,
  Package,
  Store,
  Calendar,
  ArrowUpRight,
  ArrowDownRight,
  Download,
  Filter
} from 'lucide-react';
import AdminSidebar from '@/components/AdminSidebar';
import { adminAPI } from '@/lib/api';

const AdminAnalytics = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('30d');
  const [selectedMetric, setSelectedMetric] = useState('revenue');

  // Fetch analytics data
  const { data: analytics, isLoading } = useQuery({
    queryKey: ['admin-analytics', selectedPeriod],
    queryFn: () => adminAPI.getAnalytics(selectedPeriod),
  });

  const periods = [
    { value: '7d', label: '7 Days' },
    { value: '30d', label: '30 Days' },
    { value: '90d', label: '90 Days' },
    { value: '1y', label: '1 Year' },
  ];

  const metrics = [
    { value: 'revenue', label: 'Revenue', icon: DollarSign, color: 'text-green-600' },
    { value: 'orders', label: 'Orders', icon: Package, color: 'text-blue-600' },
    { value: 'users', label: 'Users', icon: Users, color: 'text-purple-600' },
    { value: 'restaurants', label: 'Restaurants', icon: Store, color: 'text-orange-600' },
  ];

  // Mock data for demonstration (replace with real API data)
  const mockStats = {
    totalRevenue: 125000,
    totalOrders: 2450,
    totalUsers: 1850,
    totalRestaurants: 125,
    periodRevenue: 45000,
    periodOrders: 890,
    periodUsers: 320,
    periodRestaurants: 15,
    revenueGrowth: 12.5,
    ordersGrowth: 8.3,
    usersGrowth: 15.2,
    restaurantsGrowth: 6.7
  };

  const stats = [
    {
      title: 'Total Revenue',
      value: mockStats.totalRevenue,
      periodValue: mockStats.periodRevenue,
      growth: mockStats.revenueGrowth,
      icon: DollarSign,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      format: 'currency'
    },
    {
      title: 'Total Orders',
      value: mockStats.totalOrders,
      periodValue: mockStats.periodOrders,
      growth: mockStats.ordersGrowth,
      icon: Package,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      format: 'number'
    },
    {
      title: 'Total Users',
      value: mockStats.totalUsers,
      periodValue: mockStats.periodUsers,
      growth: mockStats.usersGrowth,
      icon: Users,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
      format: 'number'
    },
    {
      title: 'Total Restaurants',
      value: mockStats.totalRestaurants,
      periodValue: mockStats.periodRestaurants,
      growth: mockStats.restaurantsGrowth,
      icon: Store,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
      format: 'number'
    },
  ];

  const topRestaurants = [
    { name: 'Spice Garden', revenue: 15000, orders: 245, rating: 4.8 },
    { name: 'Pizza Palace', revenue: 12500, orders: 198, rating: 4.6 },
    { name: 'Burger Hub', revenue: 11200, orders: 187, rating: 4.7 },
    { name: 'Sushi Master', revenue: 9800, orders: 156, rating: 4.9 },
    { name: 'Taco Bell', revenue: 8900, orders: 142, rating: 4.5 },
  ];

  const recentActivity = [
    { type: 'order', message: 'New order #12345 placed', time: '2 minutes ago' },
    { type: 'restaurant', message: 'New restaurant "Curry House" registered', time: '15 minutes ago' },
    { type: 'user', message: '25 new users registered today', time: '1 hour ago' },
    { type: 'payment', message: 'Payment of ₹5,000 processed', time: '2 hours ago' },
  ];

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex">
        <AdminSidebar />
        <div className="flex-1 p-8">
          <div className="max-w-7xl mx-auto">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                {[...Array(4)].map((_, i) => (
                  <div key={i} className="h-32 bg-gray-200 rounded-lg"></div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex">
      <AdminSidebar />
      
      <div className="flex-1 p-8">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <BarChart3 className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">System Analytics</h1>
                <p className="text-gray-600">Comprehensive platform performance insights</p>
              </div>
            </div>

            <div className="flex items-center gap-4">
              {/* Period Selector */}
              <div className="flex bg-white rounded-lg p-1 shadow-sm border">
                {periods.map((period) => (
                  <button
                    key={period.value}
                    onClick={() => setSelectedPeriod(period.value)}
                    className={`px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                      selectedPeriod === period.value
                        ? 'bg-blue-500 text-white shadow-sm'
                        : 'text-gray-600 hover:text-blue-600'
                    }`}
                  >
                    {period.label}
                  </button>
                ))}
              </div>

              {/* Export Button */}
              <button className="flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200">
                <Download className="w-4 h-4" />
                Export
              </button>
            </div>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {stats.map((stat, index) => (
              <motion.div
                key={stat.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-white rounded-2xl p-6 shadow-md hover:shadow-lg transition-shadow duration-200"
              >
                <div className="flex items-center justify-between mb-4">
                  <div className={`w-12 h-12 ${stat.bgColor} rounded-lg flex items-center justify-center`}>
                    <stat.icon className={`w-6 h-6 ${stat.color}`} />
                  </div>
                  <div className={`flex items-center gap-1 text-sm ${
                    stat.growth >= 0 ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {stat.growth >= 0 ? (
                      <ArrowUpRight className="w-4 h-4" />
                    ) : (
                      <ArrowDownRight className="w-4 h-4" />
                    )}
                    {Math.abs(stat.growth)}%
                  </div>
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-1">
                    {stat.format === 'currency' ? `₹${stat.value.toLocaleString()}` : stat.value.toLocaleString()}
                  </h3>
                  <p className="text-gray-600 text-sm mb-2">{stat.title}</p>
                  <p className="text-xs text-gray-500">
                    {stat.format === 'currency' ? `₹${stat.periodValue.toLocaleString()}` : stat.periodValue.toLocaleString()} this period
                  </p>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Charts and Tables */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
            {/* Revenue Chart */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="lg:col-span-2 bg-white rounded-2xl p-6 shadow-md"
            >
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900">Revenue Trend</h3>
                <div className="flex bg-gray-100 rounded-lg p-1">
                  {metrics.map((metric) => (
                    <button
                      key={metric.value}
                      onClick={() => setSelectedMetric(metric.value)}
                      className={`px-3 py-1 rounded-md text-sm font-medium transition-all duration-200 ${
                        selectedMetric === metric.value
                          ? 'bg-white text-gray-900 shadow-sm'
                          : 'text-gray-600 hover:text-gray-900'
                      }`}
                    >
                      {metric.label}
                    </button>
                  ))}
                </div>
              </div>
              <div className="h-64 flex items-center justify-center text-gray-500">
                <div className="text-center">
                  <BarChart3 className="w-12 h-12 mx-auto mb-2 text-gray-300" />
                  <p>Chart visualization will be implemented with Chart.js</p>
                </div>
              </div>
            </motion.div>

            {/* Top Restaurants */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              className="bg-white rounded-2xl p-6 shadow-md"
            >
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Top Restaurants</h3>
              <div className="space-y-4">
                {topRestaurants.map((restaurant, index) => (
                  <div key={restaurant.name} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                        <span className="text-orange-600 font-semibold text-sm">{index + 1}</span>
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">{restaurant.name}</p>
                        <p className="text-sm text-gray-500">{restaurant.orders} orders</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-gray-900">₹{restaurant.revenue.toLocaleString()}</p>
                      <div className="flex items-center gap-1">
                        <span className="text-yellow-400">★</span>
                        <span className="text-sm text-gray-600">{restaurant.rating}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>
          </div>

          {/* Recent Activity */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="bg-white rounded-2xl p-6 shadow-md"
          >
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
            <div className="space-y-4">
              {recentActivity.map((activity, index) => (
                <div key={index} className="flex items-center gap-4 p-3 bg-gray-50 rounded-lg">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <div className="flex-1">
                    <p className="text-gray-900">{activity.message}</p>
                    <p className="text-sm text-gray-500">{activity.time}</p>
                  </div>
                </div>
              ))}
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default AdminAnalytics;
