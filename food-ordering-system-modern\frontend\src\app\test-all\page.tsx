'use client';

import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { 
  CheckCircle, 
  XCircle, 
  Loader, 
  AlertTriangle,
  User, 
  Store, 
  Truck, 
  Shield,
  ShoppingCart,
  Star,
  Home,
  Menu,
  Settings,
  BarChart3
} from 'lucide-react';
import { authAPI, restaurantAPI, vendorAPI, adminAPI, deliveryAPI } from '@/lib/api';
import useAuthStore from '@/store/useAuthStore';

interface TestResult {
  name: string;
  status: 'pending' | 'loading' | 'success' | 'error';
  message?: string;
  error?: string;
  data?: any;
}

const TestAllPage = () => {
  const { user, isAuthenticated } = useAuthStore();
  const [testResults, setTestResults] = useState<Record<string, TestResult>>({});
  const [isRunning, setIsRunning] = useState(false);

  // Define all tests
  const allTests = [
    // Public Tests
    {
      id: 'public-restaurants',
      name: 'Public Restaurants API',
      category: 'Public',
      icon: Store,
      test: () => restaurantAPI.getAll({ page: 1, limit: 5 }),
      color: 'blue'
    },
    {
      id: 'auth-status',
      name: 'Authentication Status',
      category: 'Auth',
      icon: User,
      test: () => Promise.resolve({ data: { isAuthenticated, user } }),
      color: 'green'
    },

    // Vendor Tests (only if user is vendor)
    ...(user?.role === 'vendor' ? [
      {
        id: 'vendor-restaurant',
        name: 'Vendor Restaurant',
        category: 'Vendor',
        icon: Store,
        test: () => vendorAPI.getRestaurant(),
        color: 'orange'
      },
      {
        id: 'vendor-foods',
        name: 'Vendor Foods',
        category: 'Vendor',
        icon: ShoppingCart,
        test: () => vendorAPI.getFoods(),
        color: 'purple'
      },
      {
        id: 'vendor-orders',
        name: 'Vendor Orders',
        category: 'Vendor',
        icon: Star,
        test: () => vendorAPI.getOrders(),
        color: 'yellow'
      },
      {
        id: 'vendor-analytics',
        name: 'Vendor Analytics',
        category: 'Vendor',
        icon: BarChart3,
        test: () => vendorAPI.getAnalytics({ period: '7d' }),
        color: 'indigo'
      }
    ] : []),

    // Admin Tests (only if user is admin)
    ...(user?.role === 'admin' ? [
      {
        id: 'admin-stats',
        name: 'Admin Stats',
        category: 'Admin',
        icon: Shield,
        test: () => adminAPI.getStats(),
        color: 'red'
      },
      {
        id: 'admin-restaurants',
        name: 'Admin Restaurants',
        category: 'Admin',
        icon: Store,
        test: () => adminAPI.getRestaurants(),
        color: 'red'
      }
    ] : []),

    // Delivery Tests (only if user is delivery)
    ...(user?.role === 'delivery' ? [
      {
        id: 'delivery-dashboard',
        name: 'Delivery Dashboard',
        category: 'Delivery',
        icon: Truck,
        test: () => deliveryAPI.getDashboard(),
        color: 'teal'
      }
    ] : [])
  ];

  // Page Tests
  const pageTests = [
    { name: 'Home Page', url: '/', category: 'Pages' },
    { name: 'Restaurants Page', url: '/restaurants', category: 'Pages' },
    { name: 'Login Page', url: '/login', category: 'Pages' },
    { name: 'Register Page', url: '/register', category: 'Pages' },
    
    // Vendor Pages (only if vendor)
    ...(user?.role === 'vendor' ? [
      { name: 'Vendor Dashboard', url: '/vendor/dashboard', category: 'Vendor Pages' },
      { name: 'Vendor Menu', url: '/vendor/menu', category: 'Vendor Pages' },
      { name: 'Vendor Orders', url: '/vendor/orders', category: 'Vendor Pages' },
      { name: 'Vendor Analytics', url: '/vendor/analytics', category: 'Vendor Pages' },
      { name: 'Vendor Reviews', url: '/vendor/reviews', category: 'Vendor Pages' },
      { name: 'Vendor Restaurant', url: '/vendor/restaurant', category: 'Vendor Pages' },
      { name: 'Vendor Settings', url: '/vendor/settings', category: 'Vendor Pages' }
    ] : []),

    // Admin Pages (only if admin)
    ...(user?.role === 'admin' ? [
      { name: 'Admin Dashboard', url: '/admin/dashboard', category: 'Admin Pages' },
      { name: 'Admin Users', url: '/admin/users', category: 'Admin Pages' },
      { name: 'Admin Restaurants', url: '/admin/restaurants', category: 'Admin Pages' }
    ] : [])
  ];

  const runTest = async (test: any) => {
    setTestResults(prev => ({ 
      ...prev, 
      [test.id]: { 
        name: test.name, 
        status: 'loading' 
      } 
    }));
    
    try {
      const result = await test.test();
      setTestResults(prev => ({ 
        ...prev, 
        [test.id]: { 
          name: test.name,
          status: 'success', 
          data: result.data,
          message: 'Test passed successfully'
        } 
      }));
    } catch (error: any) {
      setTestResults(prev => ({ 
        ...prev, 
        [test.id]: { 
          name: test.name,
          status: 'error', 
          error: error.response?.data?.message || error.message,
          message: 'Test failed'
        } 
      }));
    }
  };

  const runAllTests = async () => {
    setIsRunning(true);
    
    // Initialize all tests as pending
    const initialResults: Record<string, TestResult> = {};
    allTests.forEach(test => {
      initialResults[test.id] = {
        name: test.name,
        status: 'pending'
      };
    });
    setTestResults(initialResults);

    // Run tests sequentially
    for (const test of allTests) {
      await runTest(test);
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    setIsRunning(false);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'loading':
        return <Loader className="w-5 h-5 animate-spin text-blue-500" />;
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'error':
        return <XCircle className="w-5 h-5 text-red-500" />;
      case 'pending':
        return <div className="w-5 h-5 bg-gray-300 rounded-full" />;
      default:
        return <AlertTriangle className="w-5 h-5 text-yellow-500" />;
    }
  };

  const getColorClasses = (color: string) => {
    const colors: Record<string, string> = {
      blue: 'bg-blue-100 text-blue-600',
      green: 'bg-green-100 text-green-600',
      orange: 'bg-orange-100 text-orange-600',
      purple: 'bg-purple-100 text-purple-600',
      yellow: 'bg-yellow-100 text-yellow-600',
      red: 'bg-red-100 text-red-600',
      indigo: 'bg-indigo-100 text-indigo-600',
      teal: 'bg-teal-100 text-teal-600'
    };
    return colors[color] || 'bg-gray-100 text-gray-600';
  };

  // Group tests by category
  const groupedTests = allTests.reduce((acc, test) => {
    if (!acc[test.category]) {
      acc[test.category] = [];
    }
    acc[test.category].push(test);
    return acc;
  }, {} as Record<string, any[]>);

  // Calculate summary
  const totalTests = allTests.length;
  const completedTests = Object.values(testResults).filter(r => r.status !== 'pending' && r.status !== 'loading').length;
  const successfulTests = Object.values(testResults).filter(r => r.status === 'success').length;
  const failedTests = Object.values(testResults).filter(r => r.status === 'error').length;

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="bg-white rounded-2xl shadow-lg p-8 mb-8">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Complete System Test</h1>
              <p className="text-gray-600">Comprehensive testing of all APIs and functionalities</p>
            </div>
            <button
              onClick={runAllTests}
              disabled={isRunning}
              className={`px-6 py-3 rounded-lg font-medium transition-colors ${
                isRunning 
                  ? 'bg-gray-400 text-white cursor-not-allowed' 
                  : 'bg-blue-500 text-white hover:bg-blue-600'
              }`}
            >
              {isRunning ? 'Running Tests...' : 'Run All Tests'}
            </button>
          </div>

          {/* Summary */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-gray-50 rounded-lg p-4">
              <p className="text-sm text-gray-600">Total Tests</p>
              <p className="text-2xl font-bold text-gray-900">{totalTests}</p>
            </div>
            <div className="bg-blue-50 rounded-lg p-4">
              <p className="text-sm text-blue-600">Completed</p>
              <p className="text-2xl font-bold text-blue-900">{completedTests}</p>
            </div>
            <div className="bg-green-50 rounded-lg p-4">
              <p className="text-sm text-green-600">Successful</p>
              <p className="text-2xl font-bold text-green-900">{successfulTests}</p>
            </div>
            <div className="bg-red-50 rounded-lg p-4">
              <p className="text-sm text-red-600">Failed</p>
              <p className="text-2xl font-bold text-red-900">{failedTests}</p>
            </div>
          </div>
        </div>

        {/* User Info */}
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Current User Context</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <p className="text-sm text-gray-600">Authentication</p>
              <p className="font-medium">{isAuthenticated ? '✅ Authenticated' : '❌ Not Authenticated'}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Role</p>
              <p className="font-medium">{user?.role || 'None'}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Name</p>
              <p className="font-medium">{user?.name || 'N/A'}</p>
            </div>
          </div>
        </div>

        {/* API Tests */}
        {Object.entries(groupedTests).map(([category, tests]) => (
          <div key={category} className="bg-white rounded-lg shadow p-6 mb-8">
            <h2 className="text-lg font-semibold text-gray-900 mb-6">{category} Tests</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {tests.map((test) => {
                const result = testResults[test.id];
                const IconComponent = test.icon;
                
                return (
                  <div key={test.id} className="border rounded-xl p-6 hover:shadow-md transition-shadow">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center gap-3">
                        <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${getColorClasses(test.color)}`}>
                          <IconComponent className="w-5 h-5" />
                        </div>
                        <h3 className="font-semibold text-gray-900">{test.name}</h3>
                      </div>
                      {getStatusIcon(result?.status || 'pending')}
                    </div>
                    
                    <div className="space-y-2">
                      <button
                        onClick={() => runTest(test)}
                        disabled={result?.status === 'loading'}
                        className="w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors text-sm disabled:opacity-50"
                      >
                        {result?.status === 'loading' ? 'Testing...' : 'Run Test'}
                      </button>
                      
                      {result && result.status !== 'pending' && (
                        <div className="text-sm">
                          <p className={`font-medium ${
                            result.status === 'success' ? 'text-green-600' : 
                            result.status === 'error' ? 'text-red-600' : 'text-blue-600'
                          }`}>
                            {result.message}
                          </p>
                          {result.error && (
                            <p className="text-red-500 mt-1 text-xs">{result.error}</p>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        ))}

        {/* Page Tests */}
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <h2 className="text-lg font-semibold text-gray-900 mb-6">Page Navigation Tests</h2>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {pageTests.map((page, index) => (
              <a
                key={index}
                href={page.url}
                target="_blank"
                rel="noopener noreferrer"
                className="p-4 border rounded-lg text-center hover:bg-gray-50 transition-colors"
              >
                <p className="font-medium text-gray-900">{page.name}</p>
                <p className="text-sm text-gray-500">{page.url}</p>
              </a>
            ))}
          </div>
        </div>

        {/* Error Summary */}
        {failedTests > 0 && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-6">
            <h2 className="text-lg font-semibold text-red-900 mb-4">Failed Tests Summary</h2>
            <div className="space-y-2">
              {Object.entries(testResults)
                .filter(([_, result]) => result.status === 'error')
                .map(([testId, result]) => (
                  <div key={testId} className="bg-white rounded p-3">
                    <p className="font-medium text-red-900">{result.name}</p>
                    <p className="text-sm text-red-600">{result.error}</p>
                  </div>
                ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default TestAllPage;
