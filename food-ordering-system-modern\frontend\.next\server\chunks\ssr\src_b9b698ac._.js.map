{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/project%20food-order%206k/food-ordering-system-modern/frontend/src/components/VendorSidebar.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { usePathname } from 'next/navigation';\nimport { \n  LayoutDashboard,\n  Store,\n  Package,\n  ChefHat,\n  BarChart3,\n  Settings,\n  Users,\n  MessageCircle,\n  Bell,\n  LogOut\n} from 'lucide-react';\nimport Link from 'next/link';\nimport useAuthStore from '@/store/useAuthStore';\n\nconst VendorSidebar = () => {\n  const pathname = usePathname();\n  const { logout } = useAuthStore();\n\n  const menuItems = [\n    {\n      name: 'Dashboard',\n      href: '/vendor/dashboard',\n      icon: LayoutDashboard,\n    },\n    {\n      name: 'Restaurant',\n      href: '/vendor/restaurant',\n      icon: Store,\n    },\n    {\n      name: 'Menu Management',\n      href: '/vendor/menu',\n      icon: ChefHat,\n    },\n    {\n      name: 'Orders',\n      href: '/vendor/orders',\n      icon: Package,\n    },\n    {\n      name: 'Analytics',\n      href: '/vendor/analytics',\n      icon: BarChart3,\n    },\n    {\n      name: 'Reviews',\n      href: '/vendor/reviews',\n      icon: MessageCircle,\n    },\n    {\n      name: 'Customers',\n      href: '/vendor/customers',\n      icon: Users,\n    },\n    {\n      name: 'Notifications',\n      href: '/vendor/notifications',\n      icon: Bell,\n    },\n    {\n      name: 'Settings',\n      href: '/vendor/settings',\n      icon: Settings,\n    },\n  ];\n\n  const handleLogout = async () => {\n    await logout();\n    window.location.href = '/';\n  };\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, x: -20 }}\n      animate={{ opacity: 1, x: 0 }}\n      transition={{ duration: 0.6 }}\n      className=\"bg-white rounded-2xl shadow-lg p-6 sticky top-24\"\n    >\n      {/* Sidebar Header */}\n      <div className=\"mb-8\">\n        <h2 className=\"text-lg font-bold text-gray-900 mb-2\">Vendor Panel</h2>\n        <p className=\"text-sm text-gray-600\">Manage your restaurant</p>\n      </div>\n\n      {/* Navigation Menu */}\n      <nav className=\"space-y-2\">\n        {menuItems.map((item) => {\n          const isActive = pathname === item.href;\n          \n          return (\n            <Link key={item.name} href={item.href}>\n              <motion.div\n                whileHover={{ scale: 1.02 }}\n                whileTap={{ scale: 0.98 }}\n                className={`flex items-center gap-3 px-4 py-3 rounded-lg transition-all duration-200 ${\n                  isActive\n                    ? 'bg-orange-50 text-orange-600 border border-orange-200'\n                    : 'text-gray-700 hover:bg-gray-50 hover:text-orange-600'\n                }`}\n              >\n                <item.icon className={`w-5 h-5 ${\n                  isActive ? 'text-orange-600' : 'text-gray-500'\n                }`} />\n                <span className=\"font-medium\">{item.name}</span>\n              </motion.div>\n            </Link>\n          );\n        })}\n      </nav>\n\n      {/* Logout Button */}\n      <div className=\"mt-8 pt-6 border-t border-gray-200\">\n        <motion.button\n          whileHover={{ scale: 1.02 }}\n          whileTap={{ scale: 0.98 }}\n          onClick={handleLogout}\n          className=\"flex items-center gap-3 px-4 py-3 w-full text-left text-red-600 hover:bg-red-50 rounded-lg transition-all duration-200\"\n        >\n          <LogOut className=\"w-5 h-5\" />\n          <span className=\"font-medium\">Logout</span>\n        </motion.button>\n      </div>\n\n      {/* Help Section */}\n      <div className=\"mt-6 p-4 bg-gradient-to-r from-orange-50 to-red-50 rounded-lg\">\n        <h3 className=\"text-sm font-semibold text-gray-900 mb-2\">Need Help?</h3>\n        <p className=\"text-xs text-gray-600 mb-3\">\n          Get support for managing your restaurant on our platform.\n        </p>\n        <motion.button\n          whileHover={{ scale: 1.02 }}\n          whileTap={{ scale: 0.98 }}\n          className=\"w-full bg-orange-500 text-white py-2 px-3 rounded-lg text-sm font-medium hover:bg-orange-600 transition-colors duration-200\"\n        >\n          Contact Support\n        </motion.button>\n      </div>\n    </motion.div>\n  );\n};\n\nexport default VendorSidebar;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AAjBA;;;;;;;AAmBA,MAAM,gBAAgB;IACpB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,UAAY,AAAD;IAE9B,MAAM,YAAY;QAChB;YACE,MAAM;YACN,MAAM;YACN,MAAM,4NAAA,CAAA,kBAAe;QACvB;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,oMAAA,CAAA,QAAK;QACb;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,4MAAA,CAAA,UAAO;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,wMAAA,CAAA,UAAO;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,kNAAA,CAAA,YAAS;QACjB;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,wNAAA,CAAA,gBAAa;QACrB;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,oMAAA,CAAA,QAAK;QACb;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,kMAAA,CAAA,OAAI;QACZ;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,0MAAA,CAAA,WAAQ;QAChB;KACD;IAED,MAAM,eAAe;QACnB,MAAM;QACN,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC9B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAU;;0BAGV,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuC;;;;;;kCACrD,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;0BAIvC,8OAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC;oBACd,MAAM,WAAW,aAAa,KAAK,IAAI;oBAEvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;wBAAiB,MAAM,KAAK,IAAI;kCACnC,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,UAAU;gCAAE,OAAO;4BAAK;4BACxB,WAAW,CAAC,yEAAyE,EACnF,WACI,0DACA,wDACJ;;8CAEF,8OAAC,KAAK,IAAI;oCAAC,WAAW,CAAC,QAAQ,EAC7B,WAAW,oBAAoB,iBAC/B;;;;;;8CACF,8OAAC;oCAAK,WAAU;8CAAe,KAAK,IAAI;;;;;;;;;;;;uBAbjC,KAAK,IAAI;;;;;gBAiBxB;;;;;;0BAIF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oBACZ,YAAY;wBAAE,OAAO;oBAAK;oBAC1B,UAAU;wBAAE,OAAO;oBAAK;oBACxB,SAAS;oBACT,WAAU;;sCAEV,8OAAC,0MAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,8OAAC;4BAAK,WAAU;sCAAc;;;;;;;;;;;;;;;;;0BAKlC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;kCAG1C,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wBACZ,YAAY;4BAAE,OAAO;wBAAK;wBAC1B,UAAU;4BAAE,OAAO;wBAAK;wBACxB,WAAU;kCACX;;;;;;;;;;;;;;;;;;AAMT;uCAEe", "debugId": null}}, {"offset": {"line": 259, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/project%20food-order%206k/food-ordering-system-modern/frontend/src/lib/utils.js"], "sourcesContent": ["import { clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs) {\n  return twMerge(clsx(inputs));\n}\n\n// Format currency\nexport const formatCurrency = (amount) => {\n  return new Intl.NumberFormat('en-IN', {\n    style: 'currency',\n    currency: 'INR',\n    minimumFractionDigits: 0,\n    maximumFractionDigits: 2,\n  }).format(amount);\n};\n\n// Format date\nexport const formatDate = (date) => {\n  return new Intl.DateTimeFormat('en-IN', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  }).format(new Date(date));\n};\n\n// Format time\nexport const formatTime = (date) => {\n  return new Intl.DateTimeFormat('en-IN', {\n    hour: '2-digit',\n    minute: '2-digit',\n    hour12: true,\n  }).format(new Date(date));\n};\n\n// Format relative time\nexport const formatRelativeTime = (date) => {\n  const now = new Date();\n  const targetDate = new Date(date);\n  const diffInSeconds = Math.floor((now - targetDate) / 1000);\n\n  if (diffInSeconds < 60) {\n    return 'Just now';\n  } else if (diffInSeconds < 3600) {\n    const minutes = Math.floor(diffInSeconds / 60);\n    return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;\n  } else if (diffInSeconds < 86400) {\n    const hours = Math.floor(diffInSeconds / 3600);\n    return `${hours} hour${hours > 1 ? 's' : ''} ago`;\n  } else {\n    const days = Math.floor(diffInSeconds / 86400);\n    return `${days} day${days > 1 ? 's' : ''} ago`;\n  }\n};\n\n// Calculate distance between two coordinates\nexport const calculateDistance = (lat1, lon1, lat2, lon2) => {\n  const R = 6371; // Radius of the Earth in kilometers\n  const dLat = (lat2 - lat1) * Math.PI / 180;\n  const dLon = (lon2 - lon1) * Math.PI / 180;\n  const a = \n    Math.sin(dLat/2) * Math.sin(dLat/2) +\n    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * \n    Math.sin(dLon/2) * Math.sin(dLon/2);\n  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));\n  const distance = R * c;\n  return Math.round(distance * 10) / 10; // Round to 1 decimal place\n};\n\n// Generate order status color\nexport const getOrderStatusColor = (status) => {\n  const colors = {\n    placed: 'bg-blue-100 text-blue-800',\n    confirmed: 'bg-green-100 text-green-800',\n    preparing: 'bg-yellow-100 text-yellow-800',\n    ready: 'bg-purple-100 text-purple-800',\n    'picked-up': 'bg-indigo-100 text-indigo-800',\n    'out-for-delivery': 'bg-orange-100 text-orange-800',\n    delivered: 'bg-green-100 text-green-800',\n    cancelled: 'bg-red-100 text-red-800',\n    refunded: 'bg-gray-100 text-gray-800',\n  };\n  return colors[status] || 'bg-gray-100 text-gray-800';\n};\n\n// Generate random ID\nexport const generateId = () => {\n  return Math.random().toString(36).substr(2, 9);\n};\n\n// Debounce function\nexport const debounce = (func, wait) => {\n  let timeout;\n  return function executedFunction(...args) {\n    const later = () => {\n      clearTimeout(timeout);\n      func(...args);\n    };\n    clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n  };\n};\n\n// Throttle function\nexport const throttle = (func, limit) => {\n  let inThrottle;\n  return function() {\n    const args = arguments;\n    const context = this;\n    if (!inThrottle) {\n      func.apply(context, args);\n      inThrottle = true;\n      setTimeout(() => inThrottle = false, limit);\n    }\n  };\n};\n\n// Validate email\nexport const isValidEmail = (email) => {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n};\n\n// Validate phone number (Indian format)\nexport const isValidPhone = (phone) => {\n  const phoneRegex = /^[6-9]\\d{9}$/;\n  return phoneRegex.test(phone);\n};\n\n// Get user location\nexport const getUserLocation = () => {\n  return new Promise((resolve, reject) => {\n    if (!navigator.geolocation) {\n      reject(new Error('Geolocation is not supported by this browser.'));\n      return;\n    }\n\n    navigator.geolocation.getCurrentPosition(\n      (position) => {\n        resolve({\n          latitude: position.coords.latitude,\n          longitude: position.coords.longitude,\n        });\n      },\n      (error) => {\n        reject(error);\n      },\n      {\n        enableHighAccuracy: true,\n        timeout: 10000,\n        maximumAge: 600000, // 10 minutes\n      }\n    );\n  });\n};\n\n// Local storage helpers\nexport const storage = {\n  get: (key) => {\n    try {\n      const item = localStorage.getItem(key);\n      return item ? JSON.parse(item) : null;\n    } catch (error) {\n      console.error('Error getting from localStorage:', error);\n      return null;\n    }\n  },\n  set: (key, value) => {\n    try {\n      localStorage.setItem(key, JSON.stringify(value));\n    } catch (error) {\n      console.error('Error setting to localStorage:', error);\n    }\n  },\n  remove: (key) => {\n    try {\n      localStorage.removeItem(key);\n    } catch (error) {\n      console.error('Error removing from localStorage:', error);\n    }\n  },\n};\n\n// Image optimization\nexport const getOptimizedImageUrl = (url, width = 400, height = 300) => {\n  if (!url) return '/images/placeholder.jpg';\n  \n  // If it's already an optimized URL, return as is\n  if (url.includes('w_') || url.includes('h_')) return url;\n  \n  // For Cloudinary URLs, add optimization parameters\n  if (url.includes('cloudinary.com')) {\n    return url.replace('/upload/', `/upload/w_${width},h_${height},c_fill,f_auto,q_auto/`);\n  }\n  \n  return url;\n};\n\n// Truncate text\nexport const truncateText = (text, maxLength = 100) => {\n  if (!text) return '';\n  if (text.length <= maxLength) return text;\n  return text.substr(0, maxLength) + '...';\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAM;IAC1B,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,MAAM,iBAAiB,CAAC;IAC7B,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;QACV,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAGO,MAAM,aAAa,CAAC;IACzB,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAGO,MAAM,aAAa,CAAC;IACzB,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,QAAQ;QACR,QAAQ;IACV,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAGO,MAAM,qBAAqB,CAAC;IACjC,MAAM,MAAM,IAAI;IAChB,MAAM,aAAa,IAAI,KAAK;IAC5B,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,MAAM,UAAU,IAAI;IAEtD,IAAI,gBAAgB,IAAI;QACtB,OAAO;IACT,OAAO,IAAI,gBAAgB,MAAM;QAC/B,MAAM,UAAU,KAAK,KAAK,CAAC,gBAAgB;QAC3C,OAAO,GAAG,QAAQ,OAAO,EAAE,UAAU,IAAI,MAAM,GAAG,IAAI,CAAC;IACzD,OAAO,IAAI,gBAAgB,OAAO;QAChC,MAAM,QAAQ,KAAK,KAAK,CAAC,gBAAgB;QACzC,OAAO,GAAG,MAAM,KAAK,EAAE,QAAQ,IAAI,MAAM,GAAG,IAAI,CAAC;IACnD,OAAO;QACL,MAAM,OAAO,KAAK,KAAK,CAAC,gBAAgB;QACxC,OAAO,GAAG,KAAK,IAAI,EAAE,OAAO,IAAI,MAAM,GAAG,IAAI,CAAC;IAChD;AACF;AAGO,MAAM,oBAAoB,CAAC,MAAM,MAAM,MAAM;IAClD,MAAM,IAAI,MAAM,oCAAoC;IACpD,MAAM,OAAO,CAAC,OAAO,IAAI,IAAI,KAAK,EAAE,GAAG;IACvC,MAAM,OAAO,CAAC,OAAO,IAAI,IAAI,KAAK,EAAE,GAAG;IACvC,MAAM,IACJ,KAAK,GAAG,CAAC,OAAK,KAAK,KAAK,GAAG,CAAC,OAAK,KACjC,KAAK,GAAG,CAAC,OAAO,KAAK,EAAE,GAAG,OAAO,KAAK,GAAG,CAAC,OAAO,KAAK,EAAE,GAAG,OAC3D,KAAK,GAAG,CAAC,OAAK,KAAK,KAAK,GAAG,CAAC,OAAK;IACnC,MAAM,IAAI,IAAI,KAAK,KAAK,CAAC,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAE;IACnD,MAAM,WAAW,IAAI;IACrB,OAAO,KAAK,KAAK,CAAC,WAAW,MAAM,IAAI,2BAA2B;AACpE;AAGO,MAAM,sBAAsB,CAAC;IAClC,MAAM,SAAS;QACb,QAAQ;QACR,WAAW;QACX,WAAW;QACX,OAAO;QACP,aAAa;QACb,oBAAoB;QACpB,WAAW;QACX,WAAW;QACX,UAAU;IACZ;IACA,OAAO,MAAM,CAAC,OAAO,IAAI;AAC3B;AAGO,MAAM,aAAa;IACxB,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAGO,MAAM,WAAW,CAAC,MAAM;IAC7B,IAAI;IACJ,OAAO,SAAS,iBAAiB,GAAG,IAAI;QACtC,MAAM,QAAQ;YACZ,aAAa;YACb,QAAQ;QACV;QACA,aAAa;QACb,UAAU,WAAW,OAAO;IAC9B;AACF;AAGO,MAAM,WAAW,CAAC,MAAM;IAC7B,IAAI;IACJ,OAAO;QACL,MAAM,OAAO;QACb,MAAM,UAAU,IAAI;QACpB,IAAI,CAAC,YAAY;YACf,KAAK,KAAK,CAAC,SAAS;YACpB,aAAa;YACb,WAAW,IAAM,aAAa,OAAO;QACvC;IACF;AACF;AAGO,MAAM,eAAe,CAAC;IAC3B,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAGO,MAAM,eAAe,CAAC;IAC3B,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAGO,MAAM,kBAAkB;IAC7B,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,IAAI,CAAC,UAAU,WAAW,EAAE;YAC1B,OAAO,IAAI,MAAM;YACjB;QACF;QAEA,UAAU,WAAW,CAAC,kBAAkB,CACtC,CAAC;YACC,QAAQ;gBACN,UAAU,SAAS,MAAM,CAAC,QAAQ;gBAClC,WAAW,SAAS,MAAM,CAAC,SAAS;YACtC;QACF,GACA,CAAC;YACC,OAAO;QACT,GACA;YACE,oBAAoB;YACpB,SAAS;YACT,YAAY;QACd;IAEJ;AACF;AAGO,MAAM,UAAU;IACrB,KAAK,CAAC;QACJ,IAAI;YACF,MAAM,OAAO,aAAa,OAAO,CAAC;YAClC,OAAO,OAAO,KAAK,KAAK,CAAC,QAAQ;QACnC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO;QACT;IACF;IACA,KAAK,CAAC,KAAK;QACT,IAAI;YACF,aAAa,OAAO,CAAC,KAAK,KAAK,SAAS,CAAC;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD;IACF;IACA,QAAQ,CAAC;QACP,IAAI;YACF,aAAa,UAAU,CAAC;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;QACrD;IACF;AACF;AAGO,MAAM,uBAAuB,CAAC,KAAK,QAAQ,GAAG,EAAE,SAAS,GAAG;IACjE,IAAI,CAAC,KAAK,OAAO;IAEjB,iDAAiD;IACjD,IAAI,IAAI,QAAQ,CAAC,SAAS,IAAI,QAAQ,CAAC,OAAO,OAAO;IAErD,mDAAmD;IACnD,IAAI,IAAI,QAAQ,CAAC,mBAAmB;QAClC,OAAO,IAAI,OAAO,CAAC,YAAY,CAAC,UAAU,EAAE,MAAM,GAAG,EAAE,OAAO,sBAAsB,CAAC;IACvF;IAEA,OAAO;AACT;AAGO,MAAM,eAAe,CAAC,MAAM,YAAY,GAAG;IAChD,IAAI,CAAC,MAAM,OAAO;IAClB,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,MAAM,CAAC,GAAG,aAAa;AACrC", "debugId": null}}, {"offset": {"line": 444, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/project%20food-order%206k/food-ordering-system-modern/frontend/src/app/vendor/restaurant/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { \n  Store, \n  Edit, \n  Save, \n  Upload, \n  MapPin, \n  Clock, \n  Phone, \n  Mail,\n  Star,\n  DollarSign,\n  Users,\n  TrendingUp\n} from 'lucide-react';\nimport { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';\nimport Image from 'next/image';\nimport VendorSidebar from '@/components/VendorSidebar';\nimport { vendorAPI } from '@/lib/api';\nimport { formatCurrency } from '@/lib/utils';\nimport { toast } from 'react-hot-toast';\n\nconst VendorRestaurantPage = () => {\n  const [isEditing, setIsEditing] = useState(false);\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    phone: '',\n    email: '',\n    cuisine: [],\n    location: {\n      address: {\n        street: '',\n        city: '',\n        state: '',\n        zipCode: ''\n      },\n      coordinates: {\n        latitude: 19.0760, // Default to Mumbai coordinates\n        longitude: 72.8777\n      }\n    },\n    operatingHours: [],\n    pricing: {\n      deliveryFee: 0,\n      minimumOrder: 0,\n      packagingFee: 0\n    }\n  });\n\n  const queryClient = useQueryClient();\n\n  // Fetch restaurant data\n  const { data: restaurantData, isLoading } = useQuery({\n    queryKey: ['vendor-restaurant'],\n    queryFn: () => vendorAPI.getRestaurant(),\n  });\n\n  const restaurant = restaurantData?.data?.data;\n\n  // Create restaurant mutation\n  const createRestaurantMutation = useMutation({\n    mutationFn: (data: any) => vendorAPI.createRestaurant(data),\n    onSuccess: () => {\n      toast.success('Restaurant created successfully!');\n      setIsEditing(false);\n      queryClient.invalidateQueries({ queryKey: ['vendor-restaurant'] });\n    },\n    onError: (error: any) => {\n      toast.error(error.response?.data?.message || 'Failed to create restaurant');\n    },\n  });\n\n  // Update restaurant mutation\n  const updateRestaurantMutation = useMutation({\n    mutationFn: (data: any) => vendorAPI.updateRestaurant(data),\n    onSuccess: () => {\n      toast.success('Restaurant updated successfully!');\n      setIsEditing(false);\n      queryClient.invalidateQueries({ queryKey: ['vendor-restaurant'] });\n    },\n    onError: (error: any) => {\n      toast.error(error.response?.data?.message || 'Failed to update restaurant');\n    },\n  });\n\n  useEffect(() => {\n    if (restaurant) {\n      setFormData(restaurant);\n      setIsEditing(false);\n    } else {\n      // If no restaurant exists, enable editing mode for creation\n      setIsEditing(true);\n    }\n  }, [restaurant]);\n\n  const validateForm = () => {\n    const errors = [];\n\n    if (!formData.name?.trim()) errors.push('Restaurant name is required');\n    if (!formData.description?.trim()) errors.push('Description is required');\n    if (!formData.phone?.trim()) errors.push('Phone number is required');\n    if (formData.phone && !/^\\d{10}$/.test(formData.phone)) errors.push('Phone number must be 10 digits');\n    if (!formData.email?.trim()) errors.push('Email is required');\n    if (formData.email && !/^\\w+([.-]?\\w+)*@\\w+([.-]?\\w+)*(\\.\\w{2,3})+$/.test(formData.email)) errors.push('Please enter a valid email');\n    if (!formData.location?.address?.street?.trim()) errors.push('Street address is required');\n    if (!formData.location?.address?.city?.trim()) errors.push('City is required');\n    if (!formData.location?.address?.state?.trim()) errors.push('State is required');\n    if (!formData.location?.address?.zipCode?.trim()) errors.push('ZIP code is required');\n\n    if (errors.length > 0) {\n      toast.error(errors[0]); // Show first error\n      return false;\n    }\n    return true;\n  };\n\n  const handleSave = () => {\n    if (!validateForm()) return;\n\n    // Ensure coordinates are properly set\n    const dataToSend = {\n      ...formData,\n      location: {\n        ...formData.location,\n        coordinates: {\n          latitude: formData.location?.coordinates?.latitude || 19.0760,\n          longitude: formData.location?.coordinates?.longitude || 72.8777\n        }\n      }\n    };\n\n    console.log('Sending restaurant data:', dataToSend);\n\n    if (restaurant) {\n      updateRestaurantMutation.mutate(dataToSend);\n    } else {\n      createRestaurantMutation.mutate(dataToSend);\n    }\n  };\n\n  const handleInputChange = (field: string, value: any) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  const handleNestedInputChange = (parent: string, field: string, value: any) => {\n    setFormData(prev => ({\n      ...prev,\n      [parent]: {\n        ...prev[parent as keyof typeof prev],\n        [field]: value\n      }\n    }));\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex\">\n        <VendorSidebar />\n        <div className=\"flex-1 p-8\">\n          <div className=\"animate-pulse\">\n            <div className=\"h-8 bg-gray-200 rounded w-1/4 mb-6\"></div>\n            <div className=\"bg-white rounded-2xl shadow-md p-6\">\n              <div className=\"space-y-4\">\n                {[...Array(6)].map((_, i) => (\n                  <div key={i} className=\"h-4 bg-gray-200 rounded\"></div>\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 flex\">\n      <VendorSidebar />\n      \n      <div className=\"flex-1 p-8\">\n        <div className=\"max-w-4xl mx-auto\">\n          {/* Header */}\n          <div className=\"flex items-center justify-between mb-8\">\n            <div className=\"flex items-center gap-3\">\n              <div className=\"w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center\">\n                <Store className=\"w-5 h-5 text-orange-600\" />\n              </div>\n              <div>\n                <h1 className=\"text-2xl font-bold text-gray-900\">\n                  {restaurant ? 'Restaurant Profile' : 'Set Up Your Restaurant'}\n                </h1>\n                <p className=\"text-gray-600\">\n                  {restaurant ? 'Manage your restaurant information' : 'Create your restaurant profile to start receiving orders'}\n                </p>\n              </div>\n            </div>\n\n            <div className=\"flex gap-3\">\n              {isEditing ? (\n                <>\n                  {restaurant && (\n                    <button\n                      onClick={() => setIsEditing(false)}\n                      className=\"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors duration-200\"\n                    >\n                      Cancel\n                    </button>\n                  )}\n                  <motion.button\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    onClick={handleSave}\n                    disabled={updateRestaurantMutation.isPending || createRestaurantMutation.isPending}\n                    className=\"flex items-center gap-2 px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors duration-200 disabled:opacity-50\"\n                  >\n                    <Save className=\"w-4 h-4\" />\n                    {(updateRestaurantMutation.isPending || createRestaurantMutation.isPending)\n                      ? 'Saving...'\n                      : restaurant ? 'Save Changes' : 'Create Restaurant'\n                    }\n                  </motion.button>\n                </>\n              ) : (\n                <motion.button\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                  onClick={() => setIsEditing(true)}\n                  className=\"flex items-center gap-2 px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors duration-200\"\n                >\n                  <Edit className=\"w-4 h-4\" />\n                  Edit Profile\n                </motion.button>\n              )}\n            </div>\n          </div>\n\n          {restaurant ? (\n            <div className=\"space-y-6\">\n              {/* Stats Cards */}\n              <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n                <div className=\"bg-white rounded-xl shadow-md p-6\">\n                  <div className=\"flex items-center gap-3\">\n                    <div className=\"w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center\">\n                      <Star className=\"w-5 h-5 text-green-600\" />\n                    </div>\n                    <div>\n                      <p className=\"text-sm text-gray-600\">Rating</p>\n                      <p className=\"text-xl font-bold text-gray-900\">\n                        {restaurant.ratings?.average?.toFixed(1) || '0.0'}\n                      </p>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"bg-white rounded-xl shadow-md p-6\">\n                  <div className=\"flex items-center gap-3\">\n                    <div className=\"w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center\">\n                      <Users className=\"w-5 h-5 text-blue-600\" />\n                    </div>\n                    <div>\n                      <p className=\"text-sm text-gray-600\">Total Orders</p>\n                      <p className=\"text-xl font-bold text-gray-900\">{restaurant.totalOrders || 0}</p>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"bg-white rounded-xl shadow-md p-6\">\n                  <div className=\"flex items-center gap-3\">\n                    <div className=\"w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center\">\n                      <DollarSign className=\"w-5 h-5 text-purple-600\" />\n                    </div>\n                    <div>\n                      <p className=\"text-sm text-gray-600\">Revenue</p>\n                      <p className=\"text-xl font-bold text-gray-900\">\n                        {formatCurrency(restaurant.totalRevenue || 0)}\n                      </p>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"bg-white rounded-xl shadow-md p-6\">\n                  <div className=\"flex items-center gap-3\">\n                    <div className=\"w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center\">\n                      <TrendingUp className=\"w-5 h-5 text-orange-600\" />\n                    </div>\n                    <div>\n                      <p className=\"text-sm text-gray-600\">Status</p>\n                      <p className={`text-sm font-medium ${\n                        restaurant.status === 'approved' ? 'text-green-600' : 'text-yellow-600'\n                      }`}>\n                        {restaurant.status?.charAt(0).toUpperCase() + restaurant.status?.slice(1)}\n                      </p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Restaurant Information */}\n              <div className=\"bg-white rounded-2xl shadow-md p-6\">\n                <h2 className=\"text-xl font-semibold text-gray-900 mb-6\">Restaurant Information</h2>\n                \n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Restaurant Name\n                    </label>\n                    {isEditing ? (\n                      <input\n                        type=\"text\"\n                        value={formData.name}\n                        onChange={(e) => handleInputChange('name', e.target.value)}\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent\"\n                      />\n                    ) : (\n                      <p className=\"text-gray-900\">{restaurant.name}</p>\n                    )}\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Phone Number\n                    </label>\n                    {isEditing ? (\n                      <input\n                        type=\"tel\"\n                        value={formData.phone}\n                        onChange={(e) => handleInputChange('phone', e.target.value)}\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent\"\n                      />\n                    ) : (\n                      <p className=\"text-gray-900\">{restaurant.phone}</p>\n                    )}\n                  </div>\n\n                  <div className=\"md:col-span-2\">\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Description\n                    </label>\n                    {isEditing ? (\n                      <textarea\n                        value={formData.description}\n                        onChange={(e) => handleInputChange('description', e.target.value)}\n                        rows={3}\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent\"\n                      />\n                    ) : (\n                      <p className=\"text-gray-900\">{restaurant.description}</p>\n                    )}\n                  </div>\n                </div>\n              </div>\n\n              {/* Location Information */}\n              <div className=\"bg-white rounded-2xl shadow-md p-6\">\n                <h2 className=\"text-xl font-semibold text-gray-900 mb-6\">Location</h2>\n                \n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Street Address\n                    </label>\n                    {isEditing ? (\n                      <input\n                        type=\"text\"\n                        value={formData.location?.address?.street || ''}\n                        onChange={(e) => handleNestedInputChange('location', 'address', {\n                          ...formData.location?.address,\n                          street: e.target.value\n                        })}\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent\"\n                      />\n                    ) : (\n                      <p className=\"text-gray-900\">{restaurant.location?.address?.street}</p>\n                    )}\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      City\n                    </label>\n                    {isEditing ? (\n                      <input\n                        type=\"text\"\n                        value={formData.location?.address?.city || ''}\n                        onChange={(e) => handleNestedInputChange('location', 'address', {\n                          ...formData.location?.address,\n                          city: e.target.value\n                        })}\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent\"\n                      />\n                    ) : (\n                      <p className=\"text-gray-900\">{restaurant.location?.address?.city}</p>\n                    )}\n                  </div>\n                </div>\n              </div>\n\n              {/* Pricing Information */}\n              <div className=\"bg-white rounded-2xl shadow-md p-6\">\n                <h2 className=\"text-xl font-semibold text-gray-900 mb-6\">Pricing</h2>\n                \n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Delivery Fee\n                    </label>\n                    {isEditing ? (\n                      <input\n                        type=\"number\"\n                        value={formData.pricing?.deliveryFee || 0}\n                        onChange={(e) => handleNestedInputChange('pricing', 'deliveryFee', Number(e.target.value))}\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent\"\n                      />\n                    ) : (\n                      <p className=\"text-gray-900\">{formatCurrency(restaurant.pricing?.deliveryFee || 0)}</p>\n                    )}\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Minimum Order\n                    </label>\n                    {isEditing ? (\n                      <input\n                        type=\"number\"\n                        value={formData.pricing?.minimumOrder || 0}\n                        onChange={(e) => handleNestedInputChange('pricing', 'minimumOrder', Number(e.target.value))}\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent\"\n                      />\n                    ) : (\n                      <p className=\"text-gray-900\">{formatCurrency(restaurant.pricing?.minimumOrder || 0)}</p>\n                    )}\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Packaging Fee\n                    </label>\n                    {isEditing ? (\n                      <input\n                        type=\"number\"\n                        value={formData.pricing?.packagingFee || 0}\n                        onChange={(e) => handleNestedInputChange('pricing', 'packagingFee', Number(e.target.value))}\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent\"\n                      />\n                    ) : (\n                      <p className=\"text-gray-900\">{formatCurrency(restaurant.pricing?.packagingFee || 0)}</p>\n                    )}\n                  </div>\n                </div>\n              </div>\n            </div>\n          ) : (\n            <div className=\"bg-white rounded-2xl shadow-md p-6\">\n              <h2 className=\"text-xl font-semibold text-gray-900 mb-6\">Restaurant Information</h2>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Restaurant Name *\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={formData.name}\n                    onChange={(e) => handleInputChange('name', e.target.value)}\n                    placeholder=\"Enter restaurant name\"\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent\"\n                    required\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Phone Number *\n                  </label>\n                  <input\n                    type=\"tel\"\n                    value={formData.phone}\n                    onChange={(e) => handleInputChange('phone', e.target.value)}\n                    placeholder=\"Enter phone number\"\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent\"\n                    required\n                  />\n                </div>\n\n                <div className=\"md:col-span-2\">\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Description *\n                  </label>\n                  <textarea\n                    value={formData.description}\n                    onChange={(e) => handleInputChange('description', e.target.value)}\n                    placeholder=\"Describe your restaurant\"\n                    rows={3}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent\"\n                    required\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Email\n                  </label>\n                  <input\n                    type=\"email\"\n                    value={formData.email}\n                    onChange={(e) => handleInputChange('email', e.target.value)}\n                    placeholder=\"Enter email address\"\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent\"\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Cuisine Types\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={Array.isArray(formData.cuisine) ? formData.cuisine.join(', ') : ''}\n                    onChange={(e) => handleInputChange('cuisine', e.target.value.split(',').map(c => c.trim()).filter(c => c))}\n                    placeholder=\"e.g., Italian, Chinese, Indian\"\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent\"\n                  />\n                </div>\n\n                <div className=\"md:col-span-2\">\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Address *\n                  </label>\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                    <input\n                      type=\"text\"\n                      value={formData.location?.address?.street || ''}\n                      onChange={(e) => {\n                        const newLocation = {\n                          ...formData.location,\n                          address: {\n                            ...formData.location?.address,\n                            street: e.target.value\n                          }\n                        };\n                        handleInputChange('location', newLocation);\n                      }}\n                      placeholder=\"Street address\"\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent\"\n                      required\n                    />\n                    <input\n                      type=\"text\"\n                      value={formData.location?.address?.city || ''}\n                      onChange={(e) => {\n                        const newLocation = {\n                          ...formData.location,\n                          address: {\n                            ...formData.location?.address,\n                            city: e.target.value\n                          }\n                        };\n                        handleInputChange('location', newLocation);\n                      }}\n                      placeholder=\"City\"\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent\"\n                      required\n                    />\n                    <input\n                      type=\"text\"\n                      value={formData.location?.address?.state || ''}\n                      onChange={(e) => {\n                        const newLocation = {\n                          ...formData.location,\n                          address: {\n                            ...formData.location?.address,\n                            state: e.target.value\n                          }\n                        };\n                        handleInputChange('location', newLocation);\n                      }}\n                      placeholder=\"State\"\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent\"\n                      required\n                    />\n                    <input\n                      type=\"text\"\n                      value={formData.location?.address?.zipCode || ''}\n                      onChange={(e) => {\n                        const newLocation = {\n                          ...formData.location,\n                          address: {\n                            ...formData.location?.address,\n                            zipCode: e.target.value\n                          }\n                        };\n                        handleInputChange('location', newLocation);\n                      }}\n                      placeholder=\"ZIP Code\"\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent\"\n                      required\n                    />\n                  </div>\n                </div>\n\n                <div className=\"md:col-span-2\">\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Location Coordinates (Optional)\n                  </label>\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                    <input\n                      type=\"number\"\n                      value={formData.location?.coordinates?.latitude || 19.0760}\n                      onChange={(e) => {\n                        const newLocation = {\n                          ...formData.location,\n                          coordinates: {\n                            ...formData.location?.coordinates,\n                            latitude: Number(e.target.value)\n                          }\n                        };\n                        handleInputChange('location', newLocation);\n                      }}\n                      placeholder=\"Latitude (e.g., 19.0760)\"\n                      step=\"any\"\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent\"\n                    />\n                    <input\n                      type=\"number\"\n                      value={formData.location?.coordinates?.longitude || 72.8777}\n                      onChange={(e) => {\n                        const newLocation = {\n                          ...formData.location,\n                          coordinates: {\n                            ...formData.location?.coordinates,\n                            longitude: Number(e.target.value)\n                          }\n                        };\n                        handleInputChange('location', newLocation);\n                      }}\n                      placeholder=\"Longitude (e.g., 72.8777)\"\n                      step=\"any\"\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent\"\n                    />\n                  </div>\n                  <p className=\"text-xs text-gray-500 mt-1\">\n                    Default coordinates are set to Mumbai. You can update them for accurate location.\n                  </p>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Delivery Fee\n                  </label>\n                  <input\n                    type=\"number\"\n                    value={formData.pricing?.deliveryFee || 0}\n                    onChange={(e) => handleNestedInputChange('pricing', 'deliveryFee', Number(e.target.value))}\n                    placeholder=\"0.00\"\n                    step=\"0.01\"\n                    min=\"0\"\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent\"\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Minimum Order\n                  </label>\n                  <input\n                    type=\"number\"\n                    value={formData.pricing?.minimumOrder || 0}\n                    onChange={(e) => handleNestedInputChange('pricing', 'minimumOrder', Number(e.target.value))}\n                    placeholder=\"0.00\"\n                    step=\"0.01\"\n                    min=\"0\"\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent\"\n                  />\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default VendorRestaurantPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AAvBA;;;;;;;;;;AAyBA,MAAM,uBAAuB;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,SAAS,EAAE;QACX,UAAU;YACR,SAAS;gBACP,QAAQ;gBACR,MAAM;gBACN,OAAO;gBACP,SAAS;YACX;YACA,aAAa;gBACX,UAAU;gBACV,WAAW;YACb;QACF;QACA,gBAAgB,EAAE;QAClB,SAAS;YACP,aAAa;YACb,cAAc;YACd,cAAc;QAChB;IACF;IAEA,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,wBAAwB;IACxB,MAAM,EAAE,MAAM,cAAc,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACnD,UAAU;YAAC;SAAoB;QAC/B,SAAS,IAAM,iHAAA,CAAA,YAAS,CAAC,aAAa;IACxC;IAEA,MAAM,aAAa,gBAAgB,MAAM;IAEzC,6BAA6B;IAC7B,MAAM,2BAA2B,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QAC3C,YAAY,CAAC,OAAc,iHAAA,CAAA,YAAS,CAAC,gBAAgB,CAAC;QACtD,WAAW;YACT,uJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,aAAa;YACb,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAoB;YAAC;QAClE;QACA,SAAS,CAAC;YACR,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,EAAE,MAAM,WAAW;QAC/C;IACF;IAEA,6BAA6B;IAC7B,MAAM,2BAA2B,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QAC3C,YAAY,CAAC,OAAc,iHAAA,CAAA,YAAS,CAAC,gBAAgB,CAAC;QACtD,WAAW;YACT,uJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,aAAa;YACb,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAoB;YAAC;QAClE;QACA,SAAS,CAAC;YACR,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,EAAE,MAAM,WAAW;QAC/C;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY;YACd,YAAY;YACZ,aAAa;QACf,OAAO;YACL,4DAA4D;YAC5D,aAAa;QACf;IACF,GAAG;QAAC;KAAW;IAEf,MAAM,eAAe;QACnB,MAAM,SAAS,EAAE;QAEjB,IAAI,CAAC,SAAS,IAAI,EAAE,QAAQ,OAAO,IAAI,CAAC;QACxC,IAAI,CAAC,SAAS,WAAW,EAAE,QAAQ,OAAO,IAAI,CAAC;QAC/C,IAAI,CAAC,SAAS,KAAK,EAAE,QAAQ,OAAO,IAAI,CAAC;QACzC,IAAI,SAAS,KAAK,IAAI,CAAC,WAAW,IAAI,CAAC,SAAS,KAAK,GAAG,OAAO,IAAI,CAAC;QACpE,IAAI,CAAC,SAAS,KAAK,EAAE,QAAQ,OAAO,IAAI,CAAC;QACzC,IAAI,SAAS,KAAK,IAAI,CAAC,8CAA8C,IAAI,CAAC,SAAS,KAAK,GAAG,OAAO,IAAI,CAAC;QACvG,IAAI,CAAC,SAAS,QAAQ,EAAE,SAAS,QAAQ,QAAQ,OAAO,IAAI,CAAC;QAC7D,IAAI,CAAC,SAAS,QAAQ,EAAE,SAAS,MAAM,QAAQ,OAAO,IAAI,CAAC;QAC3D,IAAI,CAAC,SAAS,QAAQ,EAAE,SAAS,OAAO,QAAQ,OAAO,IAAI,CAAC;QAC5D,IAAI,CAAC,SAAS,QAAQ,EAAE,SAAS,SAAS,QAAQ,OAAO,IAAI,CAAC;QAE9D,IAAI,OAAO,MAAM,GAAG,GAAG;YACrB,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,GAAG,mBAAmB;YAC3C,OAAO;QACT;QACA,OAAO;IACT;IAEA,MAAM,aAAa;QACjB,IAAI,CAAC,gBAAgB;QAErB,sCAAsC;QACtC,MAAM,aAAa;YACjB,GAAG,QAAQ;YACX,UAAU;gBACR,GAAG,SAAS,QAAQ;gBACpB,aAAa;oBACX,UAAU,SAAS,QAAQ,EAAE,aAAa,YAAY;oBACtD,WAAW,SAAS,QAAQ,EAAE,aAAa,aAAa;gBAC1D;YACF;QACF;QAEA,QAAQ,GAAG,CAAC,4BAA4B;QAExC,IAAI,YAAY;YACd,yBAAyB,MAAM,CAAC;QAClC,OAAO;YACL,yBAAyB,MAAM,CAAC;QAClC;IACF;IAEA,MAAM,oBAAoB,CAAC,OAAe;QACxC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;IACH;IAEA,MAAM,0BAA0B,CAAC,QAAgB,OAAe;QAC9D,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,OAAO,EAAE;oBACR,GAAG,IAAI,CAAC,OAA4B;oBACpC,CAAC,MAAM,EAAE;gBACX;YACF,CAAC;IACH;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,mIAAA,CAAA,UAAa;;;;;8BACd,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACZ;2CAAI,MAAM;qCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;4CAAY,WAAU;2CAAb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQ1B;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,mIAAA,CAAA,UAAa;;;;;0BAEd,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DACX,aAAa,uBAAuB;;;;;;8DAEvC,8OAAC;oDAAE,WAAU;8DACV,aAAa,uCAAuC;;;;;;;;;;;;;;;;;;8CAK3D,8OAAC;oCAAI,WAAU;8CACZ,0BACC;;4CACG,4BACC,8OAAC;gDACC,SAAS,IAAM,aAAa;gDAC5B,WAAU;0DACX;;;;;;0DAIH,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gDACZ,YAAY;oDAAE,OAAO;gDAAK;gDAC1B,UAAU;oDAAE,OAAO;gDAAK;gDACxB,SAAS;gDACT,UAAU,yBAAyB,SAAS,IAAI,yBAAyB,SAAS;gDAClF,WAAU;;kEAEV,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDACd,yBAAyB,SAAS,IAAI,yBAAyB,SAAS,GACtE,cACA,aAAa,iBAAiB;;;;;;;;qEAKtC,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,SAAS,IAAM,aAAa;wCAC5B,WAAU;;0DAEV,8OAAC,2MAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;;;;;;wBAOnC,2BACC,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;kEAElB,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,8OAAC;gEAAE,WAAU;0EACV,WAAW,OAAO,EAAE,SAAS,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;;sDAMpD,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;;;;;;kEAEnB,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,8OAAC;gEAAE,WAAU;0EAAmC,WAAW,WAAW,IAAI;;;;;;;;;;;;;;;;;;;;;;;sDAKhF,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;kEAExB,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,8OAAC;gEAAE,WAAU;0EACV,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,YAAY,IAAI;;;;;;;;;;;;;;;;;;;;;;;sDAMnD,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;kEAExB,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,8OAAC;gEAAE,WAAW,CAAC,oBAAoB,EACjC,WAAW,MAAM,KAAK,aAAa,mBAAmB,mBACtD;0EACC,WAAW,MAAM,EAAE,OAAO,GAAG,gBAAgB,WAAW,MAAM,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQjF,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDAEzD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAA+C;;;;;;wDAG/D,0BACC,8OAAC;4DACC,MAAK;4DACL,OAAO,SAAS,IAAI;4DACpB,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;4DACzD,WAAU;;;;;qHAGZ,8OAAC;4DAAE,WAAU;sEAAiB,WAAW,IAAI;;;;;;;;;;;;8DAIjD,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAA+C;;;;;;wDAG/D,0BACC,8OAAC;4DACC,MAAK;4DACL,OAAO,SAAS,KAAK;4DACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;4DAC1D,WAAU;;;;;qHAGZ,8OAAC;4DAAE,WAAU;sEAAiB,WAAW,KAAK;;;;;;;;;;;;8DAIlD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAM,WAAU;sEAA+C;;;;;;wDAG/D,0BACC,8OAAC;4DACC,OAAO,SAAS,WAAW;4DAC3B,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;4DAChE,MAAM;4DACN,WAAU;;;;;qHAGZ,8OAAC;4DAAE,WAAU;sEAAiB,WAAW,WAAW;;;;;;;;;;;;;;;;;;;;;;;;8CAO5D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDAEzD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAA+C;;;;;;wDAG/D,0BACC,8OAAC;4DACC,MAAK;4DACL,OAAO,SAAS,QAAQ,EAAE,SAAS,UAAU;4DAC7C,UAAU,CAAC,IAAM,wBAAwB,YAAY,WAAW;oEAC9D,GAAG,SAAS,QAAQ,EAAE,OAAO;oEAC7B,QAAQ,EAAE,MAAM,CAAC,KAAK;gEACxB;4DACA,WAAU;;;;;qHAGZ,8OAAC;4DAAE,WAAU;sEAAiB,WAAW,QAAQ,EAAE,SAAS;;;;;;;;;;;;8DAIhE,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAA+C;;;;;;wDAG/D,0BACC,8OAAC;4DACC,MAAK;4DACL,OAAO,SAAS,QAAQ,EAAE,SAAS,QAAQ;4DAC3C,UAAU,CAAC,IAAM,wBAAwB,YAAY,WAAW;oEAC9D,GAAG,SAAS,QAAQ,EAAE,OAAO;oEAC7B,MAAM,EAAE,MAAM,CAAC,KAAK;gEACtB;4DACA,WAAU;;;;;qHAGZ,8OAAC;4DAAE,WAAU;sEAAiB,WAAW,QAAQ,EAAE,SAAS;;;;;;;;;;;;;;;;;;;;;;;;8CAOpE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDAEzD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAA+C;;;;;;wDAG/D,0BACC,8OAAC;4DACC,MAAK;4DACL,OAAO,SAAS,OAAO,EAAE,eAAe;4DACxC,UAAU,CAAC,IAAM,wBAAwB,WAAW,eAAe,OAAO,EAAE,MAAM,CAAC,KAAK;4DACxF,WAAU;;;;;qHAGZ,8OAAC;4DAAE,WAAU;sEAAiB,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,OAAO,EAAE,eAAe;;;;;;;;;;;;8DAIpF,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAA+C;;;;;;wDAG/D,0BACC,8OAAC;4DACC,MAAK;4DACL,OAAO,SAAS,OAAO,EAAE,gBAAgB;4DACzC,UAAU,CAAC,IAAM,wBAAwB,WAAW,gBAAgB,OAAO,EAAE,MAAM,CAAC,KAAK;4DACzF,WAAU;;;;;qHAGZ,8OAAC;4DAAE,WAAU;sEAAiB,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,OAAO,EAAE,gBAAgB;;;;;;;;;;;;8DAIrF,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAA+C;;;;;;wDAG/D,0BACC,8OAAC;4DACC,MAAK;4DACL,OAAO,SAAS,OAAO,EAAE,gBAAgB;4DACzC,UAAU,CAAC,IAAM,wBAAwB,WAAW,gBAAgB,OAAO,EAAE,MAAM,CAAC,KAAK;4DACzF,WAAU;;;;;qHAGZ,8OAAC;4DAAE,WAAU;sEAAiB,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,OAAO,EAAE,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;qFAO3F,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;8CAEzD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,MAAK;oDACL,OAAO,SAAS,IAAI;oDACpB,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;oDACzD,aAAY;oDACZ,WAAU;oDACV,QAAQ;;;;;;;;;;;;sDAIZ,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,MAAK;oDACL,OAAO,SAAS,KAAK;oDACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;oDAC1D,aAAY;oDACZ,WAAU;oDACV,QAAQ;;;;;;;;;;;;sDAIZ,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,OAAO,SAAS,WAAW;oDAC3B,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;oDAChE,aAAY;oDACZ,MAAM;oDACN,WAAU;oDACV,QAAQ;;;;;;;;;;;;sDAIZ,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,MAAK;oDACL,OAAO,SAAS,KAAK;oDACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;oDAC1D,aAAY;oDACZ,WAAU;;;;;;;;;;;;sDAId,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,MAAK;oDACL,OAAO,MAAM,OAAO,CAAC,SAAS,OAAO,IAAI,SAAS,OAAO,CAAC,IAAI,CAAC,QAAQ;oDACvE,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,IAAI,MAAM,CAAC,CAAA,IAAK;oDACvG,aAAY;oDACZ,WAAU;;;;;;;;;;;;sDAId,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,MAAK;4DACL,OAAO,SAAS,QAAQ,EAAE,SAAS,UAAU;4DAC7C,UAAU,CAAC;gEACT,MAAM,cAAc;oEAClB,GAAG,SAAS,QAAQ;oEACpB,SAAS;wEACP,GAAG,SAAS,QAAQ,EAAE,OAAO;wEAC7B,QAAQ,EAAE,MAAM,CAAC,KAAK;oEACxB;gEACF;gEACA,kBAAkB,YAAY;4DAChC;4DACA,aAAY;4DACZ,WAAU;4DACV,QAAQ;;;;;;sEAEV,8OAAC;4DACC,MAAK;4DACL,OAAO,SAAS,QAAQ,EAAE,SAAS,QAAQ;4DAC3C,UAAU,CAAC;gEACT,MAAM,cAAc;oEAClB,GAAG,SAAS,QAAQ;oEACpB,SAAS;wEACP,GAAG,SAAS,QAAQ,EAAE,OAAO;wEAC7B,MAAM,EAAE,MAAM,CAAC,KAAK;oEACtB;gEACF;gEACA,kBAAkB,YAAY;4DAChC;4DACA,aAAY;4DACZ,WAAU;4DACV,QAAQ;;;;;;sEAEV,8OAAC;4DACC,MAAK;4DACL,OAAO,SAAS,QAAQ,EAAE,SAAS,SAAS;4DAC5C,UAAU,CAAC;gEACT,MAAM,cAAc;oEAClB,GAAG,SAAS,QAAQ;oEACpB,SAAS;wEACP,GAAG,SAAS,QAAQ,EAAE,OAAO;wEAC7B,OAAO,EAAE,MAAM,CAAC,KAAK;oEACvB;gEACF;gEACA,kBAAkB,YAAY;4DAChC;4DACA,aAAY;4DACZ,WAAU;4DACV,QAAQ;;;;;;sEAEV,8OAAC;4DACC,MAAK;4DACL,OAAO,SAAS,QAAQ,EAAE,SAAS,WAAW;4DAC9C,UAAU,CAAC;gEACT,MAAM,cAAc;oEAClB,GAAG,SAAS,QAAQ;oEACpB,SAAS;wEACP,GAAG,SAAS,QAAQ,EAAE,OAAO;wEAC7B,SAAS,EAAE,MAAM,CAAC,KAAK;oEACzB;gEACF;gEACA,kBAAkB,YAAY;4DAChC;4DACA,aAAY;4DACZ,WAAU;4DACV,QAAQ;;;;;;;;;;;;;;;;;;sDAKd,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,MAAK;4DACL,OAAO,SAAS,QAAQ,EAAE,aAAa,YAAY;4DACnD,UAAU,CAAC;gEACT,MAAM,cAAc;oEAClB,GAAG,SAAS,QAAQ;oEACpB,aAAa;wEACX,GAAG,SAAS,QAAQ,EAAE,WAAW;wEACjC,UAAU,OAAO,EAAE,MAAM,CAAC,KAAK;oEACjC;gEACF;gEACA,kBAAkB,YAAY;4DAChC;4DACA,aAAY;4DACZ,MAAK;4DACL,WAAU;;;;;;sEAEZ,8OAAC;4DACC,MAAK;4DACL,OAAO,SAAS,QAAQ,EAAE,aAAa,aAAa;4DACpD,UAAU,CAAC;gEACT,MAAM,cAAc;oEAClB,GAAG,SAAS,QAAQ;oEACpB,aAAa;wEACX,GAAG,SAAS,QAAQ,EAAE,WAAW;wEACjC,WAAW,OAAO,EAAE,MAAM,CAAC,KAAK;oEAClC;gEACF;gEACA,kBAAkB,YAAY;4DAChC;4DACA,aAAY;4DACZ,MAAK;4DACL,WAAU;;;;;;;;;;;;8DAGd,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;sDAK5C,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,MAAK;oDACL,OAAO,SAAS,OAAO,EAAE,eAAe;oDACxC,UAAU,CAAC,IAAM,wBAAwB,WAAW,eAAe,OAAO,EAAE,MAAM,CAAC,KAAK;oDACxF,aAAY;oDACZ,MAAK;oDACL,KAAI;oDACJ,WAAU;;;;;;;;;;;;sDAId,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,MAAK;oDACL,OAAO,SAAS,OAAO,EAAE,gBAAgB;oDACzC,UAAU,CAAC,IAAM,wBAAwB,WAAW,gBAAgB,OAAO,EAAE,MAAM,CAAC,KAAK;oDACzF,aAAY;oDACZ,MAAK;oDACL,KAAI;oDACJ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU9B;uCAEe", "debugId": null}}]}