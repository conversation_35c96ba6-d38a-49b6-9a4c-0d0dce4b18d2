'use client';

import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { vendorAPI, restaurantAPI } from '@/lib/api';
import useAuthStore from '@/store/useAuthStore';

const DebugPage = () => {
  const { user, isAuthenticated } = useAuthStore();

  // Test vendor restaurant endpoint
  const { data: restaurantData, isLoading: restaurantLoading, error: restaurantError } = useQuery({
    queryKey: ['debug-vendor-restaurant'],
    queryFn: () => vendorAPI.getRestaurant(),
    enabled: isAuthenticated && user?.role === 'vendor',
  });

  // Test vendor foods endpoint
  const { data: foodsData, isLoading: foodsLoading, error: foodsError } = useQuery({
    queryKey: ['debug-vendor-foods'],
    queryFn: () => vendorAPI.getFoods(),
    enabled: isAuthenticated && user?.role === 'vendor',
  });

  // Test public restaurants endpoint
  const { data: publicRestaurantsData, isLoading: publicLoading, error: publicError } = useQuery({
    queryKey: ['debug-public-restaurants'],
    queryFn: () => restaurantAPI.getAll({ page: 1, limit: 5 }),
  });

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Debug Information</h1>

        {/* User Info */}
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">User Information</h2>
          <div className="space-y-2">
            <p><strong>Authenticated:</strong> {isAuthenticated ? 'Yes' : 'No'}</p>
            <p><strong>Role:</strong> {user?.role || 'None'}</p>
            <p><strong>Name:</strong> {user?.name || 'N/A'}</p>
            <p><strong>Email:</strong> {user?.email || 'N/A'}</p>
          </div>
        </div>

        {/* Vendor Restaurant Data */}
        {user?.role === 'vendor' && (
          <div className="bg-white rounded-lg shadow p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4">Vendor Restaurant Data</h2>
            <div className="space-y-2">
              <p><strong>Loading:</strong> {restaurantLoading ? 'Yes' : 'No'}</p>
              <p><strong>Error:</strong> {restaurantError ? JSON.stringify(restaurantError.response?.data || restaurantError.message) : 'None'}</p>
              <div>
                <strong>Raw Data:</strong>
                <pre className="mt-2 p-4 bg-gray-100 rounded text-sm overflow-auto">
                  {JSON.stringify(restaurantData, null, 2)}
                </pre>
              </div>
              {restaurantData?.data?.data && (
                <div>
                  <strong>Restaurant Object:</strong>
                  <pre className="mt-2 p-4 bg-gray-100 rounded text-sm overflow-auto">
                    {JSON.stringify(restaurantData.data.data, null, 2)}
                  </pre>
                  <div className="mt-4 space-y-1">
                    <p><strong>Restaurant ID:</strong> {restaurantData.data.data.id || 'undefined'}</p>
                    <p><strong>Restaurant _id:</strong> {restaurantData.data.data._id || 'undefined'}</p>
                    <p><strong>Restaurant Name:</strong> {restaurantData.data.data.name || 'undefined'}</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Vendor Foods Data */}
        {user?.role === 'vendor' && (
          <div className="bg-white rounded-lg shadow p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4">Vendor Foods Data</h2>
            <div className="space-y-2">
              <p><strong>Loading:</strong> {foodsLoading ? 'Yes' : 'No'}</p>
              <p><strong>Error:</strong> {foodsError ? JSON.stringify(foodsError.response?.data || foodsError.message) : 'None'}</p>
              <div>
                <strong>Raw Data:</strong>
                <pre className="mt-2 p-4 bg-gray-100 rounded text-sm overflow-auto max-h-64">
                  {JSON.stringify(foodsData, null, 2)}
                </pre>
              </div>
              {foodsData?.data?.data && (
                <div>
                  <p><strong>Foods Count:</strong> {Array.isArray(foodsData.data.data) ? foodsData.data.data.length : 'Not an array'}</p>
                  <p><strong>Is Array:</strong> {Array.isArray(foodsData.data.data) ? 'Yes' : 'No'}</p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Public Restaurants Data */}
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Public Restaurants Data</h2>
          <div className="space-y-2">
            <p><strong>Loading:</strong> {publicLoading ? 'Yes' : 'No'}</p>
            <p><strong>Error:</strong> {publicError ? JSON.stringify(publicError.response?.data || publicError.message) : 'None'}</p>
            <div>
              <strong>Raw Data:</strong>
              <pre className="mt-2 p-4 bg-gray-100 rounded text-sm overflow-auto max-h-64">
                {JSON.stringify(publicRestaurantsData, null, 2)}
              </pre>
            </div>
            {publicRestaurantsData?.data?.data && (
              <div>
                <p><strong>Restaurants Count:</strong> {Array.isArray(publicRestaurantsData.data.data) ? publicRestaurantsData.data.data.length : 'Not an array'}</p>
                <p><strong>Is Array:</strong> {Array.isArray(publicRestaurantsData.data.data) ? 'Yes' : 'No'}</p>
                {Array.isArray(publicRestaurantsData.data.data) && publicRestaurantsData.data.data.length > 0 && (
                  <div>
                    <strong>First Restaurant:</strong>
                    <pre className="mt-2 p-4 bg-gray-100 rounded text-sm overflow-auto">
                      {JSON.stringify(publicRestaurantsData.data.data[0], null, 2)}
                    </pre>
                    <div className="mt-2 space-y-1">
                      <p><strong>First Restaurant ID:</strong> {publicRestaurantsData.data.data[0].id || 'undefined'}</p>
                      <p><strong>First Restaurant _id:</strong> {publicRestaurantsData.data.data[0]._id || 'undefined'}</p>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Quick Actions</h2>
          <div className="space-y-4">
            <div className="flex gap-4">
              <a 
                href="/" 
                className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
              >
                Go to Home
              </a>
              <a 
                href="/restaurants" 
                className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
              >
                Go to Restaurants
              </a>
              {user?.role === 'vendor' && (
                <>
                  <a 
                    href="/vendor/dashboard" 
                    className="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600"
                  >
                    Vendor Dashboard
                  </a>
                  <a 
                    href="/vendor/menu" 
                    className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600"
                  >
                    Vendor Menu
                  </a>
                </>
              )}
            </div>
            
            {/* Test Restaurant Link */}
            {restaurantData?.data?.data && (
              <div className="mt-4">
                <p className="mb-2"><strong>Test Restaurant Links:</strong></p>
                <div className="flex gap-2">
                  <a
                    href={`/restaurants/${restaurantData.data.data.id}`}
                    className="px-3 py-1 bg-red-500 text-white rounded text-sm hover:bg-red-600"
                  >
                    Using .id: {restaurantData.data.data.id || 'undefined'}
                  </a>
                  <a
                    href={`/restaurants/${restaurantData.data.data._id}`}
                    className="px-3 py-1 bg-red-500 text-white rounded text-sm hover:bg-red-600"
                  >
                    Using ._id: {restaurantData.data.data._id || 'undefined'}
                  </a>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DebugPage;
