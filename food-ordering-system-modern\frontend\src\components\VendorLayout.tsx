'use client';

import React from 'react';
import VendorSidebar from '@/components/VendorSidebar';

interface VendorLayoutProps {
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
  headerActions?: React.ReactNode;
}

const VendorLayout: React.FC<VendorLayoutProps> = ({ 
  children, 
  title, 
  subtitle, 
  headerActions 
}) => {
  return (
    <div className="min-h-screen bg-gray-50 flex">
      <VendorSidebar />
      
      <div className="flex-1 p-8">
        <div className="max-w-7xl mx-auto">
          {(title || subtitle || headerActions) && (
            <div className="flex items-center justify-between mb-8">
              <div>
                {title && (
                  <h1 className="text-3xl font-bold text-gray-900 mb-2">{title}</h1>
                )}
                {subtitle && (
                  <p className="text-gray-600">{subtitle}</p>
                )}
              </div>
              {headerActions && (
                <div className="flex items-center gap-4">
                  {headerActions}
                </div>
              )}
            </div>
          )}
          
          {children}
        </div>
      </div>
    </div>
  );
};

export default VendorLayout;
