const express = require('express');
const { protect, authorize } = require('../middleware/auth');
const Restaurant = require('../models/Restaurant');
const Order = require('../models/Order');
const Food = require('../models/Food');

const router = express.Router();

// Test route to check if vendor routes are working
router.get('/test', (req, res) => {
  res.json({ message: 'Vendor routes are working!' });
});

// @desc    Get vendor restaurant
// @route   GET /api/vendors/restaurant
// @access  Private (Vendor)
router.get('/restaurant', protect, authorize('vendor'), async (req, res, next) => {
  try {
    const restaurant = await Restaurant.findOne({ owner: req.user._id });
    
    if (!restaurant) {
      return res.status(404).json({
        success: false,
        message: 'No restaurant found for this vendor'
      });
    }

    res.json({
      success: true,
      data: restaurant
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Create vendor restaurant
// @route   POST /api/vendors/restaurant
// @access  Private (Vendor)
router.post('/restaurant', protect, authorize('vendor'), async (req, res, next) => {
  try {
    // Check if vendor already has a restaurant
    const existingRestaurant = await Restaurant.findOne({ owner: req.user._id });
    
    if (existingRestaurant) {
      return res.status(400).json({
        success: false,
        message: 'You already have a restaurant registered'
      });
    }

    // Create restaurant with vendor as owner
    const restaurantData = {
      ...req.body,
      owner: req.user._id,
      status: 'pending' // Default status for new restaurants
    };

    const restaurant = await Restaurant.create(restaurantData);

    res.status(201).json({
      success: true,
      data: restaurant,
      message: 'Restaurant created successfully'
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Update vendor restaurant
// @route   PUT /api/vendors/restaurant
// @access  Private (Vendor)
router.put('/restaurant', protect, authorize('vendor'), async (req, res, next) => {
  try {
    const restaurant = await Restaurant.findOneAndUpdate(
      { owner: req.user._id },
      req.body,
      { new: true, runValidators: true }
    );

    if (!restaurant) {
      return res.status(404).json({
        success: false,
        message: 'No restaurant found for this vendor'
      });
    }

    res.json({
      success: true,
      data: restaurant,
      message: 'Restaurant updated successfully'
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get vendor foods
// @route   GET /api/vendors/foods
// @access  Private (Vendor)
router.get('/foods', protect, authorize('vendor'), async (req, res, next) => {
  try {
    const restaurant = await Restaurant.findOne({ owner: req.user._id });
    
    if (!restaurant) {
      return res.status(404).json({
        success: false,
        message: 'No restaurant found for this vendor'
      });
    }

    const foods = await Food.find({ restaurant: restaurant._id });

    res.json({
      success: true,
      data: foods
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Create vendor food
// @route   POST /api/vendors/foods
// @access  Private (Vendor)
router.post('/foods', protect, authorize('vendor'), async (req, res, next) => {
  try {
    const restaurant = await Restaurant.findOne({ owner: req.user._id });
    
    if (!restaurant) {
      return res.status(404).json({
        success: false,
        message: 'No restaurant found for this vendor'
      });
    }

    const foodData = {
      ...req.body,
      restaurant: restaurant._id
    };

    const food = await Food.create(foodData);

    res.status(201).json({
      success: true,
      data: food,
      message: 'Food item created successfully'
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Update vendor food
// @route   PUT /api/vendors/foods/:id
// @access  Private (Vendor)
router.put('/foods/:id', protect, authorize('vendor'), async (req, res, next) => {
  try {
    const restaurant = await Restaurant.findOne({ owner: req.user._id });
    
    if (!restaurant) {
      return res.status(404).json({
        success: false,
        message: 'No restaurant found for this vendor'
      });
    }

    const food = await Food.findOneAndUpdate(
      { _id: req.params.id, restaurant: restaurant._id },
      req.body,
      { new: true, runValidators: true }
    );

    if (!food) {
      return res.status(404).json({
        success: false,
        message: 'Food item not found'
      });
    }

    res.json({
      success: true,
      data: food,
      message: 'Food item updated successfully'
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Delete vendor food
// @route   DELETE /api/vendors/foods/:id
// @access  Private (Vendor)
router.delete('/foods/:id', protect, authorize('vendor'), async (req, res, next) => {
  try {
    const restaurant = await Restaurant.findOne({ owner: req.user._id });
    
    if (!restaurant) {
      return res.status(404).json({
        success: false,
        message: 'No restaurant found for this vendor'
      });
    }

    const food = await Food.findOneAndDelete({
      _id: req.params.id,
      restaurant: restaurant._id
    });

    if (!food) {
      return res.status(404).json({
        success: false,
        message: 'Food item not found'
      });
    }

    res.json({
      success: true,
      message: 'Food item deleted successfully'
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get vendor orders
// @route   GET /api/vendors/orders
// @access  Private (Vendor)
router.get('/orders', protect, authorize('vendor'), async (req, res, next) => {
  try {
    const restaurant = await Restaurant.findOne({ owner: req.user._id });
    
    if (!restaurant) {
      return res.status(404).json({
        success: false,
        message: 'No restaurant found for this vendor'
      });
    }

    const { status, page = 1, limit = 10 } = req.query;
    const query = { restaurant: restaurant._id };
    
    if (status) {
      query.status = status;
    }

    const orders = await Order.find(query)
      .populate('customer', 'name email phone')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await Order.countDocuments(query);

    res.json({
      success: true,
      data: orders,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Update order status
// @route   PUT /api/vendors/orders/:id/status
// @access  Private (Vendor)
router.put('/orders/:id/status', protect, authorize('vendor'), async (req, res, next) => {
  try {
    const restaurant = await Restaurant.findOne({ owner: req.user._id });

    if (!restaurant) {
      return res.status(404).json({
        success: false,
        message: 'No restaurant found for this vendor'
      });
    }

    const { status } = req.body;

    const order = await Order.findOneAndUpdate(
      { _id: req.params.id, restaurant: restaurant._id },
      { status },
      { new: true }
    ).populate('customer', 'name email phone');

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    res.json({
      success: true,
      data: order,
      message: 'Order status updated successfully'
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get recent orders
// @route   GET /api/vendors/orders/recent
// @access  Private (Vendor)
router.get('/orders/recent', protect, authorize('vendor'), async (req, res, next) => {
  try {
    const restaurant = await Restaurant.findOne({ owner: req.user._id });

    if (!restaurant) {
      return res.status(404).json({
        success: false,
        message: 'No restaurant found for this vendor'
      });
    }

    const { limit = 5 } = req.query;

    const orders = await Order.find({ restaurant: restaurant._id })
      .populate('customer', 'name email')
      .sort({ createdAt: -1 })
      .limit(parseInt(limit));

    res.json({
      success: true,
      data: orders
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get vendor analytics
// @route   GET /api/vendors/analytics
// @access  Private (Vendor)
router.get('/analytics', protect, authorize('vendor'), async (req, res, next) => {
  try {
    const restaurant = await Restaurant.findOne({ owner: req.user._id });

    if (!restaurant) {
      return res.status(404).json({
        success: false,
        message: 'No restaurant found for this vendor'
      });
    }

    const { period = '7d' } = req.query;
    let days = 7;

    if (period === '30d') days = 30;
    else if (period === '90d') days = 90;

    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // Get analytics data
    const totalOrders = await Order.countDocuments({ restaurant: restaurant._id });
    const totalRevenue = await Order.aggregate([
      {
        $match: {
          restaurant: restaurant._id,
          status: { $in: ['delivered', 'completed'] }
        }
      },
      {
        $group: {
          _id: null,
          total: { $sum: '$pricing.total' }
        }
      }
    ]);

    const periodOrders = await Order.countDocuments({
      restaurant: restaurant._id,
      createdAt: { $gte: startDate }
    });

    const periodRevenue = await Order.aggregate([
      {
        $match: {
          restaurant: restaurant._id,
          createdAt: { $gte: startDate },
          status: { $in: ['delivered', 'completed'] }
        }
      },
      {
        $group: {
          _id: null,
          total: { $sum: '$pricing.total' }
        }
      }
    ]);

    res.json({
      success: true,
      data: {
        totalOrders,
        totalRevenue: totalRevenue.length > 0 ? totalRevenue[0].total : 0,
        periodOrders,
        periodRevenue: periodRevenue.length > 0 ? periodRevenue[0].total : 0,
        period: `${days}d`
      }
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get vendor customers
// @route   GET /api/vendors/customers
// @access  Private (Vendor)
router.get('/customers', protect, authorize('vendor'), async (req, res, next) => {
  try {
    const restaurant = await Restaurant.findOne({ owner: req.user._id });

    if (!restaurant) {
      return res.status(404).json({
        success: false,
        message: 'No restaurant found for this vendor'
      });
    }

    // Get unique customers who ordered from this restaurant
    const customers = await Order.aggregate([
      {
        $match: { restaurant: restaurant._id }
      },
      {
        $group: {
          _id: '$customer',
          totalOrders: { $sum: 1 },
          totalSpent: { $sum: '$pricing.total' },
          lastOrder: { $max: '$createdAt' },
          averageOrderValue: { $avg: '$pricing.total' }
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: '_id',
          foreignField: '_id',
          as: 'customerInfo'
        }
      },
      {
        $unwind: '$customerInfo'
      },
      {
        $project: {
          _id: 1,
          name: '$customerInfo.name',
          email: '$customerInfo.email',
          phone: '$customerInfo.phone',
          totalOrders: 1,
          totalSpent: 1,
          lastOrder: 1,
          averageOrderValue: 1
        }
      },
      {
        $sort: { totalOrders: -1 }
      }
    ]);

    res.json({
      success: true,
      data: customers
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get vendor reviews
// @route   GET /api/vendors/reviews
// @access  Private (Vendor)
router.get('/reviews', protect, authorize('vendor'), async (req, res, next) => {
  try {
    const restaurant = await Restaurant.findOne({ owner: req.user._id });

    if (!restaurant) {
      return res.status(404).json({
        success: false,
        message: 'No restaurant found for this vendor'
      });
    }

    const { rating, page = 1, limit = 10 } = req.query;
    const query = { restaurant: restaurant._id };

    if (rating && rating !== 'all') {
      query.rating = parseInt(rating);
    }

    // For now, return mock data since Review model doesn't exist yet
    // This will be replaced with actual Review model queries
    const mockReviews = [
      {
        id: 1,
        customer: { name: 'John Doe', email: '<EMAIL>' },
        rating: 5,
        comment: 'Amazing food! The biryani was perfectly cooked and the delivery was super fast.',
        date: new Date().toISOString(),
        helpful: 12,
        orderItems: ['Chicken Biryani', 'Raita'],
        response: null
      },
      {
        id: 2,
        customer: { name: 'Sarah Smith', email: '<EMAIL>' },
        rating: 4,
        comment: 'Good taste but the portion size could be better. Overall satisfied with the service.',
        date: new Date(Date.now() - 86400000).toISOString(),
        helpful: 8,
        orderItems: ['Paneer Butter Masala', 'Naan'],
        response: 'Thank you for your feedback! We\'ll work on improving our portion sizes.'
      }
    ];

    res.json({
      success: true,
      data: mockReviews,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: mockReviews.length,
        pages: Math.ceil(mockReviews.length / limit)
      }
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get vendor notifications
// @route   GET /api/vendors/notifications
// @access  Private (Vendor)
router.get('/notifications', protect, authorize('vendor'), async (req, res, next) => {
  try {
    const restaurant = await Restaurant.findOne({ owner: req.user._id });

    if (!restaurant) {
      return res.status(404).json({
        success: false,
        message: 'No restaurant found for this vendor'
      });
    }

    // For now, return mock data since Notification model doesn't exist yet
    const mockNotifications = [
      {
        id: 1,
        type: 'order',
        title: 'New Order Received',
        message: `New order has been placed for ${restaurant.name}`,
        time: '2 minutes ago',
        read: false,
        createdAt: new Date()
      },
      {
        id: 2,
        type: 'review',
        title: 'New Review',
        message: 'A customer left a 5-star review for your restaurant',
        time: '15 minutes ago',
        read: false,
        createdAt: new Date(Date.now() - 900000)
      },
      {
        id: 3,
        type: 'payment',
        title: 'Payment Received',
        message: 'Payment has been credited to your account',
        time: '1 hour ago',
        read: true,
        createdAt: new Date(Date.now() - 3600000)
      }
    ];

    res.json({
      success: true,
      data: mockNotifications
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
