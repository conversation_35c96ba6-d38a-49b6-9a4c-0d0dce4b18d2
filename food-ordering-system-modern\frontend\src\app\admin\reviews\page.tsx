'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { useQuery } from '@tanstack/react-query';
import { 
  MessageCircle, 
  Star,
  Search,
  Filter,
  Eye,
  Flag,
  CheckCircle,
  XCircle,
  AlertTriangle,
  User,
  Calendar
} from 'lucide-react';
import AdminSidebar from '@/components/AdminSidebar';
import { adminAPI } from '@/lib/api';

const AdminReviews = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [ratingFilter, setRatingFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');

  // Mock reviews data (replace with real API)
  const mockReviews = [
    {
      id: 1,
      customer: { name: '<PERSON>', email: '<EMAIL>' },
      restaurant: { name: 'Spice Garden', id: 'rest1' },
      rating: 5,
      comment: 'Amazing food! The biryani was perfectly cooked and the delivery was super fast.',
      date: '2024-01-15T10:30:00Z',
      status: 'approved',
      flagged: false,
      helpful: 12,
      orderItems: ['Chicken Biryani', 'Raita'],
      response: null
    },
    {
      id: 2,
      customer: { name: '<PERSON>', email: '<EMAIL>' },
      restaurant: { name: 'Pizza Palace', id: 'rest2' },
      rating: 4,
      comment: 'Good taste but the portion size could be better. Overall satisfied with the service.',
      date: '2024-01-14T15:20:00Z',
      status: 'approved',
      flagged: false,
      helpful: 8,
      orderItems: ['Margherita Pizza', 'Garlic Bread'],
      response: 'Thank you for your feedback! We\'ll work on improving our portion sizes.'
    },
    {
      id: 3,
      customer: { name: 'Mike Johnson', email: '<EMAIL>' },
      restaurant: { name: 'Burger Hub', id: 'rest3' },
      rating: 1,
      comment: 'Terrible food quality and very rude staff. Would not recommend to anyone.',
      date: '2024-01-13T18:45:00Z',
      status: 'flagged',
      flagged: true,
      helpful: 2,
      orderItems: ['Cheese Burger', 'Fries'],
      response: null
    },
    {
      id: 4,
      customer: { name: 'Emily Davis', email: '<EMAIL>' },
      restaurant: { name: 'Sushi Master', id: 'rest4' },
      rating: 5,
      comment: 'Excellent quality and taste! Will definitely order again.',
      date: '2024-01-12T12:15:00Z',
      status: 'approved',
      flagged: false,
      helpful: 15,
      orderItems: ['California Roll', 'Miso Soup'],
      response: 'Thank you so much! We appreciate your support.'
    },
    {
      id: 5,
      customer: { name: 'David Wilson', email: '<EMAIL>' },
      restaurant: { name: 'Taco Bell', id: 'rest5' },
      rating: 2,
      comment: 'Food was cold when it arrived and the taste was not up to the mark.',
      date: '2024-01-11T20:30:00Z',
      status: 'pending',
      flagged: false,
      helpful: 3,
      orderItems: ['Chicken Tacos', 'Nachos'],
      response: null
    }
  ];

  const ratingFilters = [
    { value: 'all', label: 'All Ratings' },
    { value: '5', label: '5 Stars' },
    { value: '4', label: '4 Stars' },
    { value: '3', label: '3 Stars' },
    { value: '2', label: '2 Stars' },
    { value: '1', label: '1 Star' },
  ];

  const statusFilters = [
    { value: 'all', label: 'All Status' },
    { value: 'approved', label: 'Approved' },
    { value: 'pending', label: 'Pending' },
    { value: 'flagged', label: 'Flagged' },
    { value: 'rejected', label: 'Rejected' },
  ];

  const filteredReviews = mockReviews.filter(review => {
    const matchesSearch = review.comment.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         review.customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         review.restaurant.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesRating = ratingFilter === 'all' || review.rating.toString() === ratingFilter;
    const matchesStatus = statusFilter === 'all' || review.status === statusFilter;
    return matchesSearch && matchesRating && matchesStatus;
  });

  // Calculate stats
  const totalReviews = mockReviews.length;
  const averageRating = mockReviews.reduce((sum, review) => sum + review.rating, 0) / totalReviews;
  const flaggedReviews = mockReviews.filter(r => r.flagged).length;
  const pendingReviews = mockReviews.filter(r => r.status === 'pending').length;

  const stats = [
    {
      title: 'Total Reviews',
      value: totalReviews,
      icon: MessageCircle,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    },
    {
      title: 'Average Rating',
      value: averageRating.toFixed(1),
      icon: Star,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100'
    },
    {
      title: 'Flagged Reviews',
      value: flaggedReviews,
      icon: Flag,
      color: 'text-red-600',
      bgColor: 'bg-red-100'
    },
    {
      title: 'Pending Reviews',
      value: pendingReviews,
      icon: AlertTriangle,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100'
    },
  ];

  const renderStars = (rating) => {
    return [...Array(5)].map((_, index) => (
      <Star
        key={index}
        className={`w-4 h-4 ${
          index < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ));
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-orange-100 text-orange-800';
      case 'flagged':
        return 'bg-red-100 text-red-800';
      case 'rejected':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleApproveReview = (reviewId) => {
    console.log('Approve review:', reviewId);
    // Implement approve functionality
  };

  const handleRejectReview = (reviewId) => {
    console.log('Reject review:', reviewId);
    // Implement reject functionality
  };

  const handleFlagReview = (reviewId) => {
    console.log('Flag review:', reviewId);
    // Implement flag functionality
  };

  return (
    <div className="min-h-screen bg-gray-50 flex">
      <AdminSidebar />
      
      <div className="flex-1 p-8">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <MessageCircle className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Review Management</h1>
                <p className="text-gray-600">Monitor and moderate customer reviews</p>
              </div>
            </div>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {stats.map((stat, index) => (
              <motion.div
                key={stat.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-white rounded-2xl p-6 shadow-md hover:shadow-lg transition-shadow duration-200"
              >
                <div className="flex items-center justify-between mb-4">
                  <div className={`w-12 h-12 ${stat.bgColor} rounded-lg flex items-center justify-center`}>
                    <stat.icon className={`w-6 h-6 ${stat.color}`} />
                  </div>
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-1">
                    {stat.title === 'Average Rating' ? stat.value : stat.value.toLocaleString()}
                  </h3>
                  <p className="text-gray-600 text-sm">{stat.title}</p>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Filters */}
          <div className="bg-white rounded-2xl p-6 shadow-md mb-6">
            <div className="flex flex-col md:flex-row gap-4">
              {/* Search */}
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    type="text"
                    placeholder="Search reviews, customers, or restaurants..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              {/* Rating Filter */}
              <div className="flex items-center gap-2">
                <Filter className="w-5 h-5 text-gray-400" />
                <select
                  value={ratingFilter}
                  onChange={(e) => setRatingFilter(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {ratingFilters.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Status Filter */}
              <div>
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {statusFilters.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Reviews List */}
          <div className="space-y-6">
            {filteredReviews.map((review, index) => (
              <motion.div
                key={review.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className={`bg-white rounded-2xl p-6 shadow-md ${
                  review.flagged ? 'border-l-4 border-red-500' : ''
                }`}
              >
                {/* Review Header */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                      <User className="w-6 h-6 text-gray-500" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900">{review.customer.name}</h4>
                      <p className="text-sm text-gray-500">{review.customer.email}</p>
                      <div className="flex items-center gap-2 mt-1">
                        <div className="flex">{renderStars(review.rating)}</div>
                        <span className="text-sm text-gray-500">•</span>
                        <span className="text-sm text-gray-500">{formatDate(review.date)}</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(review.status)}`}>
                      {review.status.charAt(0).toUpperCase() + review.status.slice(1)}
                    </span>
                    {review.flagged && (
                      <Flag className="w-4 h-4 text-red-500" />
                    )}
                  </div>
                </div>

                {/* Restaurant Info */}
                <div className="mb-4">
                  <p className="text-sm text-gray-600">
                    <strong>Restaurant:</strong> {review.restaurant.name}
                  </p>
                  <p className="text-sm text-gray-600">
                    <strong>Items:</strong> {review.orderItems.join(', ')}
                  </p>
                </div>

                {/* Review Content */}
                <p className="text-gray-700 mb-4">{review.comment}</p>

                {/* Response */}
                {review.response && (
                  <div className="bg-blue-50 border-l-4 border-blue-500 p-4 mb-4">
                    <p className="text-sm font-medium text-blue-800 mb-1">Restaurant Response:</p>
                    <p className="text-blue-700">{review.response}</p>
                  </div>
                )}

                {/* Actions */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <span className="text-sm text-gray-500">{review.helpful} people found this helpful</span>
                  </div>
                  <div className="flex items-center gap-2">
                    {review.status === 'pending' && (
                      <>
                        <button
                          onClick={() => handleApproveReview(review.id)}
                          className="flex items-center gap-1 px-3 py-1 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors text-sm"
                        >
                          <CheckCircle className="w-4 h-4" />
                          Approve
                        </button>
                        <button
                          onClick={() => handleRejectReview(review.id)}
                          className="flex items-center gap-1 px-3 py-1 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors text-sm"
                        >
                          <XCircle className="w-4 h-4" />
                          Reject
                        </button>
                      </>
                    )}
                    {!review.flagged && review.status !== 'flagged' && (
                      <button
                        onClick={() => handleFlagReview(review.id)}
                        className="flex items-center gap-1 px-3 py-1 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors text-sm"
                      >
                        <Flag className="w-4 h-4" />
                        Flag
                      </button>
                    )}
                    <button className="flex items-center gap-1 px-3 py-1 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors text-sm">
                      <Eye className="w-4 h-4" />
                      View Details
                    </button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {filteredReviews.length === 0 && (
            <div className="text-center py-12">
              <MessageCircle className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No reviews found</h3>
              <p className="text-gray-600">Try adjusting your search or filter criteria.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AdminReviews;
