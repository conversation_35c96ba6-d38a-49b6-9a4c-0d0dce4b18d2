'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useQuery } from '@tanstack/react-query';
import { 
  CheckCircle,
  Clock,
  Package,
  Truck,
  MapPin,
  Phone
} from 'lucide-react';
import { orderAPI } from '@/lib/api';

interface RealTimeOrderStatusProps {
  orderId: string;
  onStatusChange?: (status: string) => void;
}

const RealTimeOrderStatus: React.FC<RealTimeOrderStatusProps> = ({ 
  orderId, 
  onStatusChange 
}) => {
  const [previousStatus, setPreviousStatus] = useState<string>('');

  // Fetch order status with real-time updates
  const { data: order, isLoading } = useQuery({
    queryKey: ['order-status', orderId],
    queryFn: () => orderAPI.getOrder(orderId),
    refetchInterval: 15000, // Refetch every 15 seconds
    enabled: !!orderId,
  });

  const orderData = order?.data;

  // Handle status changes
  useEffect(() => {
    if (orderData?.status && orderData.status !== previousStatus) {
      setPreviousStatus(orderData.status);
      onStatusChange?.(orderData.status);
    }
  }, [orderData?.status, previousStatus, onStatusChange]);

  const statusSteps = [
    {
      key: 'confirmed',
      label: 'Order Confirmed',
      icon: CheckCircle,
      description: 'Your order has been confirmed'
    },
    {
      key: 'preparing',
      label: 'Preparing',
      icon: Package,
      description: 'Restaurant is preparing your food'
    },
    {
      key: 'ready',
      label: 'Ready for Pickup',
      icon: Clock,
      description: 'Food is ready for delivery'
    },
    {
      key: 'picked_up',
      label: 'Out for Delivery',
      icon: Truck,
      description: 'Delivery partner is on the way'
    },
    {
      key: 'delivered',
      label: 'Delivered',
      icon: MapPin,
      description: 'Order delivered successfully'
    }
  ];

  const getCurrentStepIndex = (status: string) => {
    const statusMap: { [key: string]: number } = {
      'pending': 0,
      'confirmed': 0,
      'preparing': 1,
      'ready': 2,
      'picked_up': 3,
      'out_for_delivery': 3,
      'delivered': 4,
      'completed': 4
    };
    return statusMap[status] || 0;
  };

  const currentStepIndex = orderData ? getCurrentStepIndex(orderData.status) : 0;

  const getEstimatedTime = () => {
    if (!orderData) return '';
    
    const orderTime = new Date(orderData.createdAt);
    const estimatedDeliveryTime = new Date(orderTime.getTime() + 45 * 60000); // 45 minutes
    
    return estimatedDeliveryTime.toLocaleTimeString('en-IN', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (isLoading) {
    return (
      <div className="bg-white rounded-2xl p-6 shadow-md">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex items-center gap-4">
                <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!orderData) {
    return (
      <div className="bg-white rounded-2xl p-6 shadow-md text-center">
        <Package className="w-12 h-12 text-gray-300 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Order Not Found</h3>
        <p className="text-gray-600">Unable to load order status</p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-2xl p-6 shadow-md">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Order Status</h3>
          <p className="text-gray-600">Order #{orderData.orderNumber}</p>
        </div>
        <div className="text-right">
          <p className="text-sm text-gray-600">Estimated delivery</p>
          <p className="font-semibold text-gray-900">{getEstimatedTime()}</p>
        </div>
      </div>

      {/* Status Steps */}
      <div className="space-y-6">
        {statusSteps.map((step, index) => {
          const isCompleted = index <= currentStepIndex;
          const isCurrent = index === currentStepIndex;
          const isNext = index === currentStepIndex + 1;

          return (
            <motion.div
              key={step.key}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="flex items-start gap-4"
            >
              {/* Step Icon */}
              <div className="relative">
                <motion.div
                  className={`w-10 h-10 rounded-full flex items-center justify-center ${
                    isCompleted
                      ? 'bg-green-100 text-green-600'
                      : isCurrent
                      ? 'bg-blue-100 text-blue-600'
                      : 'bg-gray-100 text-gray-400'
                  }`}
                  animate={isCurrent ? { scale: [1, 1.1, 1] } : {}}
                  transition={{ duration: 2, repeat: Infinity }}
                >
                  <step.icon className="w-5 h-5" />
                </motion.div>
                
                {/* Connecting Line */}
                {index < statusSteps.length - 1 && (
                  <div
                    className={`absolute top-10 left-1/2 transform -translate-x-1/2 w-0.5 h-6 ${
                      isCompleted ? 'bg-green-300' : 'bg-gray-200'
                    }`}
                  />
                )}
              </div>

              {/* Step Content */}
              <div className="flex-1 pb-6">
                <div className="flex items-center gap-2 mb-1">
                  <h4 className={`font-medium ${
                    isCompleted ? 'text-gray-900' : isCurrent ? 'text-blue-600' : 'text-gray-500'
                  }`}>
                    {step.label}
                  </h4>
                  {isCurrent && (
                    <motion.span
                      initial={{ opacity: 0, scale: 0 }}
                      animate={{ opacity: 1, scale: 1 }}
                      className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full font-medium"
                    >
                      Current
                    </motion.span>
                  )}
                  {isNext && orderData.status !== 'delivered' && (
                    <span className="px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded-full font-medium">
                      Next
                    </span>
                  )}
                </div>
                <p className={`text-sm ${
                  isCompleted ? 'text-gray-600' : 'text-gray-400'
                }`}>
                  {step.description}
                </p>
                {isCompleted && (
                  <p className="text-xs text-green-600 mt-1">
                    ✓ Completed
                  </p>
                )}
              </div>
            </motion.div>
          );
        })}
      </div>

      {/* Delivery Partner Info */}
      {orderData.deliveryPartner && orderData.status === 'picked_up' && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200"
        >
          <h4 className="font-medium text-blue-900 mb-2">Delivery Partner</h4>
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium text-blue-800">{orderData.deliveryPartner.name}</p>
              <p className="text-sm text-blue-600">On the way to you</p>
            </div>
            <a
              href={`tel:${orderData.deliveryPartner.phone}`}
              className="flex items-center gap-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Phone className="w-4 h-4" />
              Call
            </a>
          </div>
        </motion.div>
      )}

      {/* Live Updates Indicator */}
      <div className="mt-6 flex items-center justify-center gap-2 text-sm text-gray-500">
        <motion.div
          className="w-2 h-2 bg-green-500 rounded-full"
          animate={{ opacity: [1, 0.5, 1] }}
          transition={{ duration: 2, repeat: Infinity }}
        />
        Live updates every 15 seconds
      </div>
    </div>
  );
};

export default RealTimeOrderStatus;
