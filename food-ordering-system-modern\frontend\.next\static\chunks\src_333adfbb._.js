(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/lib/api.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "adminAPI": ()=>adminAPI,
    "authAPI": ()=>authAPI,
    "default": ()=>__TURBOPACK__default__export__,
    "deliveryAPI": ()=>deliveryAPI,
    "foodAPI": ()=>foodAPI,
    "notificationsAPI": ()=>notificationsAPI,
    "offersAPI": ()=>offersAPI,
    "orderAPI": ()=>orderAPI,
    "paymentAPI": ()=>paymentAPI,
    "restaurantAPI": ()=>restaurantAPI,
    "userAPI": ()=>userAPI,
    "vendorAPI": ()=>vendorAPI
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = /*#__PURE__*/ __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hot-toast/dist/index.mjs [app-client] (ecmascript)");
;
;
// Create axios instance
const api = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].create({
    baseURL: ("TURBOPACK compile-time value", "http://localhost:5000/api") || 'http://localhost:5000/api',
    timeout: 10000,
    withCredentials: true,
    headers: {
        'Content-Type': 'application/json'
    }
});
// Request interceptor to add auth token
api.interceptors.request.use((config)=>{
    const token = localStorage.getItem('token');
    if (token) {
        config.headers.Authorization = "Bearer ".concat(token);
    }
    return config;
}, (error)=>{
    return Promise.reject(error);
});
// Response interceptor for error handling
api.interceptors.response.use((response)=>{
    return response;
}, async (error)=>{
    var _error_response, _error_response_data, _error_response1;
    const originalRequest = error.config;
    // Only try to refresh if we have a token and this is an auth-related 401
    if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401 && !originalRequest._retry && localStorage.getItem('token')) {
        originalRequest._retry = true;
        try {
            // Try to refresh token
            const response = await api.post('/auth/refresh');
            const { token } = response.data.data;
            localStorage.setItem('token', token);
            originalRequest.headers.Authorization = "Bearer ".concat(token);
            // Update auth store with new token
            if ("object" !== 'undefined' && window.useAuthStore) {
                window.useAuthStore.getState().setToken(token);
            }
            return api(originalRequest);
        } catch (refreshError) {
            // Refresh failed, clear auth data
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            // Only redirect to login if not already on auth pages and this was a protected route
            if (!window.location.pathname.startsWith('/auth') && originalRequest.headers.Authorization) {
                window.location.href = '/auth/login';
            }
            return Promise.reject(refreshError);
        }
    }
    // Show error toast for non-401 errors
    if ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data = _error_response1.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(error.response.data.message);
    } else {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('Something went wrong. Please try again.');
    }
    return Promise.reject(error);
});
const authAPI = {
    register: (data)=>api.post('/auth/register', data),
    login: (data)=>api.post('/auth/login', data),
    logout: ()=>api.post('/auth/logout'),
    getProfile: ()=>api.get('/auth/me'),
    forgotPassword: (email)=>api.post('/auth/forgot-password', {
            email
        }),
    resetPassword: (data)=>api.put('/auth/reset-password', data),
    refreshToken: ()=>api.post('/auth/refresh')
};
const userAPI = {
    getProfile: ()=>api.get('/users/profile'),
    updateProfile: (data)=>api.put('/users/profile', data),
    addAddress: (data)=>api.post('/users/addresses', data),
    updateAddress: (id, data)=>api.put("/users/addresses/".concat(id), data),
    deleteAddress: (id)=>api.delete("/users/addresses/".concat(id)),
    getFavorites: ()=>api.get('/users/favorites'),
    addToFavorites: (restaurantId)=>api.post("/users/favorites/".concat(restaurantId)),
    removeFromFavorites: (restaurantId)=>api.delete("/users/favorites/".concat(restaurantId)),
    getOrders: (params)=>api.get('/users/orders', {
            params
        }),
    getStats: ()=>api.get('/users/stats')
};
const restaurantAPI = {
    getAll: (params)=>api.get('/restaurants', {
            params
        }),
    getById: (id)=>api.get("/restaurants/".concat(id)),
    getNearby: (lat, lng, params)=>api.get("/restaurants/nearby/".concat(lat, "/").concat(lng), {
            params
        }),
    create: (data)=>api.post('/restaurants', data),
    update: (id, data)=>api.put("/restaurants/".concat(id), data),
    delete: (id)=>api.delete("/restaurants/".concat(id)),
    toggleStatus: (id)=>api.patch("/restaurants/".concat(id, "/toggle-status"))
};
const foodAPI = {
    getAll: (params)=>api.get('/foods', {
            params
        }),
    getById: (id)=>api.get("/foods/".concat(id)),
    getByRestaurant: (restaurantId, params)=>api.get("/foods/restaurant/".concat(restaurantId), {
            params
        }),
    create: (data)=>api.post('/foods', data),
    update: (id, data)=>api.put("/foods/".concat(id), data),
    delete: (id)=>api.delete("/foods/".concat(id)),
    toggleAvailability: (id)=>api.patch("/foods/".concat(id, "/toggle-availability"))
};
const orderAPI = {
    create: (data)=>api.post('/orders', data),
    getAll: (params)=>api.get('/orders', {
            params
        }),
    getById: (id)=>api.get("/orders/".concat(id)),
    getUserOrders: (params)=>api.get('/users/orders', {
            params
        }),
    updateStatus: (id, data)=>api.patch("/orders/".concat(id, "/status"), data),
    cancel: (id, reason)=>api.patch("/orders/".concat(id, "/cancel"), {
            reason
        })
};
const paymentAPI = {
    process: (data)=>api.post('/payments/process', data),
    getMethods: ()=>api.get('/payments/methods'),
    getHistory: (params)=>api.get('/payments/history', {
            params
        }),
    refund: (data)=>api.post('/payments/refund', data)
};
const adminAPI = {
    getStats: (params)=>api.get('/admin/stats', {
            params
        }),
    getUsers: (params)=>api.get('/admin/users', {
            params
        }),
    getRestaurants: (params)=>api.get('/admin/restaurants', {
            params
        }),
    getOrders: (params)=>api.get('/admin/orders', {
            params
        }),
    updateRestaurantStatus: (id, status)=>api.patch("/admin/restaurants/".concat(id, "/status"), {
            status
        }),
    toggleUserStatus: (id)=>api.patch("/admin/users/".concat(id, "/toggle-status")),
    getAnalytics: (period)=>api.get('/admin/analytics', {
            params: {
                period
            }
        }),
    getPayments: (params)=>api.get('/admin/payments', {
            params
        }),
    getReviews: (params)=>api.get('/admin/reviews', {
            params
        }),
    getReports: (type, params)=>api.get("/admin/reports/".concat(type), {
            params
        }),
    getSettings: ()=>api.get('/admin/settings'),
    updateSettings: (data)=>api.put('/admin/settings', data)
};
const vendorAPI = {
    getDashboard: (params)=>api.get('/vendors/dashboard', {
            params
        }),
    getAnalytics: (period)=>api.get('/vendors/analytics', {
            params: {
                period
            }
        }),
    getOrders: (params)=>api.get('/vendors/orders', {
            params
        }),
    getRecentOrders: (params)=>api.get('/vendors/orders/recent', {
            params
        }),
    getFoods: (params)=>api.get('/vendors/foods', {
            params
        }),
    getRestaurant: ()=>api.get('/vendors/restaurant'),
    createRestaurant: (data)=>api.post('/vendors/restaurant', data),
    updateRestaurant: (data)=>api.put('/vendors/restaurant', data),
    createFood: (data)=>api.post('/vendors/foods', data),
    updateFood: (id, data)=>api.put("/vendors/foods/".concat(id), data),
    deleteFood: (id)=>api.delete("/vendors/foods/".concat(id)),
    updateOrderStatus: (id, status)=>api.patch("/vendors/orders/".concat(id, "/status"), {
            status
        }),
    getCustomers: (params)=>api.get('/vendors/customers', {
            params
        }),
    getReviews: (params)=>api.get('/vendors/reviews', {
            params
        }),
    getNotifications: (params)=>api.get('/vendors/notifications', {
            params
        })
};
const deliveryAPI = {
    getDashboard: ()=>api.get('/delivery/dashboard'),
    getOrders: (params)=>api.get('/delivery/orders', {
            params
        }),
    acceptOrder: (id)=>api.patch("/delivery/orders/".concat(id, "/accept")),
    pickupOrder: (id)=>api.patch("/delivery/orders/".concat(id, "/pickup")),
    deliverOrder: (id)=>api.patch("/delivery/orders/".concat(id, "/deliver")),
    getEarnings: (params)=>api.get('/delivery/earnings', {
            params
        }),
    getHistory: (params)=>api.get('/delivery/history', {
            params
        }),
    updateLocation: (data)=>api.patch('/delivery/location', data),
    updateStatus: (status)=>api.patch('/delivery/status', {
            status
        })
};
const offersAPI = {
    getOffers: (params)=>api.get('/offers', {
            params
        }),
    getOfferById: (id)=>api.get("/offers/".concat(id)),
    applyOffer: (code, orderData)=>api.post('/offers/apply', {
            code,
            ...orderData
        }),
    validateOffer: (code)=>api.post('/offers/validate', {
            code
        })
};
const notificationsAPI = {
    getNotifications: (params)=>api.get('/notifications', {
            params
        }),
    getUnreadCount: ()=>api.get('/notifications/unread-count'),
    markAsRead: (id)=>api.patch("/notifications/".concat(id, "/read")),
    markAllAsRead: ()=>api.patch('/notifications/mark-all-read'),
    deleteNotification: (id)=>api.delete("/notifications/".concat(id))
};
const __TURBOPACK__default__export__ = api;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/store/useAuthStore.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/react.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/middleware.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hot-toast/dist/index.mjs [app-client] (ecmascript)");
;
;
;
;
const useAuthStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["create"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["persist"])((set, get)=>({
        user: null,
        token: null,
        isLoading: false,
        isAuthenticated: false,
        // Login action
        login: async (credentials)=>{
            set({
                isLoading: true
            });
            try {
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authAPI"].login(credentials);
                const { user, token } = response.data.data;
                localStorage.setItem('token', token);
                localStorage.setItem('user', JSON.stringify(user));
                set({
                    user,
                    token,
                    isAuthenticated: true,
                    isLoading: false
                });
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success('Login successful!');
                return {
                    success: true,
                    user
                };
            } catch (error) {
                var _error_response_data, _error_response;
                set({
                    isLoading: false
                });
                return {
                    success: false,
                    error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Login failed'
                };
            }
        },
        // Register action
        register: async (userData)=>{
            set({
                isLoading: true
            });
            try {
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authAPI"].register(userData);
                const { user, token } = response.data.data;
                localStorage.setItem('token', token);
                localStorage.setItem('user', JSON.stringify(user));
                set({
                    user,
                    token,
                    isAuthenticated: true,
                    isLoading: false
                });
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success('Registration successful!');
                return {
                    success: true
                };
            } catch (error) {
                var _error_response_data, _error_response;
                set({
                    isLoading: false
                });
                return {
                    success: false,
                    error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Registration failed'
                };
            }
        },
        // Logout action
        logout: async ()=>{
            try {
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authAPI"].logout();
            } catch (error) {
                console.error('Logout error:', error);
            } finally{
                localStorage.removeItem('token');
                localStorage.removeItem('user');
                set({
                    user: null,
                    token: null,
                    isAuthenticated: false
                });
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success('Logged out successfully');
            }
        },
        // Get current user profile
        getProfile: async ()=>{
            try {
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authAPI"].getProfile();
                const user = response.data.data;
                set({
                    user
                });
                return user;
            } catch (error) {
                console.error('Get profile error:', error);
                return null;
            }
        },
        // Update user profile
        updateProfile: async (userData)=>{
            try {
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authAPI"].updateProfile(userData);
                const user = response.data.data;
                set({
                    user
                });
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success('Profile updated successfully');
                return {
                    success: true,
                    user
                };
            } catch (error) {
                var _error_response_data, _error_response;
                return {
                    success: false,
                    error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Update failed'
                };
            }
        },
        // Initialize auth state
        initialize: async ()=>{
            set({
                isLoading: true
            });
            try {
                const token = localStorage.getItem('token');
                if (token) {
                    set({
                        token,
                        isAuthenticated: true
                    });
                    const user = await get().getProfile();
                    if (user) {
                        set({
                            isLoading: false
                        });
                    } else {
                        // Profile fetch failed, clear auth
                        get().clearAuth();
                        set({
                            isLoading: false
                        });
                    }
                } else {
                    set({
                        isLoading: false
                    });
                }
            } catch (error) {
                console.error('Auth initialization error:', error);
                get().clearAuth();
                set({
                    isLoading: false
                });
            }
        },
        // Set token (for token refresh)
        setToken: (token)=>{
            set({
                token
            });
            localStorage.setItem('token', token);
        },
        // Initialize auth state from localStorage
        initializeAuth: ()=>{
            const token = localStorage.getItem('token');
            const storedUser = localStorage.getItem('user');
            if (token && storedUser) {
                try {
                    const user = JSON.parse(storedUser);
                    set({
                        user,
                        token,
                        isAuthenticated: true,
                        isLoading: false
                    });
                } catch (error) {
                    console.error('Error parsing stored user:', error);
                    get().clearAuth();
                }
            } else {
                set({
                    isLoading: false
                });
            }
        },
        // Clear auth state
        clearAuth: ()=>{
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            set({
                user: null,
                token: null,
                isAuthenticated: false
            });
        }
    }), {
    name: 'auth-storage',
    partialize: (state)=>({
            user: state.user,
            token: state.token,
            isAuthenticated: state.isAuthenticated
        })
}));
const __TURBOPACK__default__export__ = useAuthStore;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/hooks/useSocket.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = /*#__PURE__*/ __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$socket$2e$io$2d$client$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/socket.io-client/build/esm/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$socket$2e$io$2d$client$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/socket.io-client/build/esm/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hot-toast/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$useAuthStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/useAuthStore.js [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
;
;
;
;
const useSocket = ()=>{
    var _socketRef_current;
    _s();
    const socketRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const { user, isAuthenticated } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$useAuthStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useSocket.useEffect": ()=>{
            if (!isAuthenticated || !user) return;
            // Initialize socket connection
            socketRef.current = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$socket$2e$io$2d$client$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["io"])(("TURBOPACK compile-time value", "http://localhost:5000/api") || 'http://localhost:5000', {
                withCredentials: true,
                transports: [
                    'websocket',
                    'polling'
                ]
            });
            const socket = socketRef.current;
            // Connection events
            socket.on('connect', {
                "useSocket.useEffect": ()=>{
                    console.log('Connected to server');
                    // Join user-specific room
                    socket.emit('join-user-room', user._id);
                    // Join restaurant room if user is a vendor
                    if (user.role === 'vendor' && user.restaurant) {
                        socket.emit('join-restaurant-room', user.restaurant._id);
                    }
                }
            }["useSocket.useEffect"]);
            socket.on('disconnect', {
                "useSocket.useEffect": ()=>{
                    console.log('Disconnected from server');
                }
            }["useSocket.useEffect"]);
            // Order-related events for customers
            if (user.role === 'customer') {
                socket.on('order-status-changed', {
                    "useSocket.useEffect": (data)=>{
                        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("Order ".concat(data.status, ": ").concat(data.message));
                    // You can dispatch to a global state or trigger a refetch here
                    }
                }["useSocket.useEffect"]);
                socket.on('order-placed', {
                    "useSocket.useEffect": (data)=>{
                        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success('Order placed successfully!');
                    }
                }["useSocket.useEffect"]);
            }
            // Restaurant-related events for vendors
            if (user.role === 'vendor') {
                socket.on('new-order-received', {
                    "useSocket.useEffect": (orderData)=>{
                        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success('New order received!');
                        // Play notification sound
                        playNotificationSound();
                    }
                }["useSocket.useEffect"]);
                socket.on('payment-received', {
                    "useSocket.useEffect": (data)=>{
                        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("Payment received: ₹".concat(data.amount));
                    }
                }["useSocket.useEffect"]);
                socket.on('order-cancelled', {
                    "useSocket.useEffect": (data)=>{
                        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("Order cancelled: ".concat(data.reason));
                    }
                }["useSocket.useEffect"]);
            }
            // Admin events
            if (user.role === 'admin') {
                socket.on('new-restaurant-registration', {
                    "useSocket.useEffect": (data)=>{
                        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].info('New restaurant registration pending approval');
                    }
                }["useSocket.useEffect"]);
                socket.on('new-user-registration', {
                    "useSocket.useEffect": (data)=>{
                        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].info('New user registered');
                    }
                }["useSocket.useEffect"]);
            }
            // Cleanup on unmount
            return ({
                "useSocket.useEffect": ()=>{
                    if (socket) {
                        socket.disconnect();
                    }
                }
            })["useSocket.useEffect"];
        }
    }["useSocket.useEffect"], [
        isAuthenticated,
        user
    ]);
    // Helper function to play notification sound
    const playNotificationSound = ()=>{
        try {
            const audio = new Audio('/sounds/notification.mp3');
            audio.play().catch(console.error);
        } catch (error) {
            console.error('Error playing notification sound:', error);
        }
    };
    // Emit order status update (for vendors)
    const updateOrderStatus = (orderData)=>{
        if (socketRef.current) {
            socketRef.current.emit('order-status-update', orderData);
        }
    };
    // Emit new order (for customers)
    const emitNewOrder = (orderData)=>{
        if (socketRef.current) {
            socketRef.current.emit('new-order', orderData);
        }
    };
    return {
        socket: socketRef.current,
        updateOrderStatus,
        emitNewOrder,
        isConnected: ((_socketRef_current = socketRef.current) === null || _socketRef_current === void 0 ? void 0 : _socketRef_current.connected) || false
    };
};
_s(useSocket, "aXYxndM+Ye10/R1ISOTt6GDRjX0=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$useAuthStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
    ];
});
const __TURBOPACK__default__export__ = useSocket;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/providers.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Providers": ()=>Providers
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$core$2f$build$2f$modern$2f$queryClient$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/query-core/build/modern/queryClient.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$useAuthStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/useAuthStore.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useSocket$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/useSocket.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
;
;
;
function Providers(param) {
    let { children } = param;
    _s();
    const [queryClient] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        "Providers.useState": ()=>new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$core$2f$build$2f$modern$2f$queryClient$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QueryClient"]({
                defaultOptions: {
                    queries: {
                        staleTime: 60 * 1000,
                        retry: 1,
                        refetchOnWindowFocus: false
                    }
                }
            })
    }["Providers.useState"]);
    const initializeAuth = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$useAuthStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
        "Providers.useAuthStore[initializeAuth]": (state)=>state.initializeAuth
    }["Providers.useAuthStore[initializeAuth]"]);
    // Initialize auth state on app load
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Providers.useEffect": ()=>{
            // Make auth store globally accessible for token refresh
            if ("TURBOPACK compile-time truthy", 1) {
                window.useAuthStore = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$useAuthStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
            }
            initializeAuth();
        }
    }["Providers.useEffect"], [
        initializeAuth
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QueryClientProvider"], {
        client: queryClient,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(SocketProvider, {
            children: children
        }, void 0, false, {
            fileName: "[project]/src/app/providers.tsx",
            lineNumber: 32,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/providers.tsx",
        lineNumber: 31,
        columnNumber: 5
    }, this);
}
_s(Providers, "AZY57jNZJwB6/syzo9lFXanXe/M=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$useAuthStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
    ];
});
_c = Providers;
// Socket provider component
function SocketProvider(param) {
    let { children } = param;
    _s1();
    // Initialize socket connection
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useSocket$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: children
    }, void 0, false);
}
_s1(SocketProvider, "59v6aOh2SRVeR9sPe8btaW86J0Y=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useSocket$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
    ];
});
_c1 = SocketProvider;
var _c, _c1;
__turbopack_context__.k.register(_c, "Providers");
__turbopack_context__.k.register(_c1, "SocketProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_333adfbb._.js.map