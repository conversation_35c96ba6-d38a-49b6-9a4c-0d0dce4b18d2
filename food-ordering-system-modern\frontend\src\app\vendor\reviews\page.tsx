'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { useQuery } from '@tanstack/react-query';
import { 
  MessageCircle, 
  Star, 
  Filter,
  Search,
  ThumbsUp,
  ThumbsDown,
  Calendar,
  User
} from 'lucide-react';
import VendorSidebar from '@/components/VendorSidebar';
import { vendorAPI } from '@/lib/api';

const VendorReviews = () => {
  const [filterRating, setFilterRating] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  // Fetch reviews data
  const { data: reviewsData, isLoading } = useQuery({
    queryKey: ['vendor-reviews', filterRating],
    queryFn: () => vendorAPI.getReviews({ rating: filterRating }),
  });

  const reviews = reviewsData?.data || [];


  const ratingFilters = [
    { value: 'all', label: 'All Reviews' },
    { value: '5', label: '5 Stars' },
    { value: '4', label: '4 Stars' },
    { value: '3', label: '3 Stars' },
    { value: '2', label: '2 Stars' },
    { value: '1', label: '1 Star' },
  ];

  const filteredReviews = reviews.filter(review => {
    const matchesRating = filterRating === 'all' || review.rating.toString() === filterRating;
    const matchesSearch = review.comment.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         review.customer.name.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesRating && matchesSearch;
  });

  const averageRating = reviews.length > 0 ? reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length : 0;
  const totalReviews = reviews.length;

  const renderStars = (rating) => {
    return [...Array(5)].map((_, index) => (
      <Star
        key={index}
        className={`w-4 h-4 ${
          index < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ));
  };

  const handleReplyToReview = (reviewId) => {
    // Handle reply functionality
    console.log('Reply to review:', reviewId);
  };

  return (
    <div className="min-h-screen bg-gray-50 flex">
      <VendorSidebar />
      
      <div className="flex-1 p-8">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                <MessageCircle className="w-5 h-5 text-orange-600" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Reviews & Ratings</h1>
                <p className="text-gray-600">Manage customer feedback and responses</p>
              </div>
            </div>
          </div>

          {/* Stats Overview */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="bg-white rounded-2xl p-6 shadow-md"
            >
              <div className="flex items-center gap-3 mb-2">
                <Star className="w-6 h-6 text-yellow-400 fill-current" />
                <h3 className="text-lg font-semibold text-gray-900">Average Rating</h3>
              </div>
              <p className="text-3xl font-bold text-gray-900">{averageRating.toFixed(1)}</p>
              <p className="text-gray-600">Based on {totalReviews} reviews</p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="bg-white rounded-2xl p-6 shadow-md"
            >
              <div className="flex items-center gap-3 mb-2">
                <ThumbsUp className="w-6 h-6 text-green-500" />
                <h3 className="text-lg font-semibold text-gray-900">Positive Reviews</h3>
              </div>
              <p className="text-3xl font-bold text-gray-900">
                {reviews.filter(r => r.rating >= 4).length}
              </p>
              <p className="text-gray-600">4+ star ratings</p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="bg-white rounded-2xl p-6 shadow-md"
            >
              <div className="flex items-center gap-3 mb-2">
                <MessageCircle className="w-6 h-6 text-blue-500" />
                <h3 className="text-lg font-semibold text-gray-900">Response Rate</h3>
              </div>
              <p className="text-3xl font-bold text-gray-900">
                {totalReviews > 0 ? Math.round((reviews.filter(r => r.response).length / totalReviews) * 100) : 0}%
              </p>
              <p className="text-gray-600">Reviews responded to</p>
            </motion.div>
          </div>

          {/* Filters and Search */}
          <div className="bg-white rounded-2xl p-6 shadow-md mb-6">
            <div className="flex flex-col md:flex-row gap-4">
              {/* Search */}
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    type="text"
                    placeholder="Search reviews..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  />
                </div>
              </div>

              {/* Rating Filter */}
              <div className="flex gap-2">
                {ratingFilters.map((filter) => (
                  <button
                    key={filter.value}
                    onClick={() => setFilterRating(filter.value)}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                      filterRating === filter.value
                        ? 'bg-orange-500 text-white'
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    }`}
                  >
                    {filter.label}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Reviews List */}
          <div className="space-y-6">
            {filteredReviews.map((review, index) => (
              <motion.div
                key={review.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-white rounded-2xl p-6 shadow-md"
              >
                {/* Review Header */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                      <User className="w-5 h-5 text-gray-500" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900">{review.customer.name}</h4>
                      <div className="flex items-center gap-2">
                        <div className="flex">{renderStars(review.rating)}</div>
                        <span className="text-sm text-gray-500">•</span>
                        <span className="text-sm text-gray-500">{review.date}</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-500">
                    <ThumbsUp className="w-4 h-4" />
                    <span>{review.helpful}</span>
                  </div>
                </div>

                {/* Review Content */}
                <p className="text-gray-700 mb-4">{review.comment}</p>

                {/* Order Items */}
                <div className="mb-4">
                  <p className="text-sm text-gray-500 mb-2">Items ordered:</p>
                  <div className="flex flex-wrap gap-2">
                    {review.orderItems.map((item, idx) => (
                      <span
                        key={idx}
                        className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm"
                      >
                        {item}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Response */}
                {review.response ? (
                  <div className="bg-orange-50 border-l-4 border-orange-500 p-4 mb-4">
                    <p className="text-sm font-medium text-orange-800 mb-1">Your Response:</p>
                    <p className="text-orange-700">{review.response}</p>
                  </div>
                ) : (
                  <button
                    onClick={() => handleReplyToReview(review.id)}
                    className="text-orange-600 hover:text-orange-700 text-sm font-medium"
                  >
                    Reply to this review
                  </button>
                )}
              </motion.div>
            ))}
          </div>

          {filteredReviews.length === 0 && (
            <div className="text-center py-12">
              <MessageCircle className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No reviews found</h3>
              <p className="text-gray-600">Try adjusting your filters or search terms.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default VendorReviews;
